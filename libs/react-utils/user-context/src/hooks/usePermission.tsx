'use client';
import React from 'react';

import { SessionContext } from '@benzinga/session-context';
import { PermissionsManager, PermissionsManagerEvent } from '@benzinga/permission-manager';
import Hooks from '@benzinga/hooks';

/**
 * Hook to check if a user has a permission or not
 */
export const usePermission = (action: string, resource = '#'): boolean => {
  const session = React.useContext(SessionContext);
  const permissionsManager = session.getManager(PermissionsManager);
  const [hasAccess, setHasAccess] = React.useState(() => permissionsManager.hasAccess(action, resource).ok ?? false);

  Hooks.useSubscriber(permissionsManager, (event: PermissionsManagerEvent) => {
    switch (event.type) {
      case 'permission_changed':
        setHasAccess(permissionsManager.hasAccess(action, resource).ok ?? false);
        break;
    }
  });

  React.useEffect(() => {
    const hasPermission = permissionsManager.hasAccess(action, resource).ok ?? false;
    setHasAccess(hasPermission);
  }, [action, permissionsManager, resource]);

  return hasAccess;
};
