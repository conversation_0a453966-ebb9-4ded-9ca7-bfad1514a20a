import { useCallback, useEffect, useState, useContext } from 'react';
import { sophiManager } from '@benzinga/ads-utils';
import { SessionContext } from '@benzinga/session-context';
import { useUser } from '../hooks/useUser';

export interface SophiTrackingData {
  [key: string]: any;
  page_type: string;
  page_section: string;
}

export interface SophiDecision {
  outcome?: {
    wallVisibility?: 'always' | 'never' | string;
    wallType?: string;
  };
}

export interface UseSophiTrackingOptions {
  pageType: string;
  section?: string;
  trackingData?: Partial<SophiTrackingData>;
  isArticlePage?: boolean;
  disabled?: boolean;
}

export interface UseSophiTrackingReturn {
  sophiDecision: SophiDecision | null;
  isLoaded: boolean;
  getDecision: (userType?: 'anonymous' | 'registered') => Promise<SophiDecision>;
  trackWallHit: (wallType: 'paywall' | 'regwall', additionalData?: Record<string, any>) => void;
}

/**
 * Universal Sophi tracking hook for page view tracking and paywall decisions
 * Can be used across all page types while maintaining compatibility with article pages
 */
export const useSophiTracking = ({
  disabled = false,
  isArticlePage = false,
  pageType,
  section = 'news',
  trackingData = {},
}: UseSophiTrackingOptions): UseSophiTrackingReturn => {
  const session = useContext(SessionContext);
  const user = useUser();
  const [sophiDecision, setSophiDecision] = useState<SophiDecision | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Check if Sophi script is loaded
  useEffect(() => {
    const checkLoaded = () => {
      const loaded = sophiManager.isLoaded();
      setIsLoaded(loaded);
      if (!loaded && !disabled) {
        // Retry checking in 100ms if not loaded yet
        setTimeout(checkLoaded, 100);
      }
    };
    checkLoaded();
  }, [disabled]);

  // Handle page view tracking
  useEffect(() => {
    if (disabled || !isLoaded) return;

    console.log(`[Sophi] Page view tracking - Type: ${pageType}, Section: ${section}`);

    // Notify Sophi of page view
    sophiManager.notifyPageView(isArticlePage, section);

    // Prepare tracking data
    const baseTrackingData: SophiTrackingData = {
      page_section: section,
      page_type: pageType,
      ...trackingData,
    };

    console.log('[Sophi] Tracking page view with data:', baseTrackingData);
    sophiManager.trackWithData(session, 'page_view', baseTrackingData);
    console.log('[Sophi] Page view tracking complete');
  }, [session, pageType, section, trackingData, isArticlePage, isLoaded, disabled]);

  // Function to get paywall decision
  const getDecision = useCallback(
    async (userType?: 'anonymous' | 'registered'): Promise<SophiDecision> => {
      if (disabled || !isLoaded || !user) {
        return { outcome: { wallType: 'paywall', wallVisibility: 'never' } };
      }

      const resolvedUserType = userType || (user.accessType === 'anonymous' ? 'anonymous' : 'registered');
      console.log('[Sophi] Getting paywall decision for user type:', resolvedUserType);

      try {
        const decision = await sophiManager.getDecision(resolvedUserType);
        console.log('[Sophi] Decision received:', decision);
        setSophiDecision(decision);
        return decision;
      } catch (error) {
        console.error('[Sophi] Error getting decision:', error);
        const fallbackDecision = { outcome: { wallType: 'paywall', wallVisibility: 'never' } };
        setSophiDecision(fallbackDecision);
        return fallbackDecision;
      }
    },
    [user, isLoaded, disabled],
  );

  // Function to track wall hits
  const trackWallHit = useCallback(
    (wallType: 'paywall' | 'regwall', additionalData: Record<string, any> = {}) => {
      if (disabled || !isLoaded) return;

      console.log('[Sophi] Notifying wall encounter, type:', wallType);
      sophiManager.notifyWall(wallType, section);

      const wallHitData = {
        page_section: section,
        page_type: pageType,
        wall_type: wallType,
        ...additionalData,
      };

      console.log('[Sophi] Tracking wall hit with data:', wallHitData);
      sophiManager.trackWithData(session, 'wallhit', wallHitData);
      console.log('[Sophi] Wall hit tracking complete');
    },
    [disabled, isLoaded, section, pageType, session],
  );

  return {
    getDecision,
    isLoaded,
    sophiDecision,
    trackWallHit,
  };
};
