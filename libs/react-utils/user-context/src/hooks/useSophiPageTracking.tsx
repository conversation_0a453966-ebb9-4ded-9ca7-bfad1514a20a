import { useSophiTracking } from './useSophiTracking';
import { MetaProps } from '@benzinga/seo';
import { WordpressPage, WordpressPost } from '@benzinga/content-manager';

export interface SophiPageTrackingOptions {
  meta?: MetaProps;
  page?: WordpressPage;
  post?: WordpressPost;
  article?: any;
  disabled?: boolean;
}

/**
 * Simplified hook for page-level Sophi tracking
 * Automatically determines page type and section based on available data
 */
export const useSophiPageTracking = ({ article, disabled = false, meta, page, post }: SophiPageTrackingOptions) => {
  const getPageType = (): string => {
    if (article) return 'article';
    if (post) return 'post';
    if (page) return 'page';
    return 'website';
  };

  const getSection = (): string => {
    if (article?.channels?.[0]?.name) return article.channels[0].name;
    if (meta?.structuredData?.articleSection) return meta.structuredData.articleSection;
    return 'news';
  };

  const getTrackingData = () => {
    const baseData = {
      page_title: meta?.title || '',
      page_url: typeof window !== 'undefined' ? window.location.href : '',
    };

    if (article) {
      return {
        ...baseData,
        article_author: article.author?.name || '',
        article_id: String(article.nodeId || ''),
        article_published_date: article.createdAt,
        article_title: article.title || '',
      };
    }

    if (post) {
      return {
        ...baseData,
        post_author: post.author?.name || '',
        post_id: String(post.id || ''),
        post_title: post.title || '',
      };
    }

    if (page) {
      return {
        ...baseData,
        page_id: String(page.id || ''),
        page_title: page.title || '',
      };
    }

    return baseData;
  };

  const pageType = getPageType();
  const section = getSection();
  const trackingData = getTrackingData();

  const { getDecision, isLoaded, sophiDecision, trackWallHit } = useSophiTracking({
    disabled,
    isArticlePage: pageType === 'article',
    pageType,
    section,
    trackingData,
  });

  return {
    getDecision,
    isLoaded,
    pageType,
    section,
    sophiDecision,
    trackWallHit,
  };
};
