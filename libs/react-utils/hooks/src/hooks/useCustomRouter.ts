'use client';
import { startTransition, useCallback, useEffect, useMemo, useRef } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
// Below type should not come from dist folder.
// So moved all types to the top of the file.

// import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

export declare enum PrefetchKind {
  AUTO = 'auto',
  FULL = 'full',
  TEMPORARY = 'temporary',
}

export interface PrefetchOptions {
  kind: PrefetchKind;
}

export interface NavigateOptions {
  scroll?: boolean;
}
export interface AppRouterInstance {
  /**
   * Navigate to the previous history entry.
   */
  back(): void;
  /**
   * Navigate to the next history entry.
   */
  forward(): void;
  /**
   * Refresh the current page.
   */
  refresh(): void;
  /**
   * Navigate to the provided href.
   * Pushes a new history entry.
   */
  push(href: string, options?: NavigateOptions): void;
  /**
   * Navigate to the provided href.
   * Replaces the current history entry.
   */
  replace(href: string, options?: NavigateOptions): void;
  /**
   * Prefetch the provided href.
   */
  prefetch(href: string, options?: PrefetchOptions): void;
}

const usePrevious = <T>(value: T) => {
  const ref = useRef<T>();
  useEffect(() => {
    ref.current = value;
  });
  return ref.current;
};

interface RouterInstance extends AppRouterInstance {
  query: { [k: string]: string };
  events: {
    callbacks: Record<string, (() => void)[]>;
    off: (eventName: string, callBack: () => void) => void;
    on: (eventName: string, callBack: () => void) => void;
  };
}

export const useCustomRouter = () => {
  const pathname = usePathname();
  const prevPathname = usePrevious(pathname);

  const searchParams = useSearchParams();
  const query = useMemo(() => {
    return searchParams ? Object.fromEntries(searchParams) : {};
  }, [searchParams]);
  const prevSearchParams = usePrevious(searchParams);

  const router = useRouter() as RouterInstance;

  const events = useRef({
    callbacks: {
      routeChangeComplete: [],
      routeChangeError: [],
      routeChangeStart: [],
    } as Record<string, (() => void)[]>,
    off: (eventName: string, callBack: () => void) => {
      if (events.current.callbacks[eventName]) {
        events.current.callbacks[eventName] = events.current.callbacks[eventName].filter(cb => cb !== callBack);
      }
    },
    on: (eventName: string, callBack: () => void) => {
      events.current.callbacks[eventName] = (events.current.callbacks[eventName] || []).concat(callBack);
    },
  });

  const triggerCallbacks = useCallback((eventName: string) => {
    events.current.callbacks[eventName]?.forEach(callback => callback());
  }, []);

  router.events = events.current;
  router.query = query;

  const allowEffects = useRef(true);

  const _back = router.back;

  router.back = useCallback(() => {
    allowEffects.current = false;
    startTransition(() => {
      triggerCallbacks('routeChangeStart');
      _back();
      startTransition(() => {
        triggerCallbacks('routeChangeComplete');
        allowEffects.current = true;
      });
    });
  }, [_back, triggerCallbacks]);

  const _forward = router.forward;

  router.forward = useCallback(() => {
    allowEffects.current = false;
    startTransition(() => {
      triggerCallbacks('routeChangeStart');
      _forward();
      startTransition(() => {
        triggerCallbacks('routeChangeComplete');
        allowEffects.current = true;
      });
    });
  }, [_forward, triggerCallbacks]);

  const _refresh = router.refresh;

  router.refresh = useCallback(() => {
    allowEffects.current = false;
    startTransition(() => {
      triggerCallbacks('routeChangeStart');
      _refresh();
      startTransition(() => {
        triggerCallbacks('routeChangeComplete');
        allowEffects.current = true;
      });
    });
  }, [_refresh, triggerCallbacks]);

  const _push = router.push;

  router.push = useCallback(
    (...args) => {
      allowEffects.current = false;
      startTransition(() => {
        triggerCallbacks('routeChangeStart');
        _push(...args);
        startTransition(() => {
          triggerCallbacks('routeChangeComplete');
          allowEffects.current = true;
        });
      });
    },
    [_push, triggerCallbacks],
  ) as RouterInstance['push'];

  const _replace = router.replace;

  router.replace = useCallback(
    (...args) => {
      allowEffects.current = false;
      startTransition(() => {
        triggerCallbacks('routeChangeStart');
        _replace(...args);
        startTransition(() => {
          triggerCallbacks('routeChangeComplete');
          allowEffects.current = true;
        });
      });
    },
    [_replace, triggerCallbacks],
  ) as RouterInstance['replace'];

  const search = searchParams?.toString() || '';
  const prevSearch = prevSearchParams?.toString() || '';

  useEffect(() => {
    if (allowEffects.current && (search !== prevSearch || pathname !== prevPathname)) {
      triggerCallbacks('routeChangeComplete');
    }
  }, [pathname, prevPathname, search, prevSearch, triggerCallbacks]);

  return router;
};
