'use client';

import React from 'react';

export const useHorizontalScrollIntoView = (elementId: string) => {
  const elementRef = React.useRef<HTMLElement | null>(null);

  React.useEffect(() => {
    elementRef.current = document.getElementById(elementId);
  }, [elementId]);

  const scrollToElement = () => {
    if (elementRef.current) {
      elementRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
    }
  };

  return scrollToElement;
};

export default useHorizontalScrollIntoView;
