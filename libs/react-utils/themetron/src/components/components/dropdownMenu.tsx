'use client';
import React, { cloneElement, KeyboardEvent, ReactElement, ReactNode } from 'react';

import Hooks from '@benzinga/hooks';

import styled, { css } from '../references';
import { ChevronDown } from '../../icons';

import { Button } from './button';
import { ToolbarButton } from './toolbarButton';
import { ConstrainedFloater } from '../layout';

export interface DropdownMenuItem {
  id: string;
  value:
    | string
    | {
        render: JSX.Element;
        sortName: string;
      };
  onClick?: (item: DropdownMenuItem) => void;
  subItems?: DropdownMenuItem[];
}

interface StringButton {
  type: 'button' | 'toolbar';
  value: string | JSX.Element;
}

interface ReactElementButton {
  type: 'ReactElement';
  value: ReactElement<React.HTMLAttributes<HTMLElement>>;
}

interface DropDownOptions {
  maxWidth?: number;
  minWidth?: number | `100%`;
  parentRectClassName?: string;
  position?: 'left' | 'right';
}

export type DropDownButton = StringButton | ReactElementButton;
export type DropdownMenuItems = DropdownMenuItem[];

export const sortDropDownMenuItems = (a: DropdownMenuItem, b: DropdownMenuItem): number => {
  const aSortName = typeof a.value === 'string' ? a.value.toLowerCase() : a.value.sortName.toLowerCase();
  const bSortName = typeof b.value === 'string' ? b.value.toLowerCase() : b.value.sortName.toLowerCase();
  return aSortName.localeCompare(bSortName.toLowerCase());
};

export interface DropdownMenuProps {
  button: DropDownButton;
  className?: string;
  floaterClassName?: string;
  dropdown?: DropDownOptions;
  isOpen?: boolean;
  items: DropdownMenuItems;
  sort: boolean;
  onClose?: () => void;
  onOpen?: () => void;
}

interface State {
  dropdownSearchTerm: string;
  isOpen: boolean;
  toBeSelectedIndex: null | number;
}

export const DropdownMenu: React.FC<DropdownMenuProps> = props => {
  const dropDownRef = React.useRef<HTMLDivElement | null>(null);
  const searchTimer = React.useRef<number | null>(null);
  const buttonRef = React.useRef<HTMLDivElement | null>(null);

  const [state, setState] = React.useState<State>({
    dropdownSearchTerm: '',
    isOpen: props.isOpen ?? false,
    toBeSelectedIndex: null,
  });

  const closeDropdown = React.useCallback(() => {
    const onClose = props.onClose;
    onClose?.();
    setState(prevState => ({
      ...prevState,
      isOpen: false,
      toBeSelectedIndex: null,
    }));
  }, [props.onClose]);

  Hooks.useClickOutside(dropDownRef, closeDropdown);

  const toggleDropdown = React.useCallback(() => {
    const onClose = props.onClose;
    const onOpen = props.onOpen;
    setState(prevState => {
      if (prevState.isOpen) {
        onClose?.();
      } else {
        onOpen?.();
      }
      return { ...prevState, isOpen: !prevState.isOpen };
    });
  }, [props.onClose, props.onOpen]);

  const itemSelected = (item: DropdownMenuItem) => () => {
    if (item.onClick) {
      item.onClick(item);
    }
    setState(prevState => ({ ...prevState, isOpen: !prevState.isOpen }));
  };

  const clearSearchTerm = () => {
    searchTimer.current = window.setTimeout(() => {
      setState(prevState => ({ ...prevState, dropdownSearchTerm: '' }));
    }, 750);
  };

  React.useEffect(() => {
    if (state.dropdownSearchTerm !== '') clearSearchTerm();
  });

  const handleKeyDown = React.useCallback(
    (event: KeyboardEvent<HTMLDivElement>) => {
      event.preventDefault();

      const { items } = props;
      const { dropdownSearchTerm, isOpen, toBeSelectedIndex } = state;

      if (event.keyCode >= 48 && event.keyCode <= 90) {
        const wholeSearchTerm: string = dropdownSearchTerm.concat(event.key);

        if (searchTimer.current) {
          window.clearTimeout(searchTimer.current);
        }

        setState(prevState => ({ ...prevState, dropdownSearchTerm: wholeSearchTerm }));
        const itemNames = items.map((item, index) => ({
          name: typeof item.value === 'string' ? item.value.toLowerCase() : item.value.sortName.toLowerCase(),
          originalIndex: index,
        }));

        // It's quite likely that this portion of code is not working as intended, if at all.
        wholeSearchTerm.split('').forEach((_: string, searchLetterIndex: number) => {
          const lexicographicallySmallestItem = itemNames.reduce<{ name: string; originalIndex: number } | null>(
            (smallestItem, item) => {
              if (item.name.startsWith(wholeSearchTerm)) {
                if (smallestItem === null) {
                  smallestItem = item;
                } else if (item.name[searchLetterIndex] < smallestItem.name[searchLetterIndex]) {
                  smallestItem = item;
                }
              }
              return smallestItem;
            },
            null,
          );

          if (lexicographicallySmallestItem !== null) {
            setState(prevState => ({ ...prevState, toBeSelectedIndex: lexicographicallySmallestItem.originalIndex }));
          }
        });
        return;
      }

      switch (event.key) {
        case 'ArrowDown':
          if (!isOpen) {
            toggleDropdown();
          }
          if (toBeSelectedIndex === null) {
            setState(prevState => ({ ...prevState, toBeSelectedIndex: 0 }));
          } else {
            setState(prevState => ({ ...prevState, toBeSelectedIndex: (toBeSelectedIndex + 1) % items.length }));
          }
          break;

        case 'ArrowUp':
          if (!isOpen) {
            toggleDropdown();
          }
          if (toBeSelectedIndex === null) {
            setState(prevState => ({ ...prevState, toBeSelectedIndex: items.length - 1 }));
          } else {
            setState(prevState => ({
              ...prevState,
              toBeSelectedIndex: toBeSelectedIndex === 0 ? items.length - 1 : toBeSelectedIndex - 1,
            }));
          }
          break;

        case 'Enter':
          if (toBeSelectedIndex !== null && items && toBeSelectedIndex >= 0 && items[toBeSelectedIndex]) {
            itemSelected(items[toBeSelectedIndex]);
          }
          break;

        case 'Escape':
          closeDropdown();
          break;

        default:
          break;
      }
    },
    [closeDropdown, props, state, toggleDropdown],
  );

  const mouseOver = (toBeSelectedIndex: number) => () => {
    setState(prevState => ({ ...prevState, toBeSelectedIndex }));
  };

  const getButton = (): ReactElement => {
    switch (props.button.type) {
      case 'button':
        return (
          <Button>
            {props.button.value}
            {props.items.length > 0 && <DropdownIcon title="Open/Close the dropdown" />}
          </Button>
        );
      case 'toolbar':
        return (
          <ToolbarButton>
            {props.button.value}
            {props.items.length > 0 && <DropdownIcon title="Open/Close the dropdown" />}
          </ToolbarButton>
        );
      case 'ReactElement':
        return props.button.value;
      default:
        return <>DropDown</>;
    }
  };

  const renderItem = (item: DropdownMenuItem, index: number): ReactNode => {
    const { toBeSelectedIndex } = state;
    const { id, value } = item;

    return (
      <ListItem
        key={id}
        onClick={itemSelected(item)}
        onMouseOver={mouseOver(index)}
        selected={toBeSelectedIndex === index}
      >
        {typeof value === 'string' ? value : value.render}
      </ListItem>
    );
  };

  const renderConstrainedFloater = () => {
    const { items } = props;
    const { isOpen } = state;
    if (!isOpen) {
      return null;
    }
    const { maxWidth, minWidth, parentRectClassName, position = 'left' } = props.dropdown ?? {};
    const { bottom, left = 0, right = 0 } = buttonRef.current?.getBoundingClientRect() ?? {};

    return (
      <ConstrainedFloater
        className={props.floaterClassName}
        maxWidth={maxWidth ? maxWidth : undefined}
        minWidth={minWidth ? minWidth : right - left}
        parentRectClassName={parentRectClassName}
        positionLeft={position === 'left' ? left : undefined}
        positionRight={
          position === 'right'
            ? ((parentRectClassName
                ? buttonRef.current?.closest(`.${parentRectClassName}`)?.getBoundingClientRect().width
                : window.innerWidth) ?? window.innerWidth) - right
            : undefined
        }
        positionTop={bottom ?? undefined}
      >
        <UnorderedList>{(props.sort ? items.sort(sortDropDownMenuItems) : items).map(renderItem)}</UnorderedList>
      </ConstrainedFloater>
    );
  };

  const button = cloneElement(getButton(), {
    onClick: toggleDropdown,
    ref: buttonRef,
  });

  return (
    <DropdownMenuDiv className={props.className} onKeyDown={handleKeyDown} ref={dropDownRef} tabIndex={0}>
      {button}
      {renderConstrainedFloater()}
    </DropdownMenuDiv>
  );
};

interface DropdownCSS {
  css?: typeof css;
}

const DropdownMenuDiv = styled.div<DropdownCSS>`
  display: flex;
  position: relative;
  outline: 0px;
  outline-offset: 0px;
`;

const UnorderedList = styled.ul`
  color: ${props => props.theme.colors.foreground};
  background: ${props => props.theme.colors.background};
  margin: 0;
  padding: 0;
  // display: none;
  display: block;
  line-height: 2em;
  z-index: 100; // 100 index to cover calendar scrollbar
`;

interface selectedItem {
  selected: boolean;
}

const ListItem = styled.li<selectedItem>`
  align-items: center;
  background-color: ${props => (props.selected ? props.theme.colors.backgroundActive : props.theme.colors.background)};
  box-sizing: border-box;
  cursor: pointer;
  font-size: 1em;
  padding: 0 1rem;
  list-style: none;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  justify-content: space-between;
  overflow: hidden;
  max-width: 100%;

  :last-child {
    border-bottom: none;
  }
`;

export type SVGPropType = React.SVGProps<SVGSVGElement> & {
  title?: string | undefined;
  size?: string;
};

const DropdownIcon = styled(ChevronDown as React.FC<SVGPropType>)`
  margin-left: 6px;
  flex-shrink: 0;
`;

export default DropdownMenu;
