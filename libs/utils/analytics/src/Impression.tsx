'use client';
import React from 'react';
import VizSensor from 'react-visibility-sensor';
import ImpressionTag from './ImpressionTag';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { runningClientSide } from '@benzinga/utils';

export interface ImpressionProps {
  campaign_id: string;
  unit_type: string;
  tag?: string;
  impressProperties?: Record<string, string>;
}

export const Impression: React.FC<React.PropsWithChildren<ImpressionProps>> = ({
  campaign_id,
  children,
  impressProperties,
  tag,
  unit_type,
}) => {
  const session = React.useContext(SessionContext);
  const [didImpress, setDidImpress] = React.useState(false);

  const handleOnChange = (isVisible: boolean) => {
    if (isVisible && !didImpress) {
      setDidImpress(true);
      if (campaign_id && unit_type) {
        session.getManager(TrackingManager).trackCampaignEvent('view', {
          additionalProperties: impressProperties,
          campaign_id: campaign_id,
          non_interaction: true,
          tag: tag,
          unit_type: unit_type,
        });
      }
    }
  };

  const handleClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    // Check if the clicked element is a link
    const target = event.target as HTMLElement;
    const link = target.closest('a') as HTMLAnchorElement | null;
    const button = target.closest('button') as HTMLButtonElement | null;

    console.log('link target:', link);
    console.log('button target:', button);

    if (link) {
      if (link.href) {
        session.getManager(TrackingManager).trackCampaignEvent('click', {
          campaign_id: campaign_id,
          tag: tag,
          unit_type: unit_type,
          url: link.href,
        });
      }
      // Additional logic can go here, such as navigation or tracking
    } else if (button) {
      session.getManager(TrackingManager).trackCampaignEvent('click', {
        campaign_id: campaign_id,
        tag: tag,
        unit_type: unit_type,
      });
    }
  };

  return (
    <VizSensor onChange={handleOnChange}>
      <div onClick={handleClick}>
        {didImpress && tag && <ImpressionTag src={tag} />}
        {children}
      </div>
    </VizSensor>
  );
};
