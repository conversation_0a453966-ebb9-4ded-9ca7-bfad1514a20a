'use client';
import React, { useEffect, useCallback } from 'react';
import { useCookies } from 'react-cookie';
import { DateTime } from 'luxon';
import { useEffectDidMount, useDebounce, useCustomRouter } from '@benzinga/hooks';
import { useUserSubscriptions, useUser } from '@benzinga/user-context';
import { MetaProps } from '@benzinga/seo';
import VisibilitySensor from 'react-visibility-sensor';
import { getDocumentReferrer, setNewDocumentReferrer } from './utils';
import { ContentManager, isContentHasReachImpressionLimit } from '@benzinga/content-manager';
import { SessionContext, SessionPropsExtension } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { CoralogixRum } from '@coralogix/browser';

const RouteChangeListenerAppRouter = React.lazy(() =>
  import('./RouteChangeListenerAppRouter').then(module => {
    return { default: module.RouteChangeListenerAppRouter };
  }),
);

const RouteChangeListenerPageRouter = React.lazy(() =>
  import('./RouteChangeListenerPageRouter').then(module => {
    return { default: module.RouteChangeListenerPageRouter };
  }),
);

interface Props {
  enableCoralogixRum?: boolean;
  routerType?: 'page' | 'app';
}

// This is the way we did it in Drupal.  Should we still?  IDK.  We can also skip this or lave..... and just rely on the DataLake to solve problems.
export const getAgeOfArticleByCreatedDate = (dateCreated?: string) => {
  let ageOfArticle: number | null = null;

  if (dateCreated) {
    const today = Date.now() / 1000;
    const timestamp = DateTime.fromISO(dateCreated).toMillis() / 1000;
    const numberOfDays = (today - timestamp) / (60 * 60 * 24);

    // If the number of days is greater than 2000, set the age as 10,000.
    if (numberOfDays > 2000) {
      ageOfArticle = 10000;
    } else {
      // Otherwise check for the range.
      const rangeArray = [0, 1, 2, 3, 5, 7, 10, 15, 20, 30, 60, 90, 180, 365, 730, 1000, 2000];
      for (const i in rangeArray) {
        if (numberOfDays <= rangeArray[i] && numberOfDays > rangeArray[parseInt(i) - 1]) {
          return (ageOfArticle = rangeArray[i]);
        }
      }
    }
  }

  return ageOfArticle;
};

export const clearUTMKeys = (): string => {
  const params = new URLSearchParams(window.location.search);

  if (params) {
    const keys = Array.from(Object(params).keys()) as string[];
    if (keys.length) {
      keys.forEach(key => {
        if (key.includes('utm_')) {
          params.delete(key);
        }
      });

      const cleanedParams = params.toString();

      const query = cleanedParams ? '?' + cleanedParams : '';
      const cleanUrl = window.location.pathname + query + window.location.hash;

      if (cleanUrl) {
        window.history.replaceState(null, '', cleanUrl);

        return cleanUrl;
      }
    }
  }

  return window.location.href;
};

export const runTrackPage = (session?: SessionPropsExtension) => {
  const pageParams = {
    path: window.location.pathname,
  };

  const referrer = getDocumentReferrer();
  if (referrer) {
    pageParams['referrer'] = referrer;
  }

  session?.getManager(TrackingManager).trackPageEvent('view', {});
  return true;
};

export const PageTracker: React.FC<Props> = ({ enableCoralogixRum, routerType = 'page' }) => {
  const session: SessionPropsExtension = React.useContext(SessionContext);
  const [cookies] = useCookies(['has_bz_account_internal', 'bz_edit']);
  const router = useCustomRouter();
  const user = useUser();
  const subscriptions = useUserSubscriptions();

  const debounceIdentify = useDebounce(
    React.useCallback(() => {
      if (user && user.accessType !== 'anonymous') {
        const { benzingaUid, displayName: username, email, firstName, lastName, phoneNumber, profileData } = user;

        const userData = {
          active_monthly_price: subscriptions?.reduce((acc, sub) => {
            return sub.status === 'active' && sub.interval === 'month' ? acc + parseInt(sub.finalPrice || '0') : acc;
          }, 0),
          active_one_time_price: subscriptions?.reduce((acc, sub) => {
            return sub.status === 'active' && sub.interval === 'onetime' ? acc + parseInt(sub.finalPrice || '0') : acc;
          }, 0),
          active_plan_ids:
            subscriptions
              ?.filter(sub => sub.status === 'active')
              .map(sub => sub.basePlan)
              .join(', ') || undefined,
          active_subscriptions: subscriptions?.filter(sub => sub.status === 'active').length || 0,
          active_yearly_price: subscriptions?.reduce((acc, sub) => {
            return sub.status === 'active' && sub.interval === 'year' ? acc + parseInt(sub.finalPrice || '0') : acc;
          }, 0),
          email,
          email_verified: user.emailVerified || false,
          globalAccountType: profileData?.globalAccountType ?? undefined,
          logged_in: user.email ? true : false,
          name: [firstName || '', lastName || ''].join(' '),
          phone: phoneNumber || '',
          sms_verified: user.smsVerified || false,
          total_subscriptions: subscriptions?.length || 0,
          user_id: benzingaUid,
          username,
        };

        session.getManager(TrackingManager).identify(benzingaUid, userData);
        return true;
      } else {
        return false;
      }
    }, [session, user, subscriptions]),
    1000,
  );

  // Don't run analytics if user is a staff member
  const isAnalyticsShouldRun = useCallback(() => {
    return window?.location?.pathname && !cookies.has_bz_account_internal && !cookies.bz_edit;
  }, [cookies]);

  const runAnalytics = useCallback(() => {
    if (isAnalyticsShouldRun()) {
      runTrackPage(session);
    }
  }, [session, isAnalyticsShouldRun]);

  useEffectDidMount(() => {
    runAnalytics();
  });

  useEffect(() => {
    if (user) {
      debounceIdentify();

      if (enableCoralogixRum) {
        CoralogixRum.setUserContext({
          user_email: user.email,
          user_id: String(user.benzingaUid),
          user_metadata: {
            access_type: user.accessType,
          },
          user_name: user.displayName,
        });
      }
    }
  }, [enableCoralogixRum, debounceIdentify, user]);

  const handleRouteChange = useCallback(() => {
    clearUTMKeys();
    runAnalytics();
  }, [runAnalytics]);

  // Detect route change and run analytics
  useEffect(() => {
    const handleRouteChange = () => {
      clearUTMKeys();
      runAnalytics();
    };

    router.events.on('routeChangeComplete', handleRouteChange);
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router.events, runAnalytics]);

  return routerType === 'app' ? (
    <RouteChangeListenerAppRouter onRouteChange={handleRouteChange} />
  ) : (
    <RouteChangeListenerPageRouter onRouteChangeComplete={handleRouteChange} />
  );
};

interface VisiblePageTrackerProps {
  article: any;
  executePageviewEvent?: boolean; // used to prevent article tracking triggering more than once
  executeReplaceHistory?: boolean; // used to prevent article url history from updating
  executeImpressionEvent?: boolean;
  isSponsored?: boolean; // modifies google event
  meta?: MetaProps;
  onImpress?: () => void;
  trackMeta?: boolean;
}

export const VisiblePageTracker: React.FC<React.PropsWithChildren<VisiblePageTrackerProps>> = ({
  article,
  children,
  executeImpressionEvent = true,
  executePageviewEvent = true,
  executeReplaceHistory = true,
  meta,
  onImpress,
  trackMeta = true,
}) => {
  const session: SessionPropsExtension = React.useContext(SessionContext);
  const [didImpress, setDidImpress] = React.useState(false);

  const handleIsVisible = React.useCallback(
    (isVisible: boolean) => {
      if (isVisible && executeReplaceHistory) {
        const path = `/${article.canonicalPath}${window.location.search}`;
        if (article.canonicalPath && path !== window.location.pathname) {
          clearUTMKeys();
          setNewDocumentReferrer(window.location.href);
          window.history.replaceState({}, article.title, path);
        }

        const event = new CustomEvent('page-transition', { detail: { meta, trackMeta } });
        window.dispatchEvent(event);
      }

      if (isVisible && !didImpress && executeImpressionEvent) {
        session.getManager(TrackingManager).trackCampaignEvent('view', {
          partner_id: article.id + '-' + article.id,
          sponsored: article.isSponsored,
          unit_type: `visible-page-tracker-${article.id}`,
        });

        if (isContentHasReachImpressionLimit(article?.meta)) {
          session
            .getManager(ContentManager)
            .registerContentImpression(article.nodeId)
            .catch(error => console.log('Register Content Impression Error:', error));
        }

        // gtag('event', 'search', {
        //   'search_term': query
        // });

        // 5 second delay to run impression/page track, also confirming that the current article is still in view
        setTimeout(() => {
          if (window.location.pathname === `/${article.canonicalPath}`) {
            setDidImpress(true);

            if (onImpress) {
              onImpress();
            }
          }
        }, 50);
      }
    },
    [
      executeImpressionEvent,
      executeReplaceHistory,
      didImpress,
      article.canonicalPath,
      article.title,
      article.isSponsored,
      meta,
      onImpress,
      trackMeta,
      article.id,
      article.meta,
      article.nodeId,
      session,
    ],
  );

  return (
    <VisibilitySensor onChange={handleIsVisible}>
      <>
        {didImpress && executePageviewEvent && <PageTracker />}
        {children}
      </>
    </VisibilitySensor>
  );
};
