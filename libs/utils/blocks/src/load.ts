import { Block, ContentManager, Entity } from '@benzinga/content-manager';
import {
  BasicNewsManager,
  ChartbeatParams,
  NodeQueryParams,
  type ParselyPostParams,
  formatToSimpleNewsQueryParams,
} from '@benzinga/basic-news-manager';
import { MortgageRatesManager } from '@benzinga/mortgage-rates-manager';
import { safeAwait, safeTimeout } from '@benzinga/safe-await';
import { Session } from '@benzinga/session';
import { EventsManager } from '@benzinga/events-manager';
import { TradeIdeaManager } from '@benzinga/trade-ideas-manager';
import { CommoditiesManager } from '@benzinga/commodities-manager';
import { removeKeysWithEmptyStringValues, formatImageUrl } from '@benzinga/utils';
import { getDeviceInfoFromRequestHeaders } from '@benzinga/device-utils';
import { QuotesManager } from '@benzinga/quotes-manager';
import { CryptoManager } from '@benzinga/crypto-manager';
import { getAllMovers } from '@benzinga/movers-manager';
import { CalendarManager, Dividend } from '@benzinga/calendar-manager';
import type { IncomingHttpHeaders } from 'http';
import { getMoneyArticleByVertical } from './utils';
import { PlusManager } from '@benzinga/plus-manager';
import { QuoteProtos, ScannerManager } from '@benzinga/scanner-manager';
import { getRankingTableConfig, RankingColumn } from './rankingsUtils';

export const loadServerSideBlockData = async (
  session: Session,
  blocks: Block[],
  headers?: IncomingHttpHeaders,
  cookies?: Partial<{
    [key: string]: string;
  }>,
): Promise<Block[]> => {
  const basicNewsManager = session.getManager(BasicNewsManager);
  return await Promise.all(
    blocks.map(async (block: any) => {
      const deviceInfo = headers ? getDeviceInfoFromRequestHeaders(headers, cookies) : null;

      if (deviceInfo) {
        block.deviceType = deviceInfo.deviceType;
        block.userAgent = deviceInfo.userAgent;
      }

      if (block.blockName === 'acf/reviewed-by-benzinga') {
        block.attrs.data.image_src = block.attrs.data?.image?.url;
      }
      if (block.blockName === 'acf/news-list-with-pagination') {
        const res = await basicNewsManager.simplyQueryNews(
          {
            channels: block.attrs.data.primary_channel,
          },
          { limit: Number(block.attrs.data.total_items) },
        );
        if (Array.isArray(res?.ok)) {
          block.attrs.data.nodes = res.ok;
        }
      }
      if (block.blockName === 'acf/news-with-tabs') {
        const tabs = Array.isArray(block.attrs.data.article_tabs) ? block.attrs.data.article_tabs : [];
        block.attrs.data.article_tabs = await Promise.all(
          tabs.map(async (tab: any) => {
            const tab_content = tab.tab_content;
            if (tab_content.news_list && tab_content.news_list.primary_vertical_id) {
              tab_content.news_list.nodes = await getMoneyArticleByVertical(
                tab_content.limit,
                session,
                tab_content.news_list.primary_vertical_id,
              );
            }

            if (tab_content.articles_list) {
              const articles_list = Array.isArray(tab_content.articles_list) ? tab_content.articles_list : [];
              tab_content.articles_list = await Promise.all(
                articles_list.map(async (item: any) => {
                  if (item.vertical_id) {
                    item.nodes = await getMoneyArticleByVertical(tab_content.limit, session, item.vertical_id);
                  }
                  return item;
                }),
              );
            }
            return tab;
          }),
        );
      }
      if (block.blockName === 'acf/news-list-group') {
        const groups = block?.attrs?.data?.groups;
        if (groups) {
          block.attrs.data.groups = await Promise.all(
            groups.map(async (group: any) => {
              if (group?.type === 'parsely_news_list') {
                const chartbeatReq = await basicNewsManager.getTopPostsUsingChartbeat(group.parsely_query);
                group.nodes = chartbeatReq.ok ?? [];
              } else {
                if (group?.query_and_options) {
                  const query = group?.query_and_options?.query;
                  const options = group?.query_and_options?.options;
                  const res = await basicNewsManager.simplyQueryNews(query, options);
                  if (Array.isArray(res?.ok)) {
                    group.nodes = res.ok;
                  }
                }
              }
              return group;
            }),
          );
        }
      }
      if (block.blockName === 'acf/news-feed') {
        const query: NodeQueryParams = {
          channels: block.attrs?.data?.channels,
          pageSize: block?.attrs?.data?.posts_limit,
          topics: block.attrs?.data?.topics,
          type: block?.attrs?.data?.feed_type,
        };
        const nodesRes = await safeTimeout(basicNewsManager.fetchNodes(query), 3000);
        if (Array.isArray(nodesRes?.ok)) {
          block.attrs.data.news = nodesRes.ok;
        }
      }
      if (block.blockName === 'acf/content-feed') {
        const query = block?.attrs?.data?.contentFeedProps?.query;

        if (block?.attrs?.data?.contentFeedProps?.fetchTradeAlertTickers) {
          const plusManager = session.getManager(PlusManager);
          const response = await plusManager.getProductData('trade-alerts');
          if (response?.ok) {
            const tradeAlertsDataRes = response.ok;
            const symbols: string[] = [];

            tradeAlertsDataRes?.table_rows?.forEach(item => {
              symbols.push(item?.row_data[0]);
            });
            query.tickers = symbols;
          }
        }
        const queryParams = formatToSimpleNewsQueryParams(query);
        if (queryParams) {
          const res = await safeTimeout(
            session.getManager(BasicNewsManager).simplyQueryNews(queryParams.query, queryParams.options),
            3000,
          );
          if (Array.isArray(res?.ok) && block.attrs?.data?.contentFeedProps) {
            block.attrs.data.contentFeedProps.nodes = res.ok;
          }
        }
      }
      if (block.blockName === 'acf/link-group') {
        const query = block?.attrs?.data?.query as ChartbeatParams;
        if (query) {
          const chartbeatRes = await basicNewsManager.getTopPostsUsingChartbeat(query);
          if (Array.isArray(chartbeatRes?.ok)) {
            const formattedNewsToLinks = chartbeatRes.ok.map(newsItem => {
              return {
                list_icon: 'fa-file-alt',
                title: newsItem.title,
                url: newsItem.url,
              };
            });
            block.attrs.data.links = formattedNewsToLinks;
          }
        }
      }
      if (block.blockName === 'acf/mortgage-loan-compare') {
        const mortgageRateTrendsRes = await session.getManager(MortgageRatesManager).getMortgageRateTrends();
        block.rateData = mortgageRateTrendsRes.ok ?? null;
      }
      if (block.blockName === 'acf/covey-analyst-metrics') {
        const getCoveyMetricsData = async () => {
          return fetch('https://api.covey.io/metrics/partner-data?partner_id=88')
            .then(res => res.json())
            .then(data => data);
        };
        const data = await safeAwait(getCoveyMetricsData());
        block.attrs.data.metricsData = data.ok ?? null;
      }
      if (block.blockName === 'acf/yield-table') {
        const contentManager = session.getManager(ContentManager);
        const productResponse = await safeTimeout(
          contentManager.getProduct({
            vertical: 'yield-investments' as unknown as number,
          }),
          3000,
        );
        block.attrs.data.yieldTableData = productResponse.ok ?? null;
      }
      if (block.blockName === 'acf/carousel-news') {
        const query = block?.attrs?.data?.query_and_options?.query;
        const options = block?.attrs?.data?.query_and_options?.options;
        if (query || options) {
          const result = await basicNewsManager.simplyQueryNews(query, options);
          if (Array.isArray(result.ok)) {
            const formattedItems = result.ok.map(item => ({
              href: item.url?.replace('https://www.benzinga.com', ''),
              id: item.id ?? null,
              image: formatImageUrl(item) || block?.attrs?.data?.default_image || null,
              isBzPost: item.isBzPost ?? null,
              isBzProPost: item.isBzProPost ?? null,
              title: item.title ?? null,
            }));
            block.attrs.data.initialItems = formattedItems;
          }
        }
      }
      if (block.blockName === 'acf/carousel-events') {
        const res = await safeTimeout(session.getManager(EventsManager).getEvents(), 3000);
        if (Array.isArray(res?.ok)) {
          const formattedItems = res.ok.map(item => ({
            href: item.acf.learn_more,
            id: item.id,
            image: item.acf.marketing_image ?? null,
            subtitle: item.acf.date_and_location,
            title: item.title.rendered,
          }));
          block.attrs.data.initialItems = formattedItems;
        }
      }
      if (block.blockName === 'acf/news-grid') {
        const shouldUseQuery = block?.attrs?.data?.news_selection_mode === 'query';
        if (shouldUseQuery) {
          const query = block?.attrs?.data?.query_and_options?.query;
          const options = block?.attrs?.data?.query_and_options?.options;
          const result = await basicNewsManager.simplyQueryNews(query, options);
          block.attrs.data.nodes = result;
        }
      }
      if (block.blockName === 'acf/news-featured-section') {
        const shouldUseQuery = block?.attrs?.data?.news_selection_mode === 'query';
        if (shouldUseQuery) {
          const query = block?.attrs?.data?.query_and_options?.query;
          const options = block?.attrs?.data?.query_and_options?.options;
          if (query || options) {
            const res = await basicNewsManager.simplyQueryNews(query, options);
            if (Array.isArray(res?.ok)) {
              block.featuredNews = res.ok;
            }
          }
        }
      }
      if (block.blockName === 'acf/money-posts') {
        const query = block?.attrs?.data?.query;
        const res = await safeTimeout(session.getManager(ContentManager).getPosts(query), 3000);
        if (res?.ok) {
          block.attrs.data.posts = res.ok;
        }
      }
      if (block.blockName === 'acf/podcasts-list') {
        const query = block?.attrs?.data?.query;
        const res = await safeTimeout(session.getManager(ContentManager).getPodcastEpisodes(query), 3000);
        if (res?.ok) {
          block.podcasts = res.ok;
        }
      }
      if (block.blockName === 'acf/benzinga-briefs') {
        const isServerSide = block?.attrs?.data?.server_side;
        if (isServerSide) {
          const limit = block?.attrs?.data?.query?.limit;
          const res = await safeTimeout(
            basicNewsManager.simplyQueryNews(
              { tags: [799703] },
              {
                displayOutput: 'full',
                limit: limit || 5,
              },
            ),
            3000,
          );
          if (Array.isArray(res?.ok)) {
            block.initialBriefs = res.ok;
          }
        }
      }
      if (block.blockName === 'acf/stock-quotes') {
        const symbols: string[] = block?.tickers || block?.attrs?.data?.tickers;
        const quotesManager = session.getManager(QuotesManager);

        if (Array.isArray(symbols) && symbols.length > 0) {
          const response = await quotesManager.getDelayedQuotes(symbols);
          block.attrs.data.initialQuotes = response?.ok ?? null;
        }
      }
      if (block.blockName === 'acf/stock-movers') {
        const query = block?.attrs?.data?.query;
        if (query) {
          const movers = await getAllMovers(session, query);
          if (movers) {
            block.attrs.data.initialMovers = movers;
          }
        }
      }
      if (block.blockName === 'acf/entities') {
        if (Array.isArray(block?.entities)) {
          const symbols: string[] = block.entities.map((entity: Entity) => entity?.ticker).filter(Boolean);
          const quotesManager = session.getManager(QuotesManager);
          if (Array.isArray(symbols) && symbols.length > 0) {
            const response = await quotesManager.getDelayedQuotes(symbols);
            block.initialQuotes = response?.ok ?? null;
          }
        }
      }
      if (block.blockName === 'acf/coin-exchange-rate') {
        const exchange_rates = await safeTimeout(session.getManager(ContentManager).getExchangeRate(), 3000);
        if (exchange_rates?.ok) {
          block.attrs.data.exchange_rates = exchange_rates?.ok;
        }
      }
      if (block.blockName === 'acf/trade-ideas-widget') {
        const query = removeKeysWithEmptyStringValues(block?.attrs?.data?.query);
        const feed = session.getManager(TradeIdeaManager).createFeed(query);
        const tradeIdeas = await feed.loadTradeIdeas();
        block.items = tradeIdeas?.ok ?? [];
      }
      if (block.blockName === 'acf/future-commoditites') {
        const category = block?.attrs?.data?.commoditites_category ?? 'Forex';
        const commodititesManager = session.getManager(CommoditiesManager);
        const futures = await commodititesManager.getCommodities(category);
        block.attrs.data.futures = futures?.ok ?? [];
      }
      const blocksThatCanContainOtherBlocks = ['acf/widget-container'];
      if (blocksThatCanContainOtherBlocks.includes(block.blockName) && Array.isArray(block.blocks)) {
        block.blocks = await loadServerSideBlockData(session, block.blocks, headers, cookies);
        block.dynamic = false;
      }
      if (block.blockName === 'acf/compare-products-side-by-side') {
        block.attrs.data.enable_product_selection = true;
        if (block.attrs.data?.enable_user_selection === true) {
          let isFilterEnabled = false;
          const activeFilters: string[] = [];
          const contentManager = session.getManager(ContentManager);

          if (block.attrs.data?.filters) {
            const filters = block.attrs.data?.filters;
            Object.keys(filters).forEach(function (key) {
              if (filters[key] === true) {
                isFilterEnabled = true;
                activeFilters.push(key);
              }
            });
          }
          block.attrs.data.active_filters = activeFilters;
          if (isFilterEnabled) {
            block.attrs.data.enable_product_selection = false;
            const response = await contentManager.getProductFilters(activeFilters.join(','));
            if (response?.ok) {
              block.attrs.data.filter_options = response?.ok;
            }
          } else {
            const response = await contentManager.getProduct({
              country: block?.attrs?.data?.pre_filter?.country ?? '',
              vertical: block.attrs.data?.vertical[0],
            });
            if (response?.ok) {
              block.attrs.data.compare_products = response?.ok;
            }

            const fieldResponse = await contentManager.getCustomFieldsLabel({
              vertical: block.attrs.data?.vertical[0],
            });
            if (response?.ok) {
              block.attrs.data.compare_custom_fields = fieldResponse?.ok;
            }
          }
        }
      }
      if ((block.blockName === 'acf/cryptocurrency-card' || block.blockName === 'acf/quote-card') && block.coin) {
        const coinSymbol = block.coin.coingecko_coin_id ?? block.coin.coin_symbol;
        const partnerLink = block.attrs.data.partner_link;
        block.attrs.data.partnerLink = partnerLink;
        if (coinSymbol) {
          const cryptoManager = session.getManager(CryptoManager);
          const res = await cryptoManager.getCoin(coinSymbol);
          if (res?.ok) {
            block.attrs.data.coin_data = res.ok;
            if (partnerLink === '') {
              const symbol = res.ok.symbol;
              if (symbol) {
                const articleResponse = await cryptoManager.getHowToBuyArticle(symbol);
                if (articleResponse?.ok) {
                  if (articleResponse?.ok?.status !== 'missing_how_to_buy') {
                    if (
                      articleResponse.ok?.[0] &&
                      articleResponse.ok[0]?.canonical &&
                      articleResponse.ok[0].canonical === window.location.href
                    ) {
                      block.attrs.data.partnerLink = partnerLink;
                    } else {
                      block.attrs.data.partnerLink =
                        articleResponse.ok && articleResponse.ok[0] ? articleResponse.ok[0].url : partnerLink;
                    }
                  }
                }
              }
            }
            if (res.ok.coingecko_id) {
              const resVotes = await cryptoManager.getNumberOfVotes(res.ok.coingecko_id);
              if (resVotes?.ok?.num_votes) {
                block.attrs.data.num_votes = resVotes.ok.num_votes;
              }
            }
          }
        }
      }
      if (block.blockName === 'acf/stocks-list-container') {
        let symbols = block?.attrs?.data?.tickers;
        const layout = block?.attrs?.data?.layout;

        if (layout == 'portfolio_dividend') {
          const calendarManager = session.getManager(CalendarManager);

          const diviendData = await calendarManager.getCalendarData(
            'dividends',
            {
              pageSize: 1000,
              symbols: symbols,
            },
            true,
          );
          if (diviendData?.ok) {
            const processedQuote: string[] = [];
            const quoteDividendData: Dividend[] = [];
            diviendData.ok.forEach(item => {
              if (item?.ticker && !processedQuote.includes(item.ticker) && item?.dividend_yield != '') {
                processedQuote.push(item.ticker);
                quoteDividendData.push(item);
              }
            });

            block.attrs.data.initialDividends = quoteDividendData;
          }
        }

        if (layout == 'portfolio_alert') {
          const plusManager = session.getManager(PlusManager);
          const response = await plusManager.getProductData('trade-alerts');
          if (response?.ok) {
            const tradeAlertsDataRes = response.ok;
            symbols = [];

            tradeAlertsDataRes?.table_rows?.forEach(item => {
              symbols.push(item?.row_data[0]);
            });

            let tradeAlertsData = {};

            tradeAlertsDataRes?.table_rows?.forEach(item => {
              tradeAlertsData = Object.assign({}, tradeAlertsData, {
                [item?.row_data[0]]: {
                  date_added: item?.row_data[2],
                  entry_price: item?.row_data[5],
                  exit_date: item?.row_data[6],
                  exit_price: Number(item?.row_data[7]) > 0 ? item?.row_data[7] : 0,
                  limit_price: item?.row_data[4],
                  portfolio: item?.row_data[3],
                  symbol: item?.row_data[0],
                },
              });
              symbols.push(item?.row_data[0]);
            });

            block.attrs.data.tickers = symbols;
            block.attrs.data.tradeAlertsData = tradeAlertsData;

            const videosResponse = await plusManager.getProductVideos('trade-alerts');
            if (videosResponse?.ok && videosResponse.ok?.videos.length > 0) {
              block.attrs.data.tradeAlertVideos = videosResponse.ok;
            }
          }
        }

        if (Array.isArray(symbols) && symbols.length) {
          const quotesManager = session.getManager(QuotesManager);

          const response = await quotesManager.getDelayedQuotes(symbols);
          if (response?.ok) {
            const _quotes = Object.values(response?.ok).filter(quote => quote?.symbol);
            block.attrs.data.initialQuotes = _quotes;
            const _symbols = _quotes.map(function (d) {
              return d?.symbol;
            });
            block.attrs.data.tickers = _symbols;
          }
        }

        if (layout === 'rankings') {
          const slug = block?.attrs?.data?.ticker_group.slug;
          const data = getRankingTableConfig(slug);
          if (!data.scanner_query.fields) {
            block.attrs.data.rankingTable = {
              columns: [],
              query: {},
              rankings: [],
            };
            return block;
          }

          const response = await session.getManager(ScannerManager).getInstrumentsWithIQuery(data.scanner_query);
          const rankings =
            response?.ok?.instruments?.map((ranking): QuoteProtos.IQuote => {
              const obj = ranking instanceof Object ? { ...ranking } : ranking;
              // if the ranking includes a field with format longPrice, convert it to a number
              data.table_columns.forEach((column: RankingColumn) => {
                if (column.format === 'longPrice' && obj[column.field]) {
                  obj[column.field] = Number(obj[column.field]);
                }
              });
              Object.keys(obj).forEach(key => {
                if (obj[key] && typeof obj[key] === 'object') {
                  obj[key] = { ...obj[key] };
                }
              });
              return obj;
            }) ?? [];

          if (rankings) {
            block.attrs.data.rankingTable = {
              columns: data.table_columns,
              query: data.scanner_query,
              rankings: rankings,
            };

            const tickers = rankings.map((ranking: QuoteProtos.IQuote) => ranking.symbol);
            block.attrs.data.tickers = tickers;
          }
        }
      }
      return block;
    }),
  );
};
