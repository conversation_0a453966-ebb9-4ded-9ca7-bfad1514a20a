import { PremiumIdeaRequestParams } from './entities';
import { TradeIdeaRequest } from './request';
// import { SafePromise } from '@benzinga/safe-await';

export interface ExpertsArrayProps {
  locked: boolean;
  name: string;
  product_id: string;
  package_ids: number[];
  profileUrl: string;
}

export class PremiumIdeaFeed {
  request: TradeIdeaRequest;
  params: PremiumIdeaRequestParams = {};
  limit = 7;
  numNewIdeas = 0;
  tableType = 'option';

  constructor(params: PremiumIdeaRequestParams, request: TradeIdeaRequest, tableType: string) {
    this.params = params;
    if (params.limit) {
      this.limit = params.limit;
    }
    this.request = request;
    this.tableType = tableType;
  }

  public setParams = (params: PremiumIdeaRequestParams): void => {
    this.params = params;
    if (params.limit) {
      this.limit = params.limit;
    }
  };

  public loadMorePremiumIdeas = async (expertProductId: string, premium_Ideas: any, premium_Trades: any) => {
    const pageNo = premium_Ideas.next ? premium_Ideas.next.pageNumber : null;
    if (pageNo === null) {
      return { ok: { premiumIdeas: premium_Ideas, premiumTrades: premium_Trades } };
    }
    const limit = premium_Ideas.next.limit;
    this.params = {
      ...this.params,
      limit: limit,
      pageNumber: pageNo,
    };
    const premiumIdeas = await this.request.getMorePremiumIdeas(this.params, expertProductId);
    if (premiumIdeas.ok && premiumIdeas.ok.trades.length) {
      const trades = premiumIdeas.ok.trades.filter((item: { tableType: string }) =>
        this.tableType.includes(item.tableType),
      );
      if (this.tableType.includes('option') && trades.length) {
        return {
          ok: {
            premiumIdeas: trades[0],
            premiumTrades: premium_Trades.concat(trades[0].trades),
          },
        };
      } else if (this.tableType.includes('stock') && trades.length) {
        if (trades.length > 1) {
          const combinedIdeas = trades[0].trades.concat(trades[1].trades);
          return {
            ok: {
              premiumIdeas: trades[0],
              premiumTrades: premium_Trades.concat(combinedIdeas),
            },
          };
        } else {
          return {
            ok: {
              premiumIdeas: trades[0],
              premiumTrades: premium_Trades.concat(trades[0].trades),
            },
          };
        }
      } else {
        return {
          ok: {
            premiumIdeas: premium_Ideas,
            premiumTrades: premium_Trades,
          },
        };
      }
    } else {
      console.log('Premium ideas', premiumIdeas);
      return {
        ok: { premiumIdeas: {}, premiumTrades: [] },
      };
    }
  };

  public loadPremiumIdeas = async (expertProductId: string) => {
    const premiumIdeas = await this.request.getPremiumIdeas(this.params, expertProductId);
    if (premiumIdeas.ok && premiumIdeas.ok.trades.length) {
      const trades = premiumIdeas.ok.trades.filter((item: { tableType: string }) => {
        return this.tableType.includes(item.tableType);
      });
      if (this.tableType.includes('option') && trades.length) {
        return {
          ok: { premiumIdeas: trades[0], premiumTrades: trades[0].trades },
        };
      } else if (this.tableType.includes('stock') && trades.length) {
        if (trades.length > 1) {
          return {
            ok: {
              premiumIdeas: trades[0],
              premiumTrades: trades[0].trades.concat(trades[1].trades),
            },
          };
        } else {
          return {
            ok: {
              premiumIdeas: trades[0],
              premiumTrades: trades[0].trades,
            },
          };
        }
      } else {
        return {
          ok: {
            premiumIdeas: {},
            premiumTrades: [],
          },
        };
      }
    } else {
      console.log('Premium ideas', premiumIdeas);
      return {
        ok: { premiumIdeas: {}, premiumTrades: [] },
      };
    }
  };

  public getAllPremiumIdeas = async (premium_Ideas: any, premium_Trades: any, loadMore = false) => {
    const pageNo = premium_Ideas?.next ? premium_Ideas.next.pageNumber : null;
    if (pageNo === null && loadMore) {
      return { ok: { premiumIdeas: premium_Ideas, premiumTrades: premium_Trades } };
    }
    let premiumIdeas;
    if (premium_Ideas.next) {
      const limit = premium_Ideas.next.limit;
      this.params = {
        ...this.params,
        limit: limit,
        pageNumber: pageNo,
      };
      premiumIdeas = await this.request.getAllPremiumIdeas(this.params);
      if (premiumIdeas.ok && premiumIdeas.ok.trades.length) {
        const trades = premiumIdeas.ok.trades.filter((item: { tableType: string }) =>
          this.tableType.includes(item.tableType),
        );
        return {
          ok: {
            premiumIdeas: trades[0],
            premiumTrades: premium_Trades.concat(trades[0]?.trades ?? []),
          },
        };
      } else {
        console.log('Premium ideas', premiumIdeas);
        return {
          ok: { premiumIdeas: {}, premiumTrades: [] },
        };
      }
    } else {
      premiumIdeas = await this.request.getAllPremiumIdeas(this.params);
      if (premiumIdeas.ok && premiumIdeas.ok.trades.length) {
        const trades = premiumIdeas.ok.trades.filter((item: { tableType: string }) => {
          return this.tableType.includes(item.tableType);
        });
        return {
          ok: { premiumIdeas: trades[0], premiumTrades: trades[0]?.trades ?? [] },
        };
      } else {
        console.log('Premium ideas', premiumIdeas);
        return {
          ok: { premiumIdeas: {}, premiumTrades: [] },
        };
      }
    }
  };

  public getExpertsArray = async () => {
    const experts: any = await this.request.getExpertsArray();
    const expertArray: ExpertsArrayProps[] = [];
    if (experts.ok) {
      experts.ok.products.map((product: any) => {
        const instructorName = product.instructor.name;
        const expertDetails = {
          locked: product.locked,
          name: instructorName,
          package_ids: product.package_ids,
          product_id: product.product_id,
          profileUrl: product.icon.image_url,
        };

        expertArray.push(expertDetails);
      });
    }
    return expertArray;
  };
}
