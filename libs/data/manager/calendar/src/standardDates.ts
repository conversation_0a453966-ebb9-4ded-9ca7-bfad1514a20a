import { DateTime } from '@benzinga/session';
import {
  CalendarType,
  DateDaysOffset,
  DateRange,
  DayOffset,
  DefaultCalendarDate,
  CalendarDataParameters,
} from './entities';

import { dateDefinitions } from './definitions';
import dayjs, { Dayjs } from 'dayjs';

export type DateRating =
  | 'this-week'
  | 'this-month'
  | 'last-month'
  | 'trailing-7-day'
  | 'trailing-30-day'
  | 'week-to-date'
  | 'today'
  | 'clear'
  | 'month-to-date'
  | 'last-3-months'
  | 'last-1-year'
  | null;

export enum DateFormat {
  full = 'YYYY-MM-DD',
  pretty = 'MMM Y',
  small = 'YYYY',
}

const getDateRange = (range: DateRating) => {
  let start: Dayjs | Date | null = null;
  let end: Dayjs | Date | null = null;

  const todayMoment = dayjs();

  switch (range) {
    case 'this-week':
      start = dayjs().startOf('week');
      end = dayjs().endOf('week');
      break;

    case 'trailing-7-day':
      start = dayjs().subtract(7, 'days');
      end = todayMoment;
      break;

    case 'week-to-date' || 'wtd':
      start = dayjs().startOf('week');
      end = todayMoment;
      break;

    case 'this-month':
      start = dayjs().startOf('month');
      end = dayjs().endOf('month');
      break;

    case 'trailing-30-day':
      start = dayjs().subtract(30, 'days');
      end = todayMoment;
      break;

    case 'month-to-date' || 'mtd':
      start = dayjs().subtract(30, 'days');
      end = todayMoment;
      break;

    case 'last-month':
      start = dayjs().subtract(1, 'months').startOf('month');
      end = dayjs().subtract(1, 'months').endOf('month');
      break;

    case 'last-3-months':
      start = dayjs().subtract(3, 'months');
      end = todayMoment;
      break;

    case 'last-1-year':
      start = dayjs().subtract(1, 'year');
      end = todayMoment;
      break;

    case 'today':
    case 'clear':
    default:
      start = todayMoment;
      end = todayMoment;
      break;
  }

  return [start, end];
};

export const calendarGetDefaultDates = (calendarType: CalendarType, hasSomethingSearched: boolean): DateRange => {
  const definition = dateDefinitions[calendarType];
  if (definition && definition.defaultDates) {
    if (hasSomethingSearched) {
      return transformOffsetsToDates(definition.defaultDates.somethingSearched);
    }
    return transformOffsetsToDates(definition.defaultDates.nothingSearched);
  }
  return {
    dateEnd: null,
    dateStart: null,
  };
};

const convertToDate = (daysOffset: DayOffset): DefaultCalendarDate => {
  if (daysOffset === undefined || daysOffset === null) {
    return null;
  }
  return DateTime.dateNow().plus({ days: daysOffset }).toISODateString();
};

const transformOffsetsToDates = ({ dateEndDaysOffset, dateStartDaysOffset }: DateDaysOffset): DateRange => ({
  dateEnd: convertToDate(dateEndDaysOffset),
  dateStart: convertToDate(dateStartDaysOffset),
});

const defaultDates = (parameters: CalendarDataParameters) => {
  if (parameters.rangeType) {
    const str = parameters.rangeType.replace(/\s+/g, '-').toLowerCase() as DateRating;
    const dates = getDateRange(str);
    const dateStart = dates[0] && dates[0].format(DateFormat.full);
    const dateEnd = dates[1] && dates[1].format(DateFormat.full);
    parameters.dateStart = dateStart;
    parameters.dateEnd = dateEnd;
  } else {
    const hasSomethingSearched = !!(parameters.watchlistIds?.length || parameters.filters?.length);
    const calendarDefaultDates = calendarGetDefaultDates(parameters.calendarType, hasSomethingSearched);
    parameters.dateStart = calendarDefaultDates.dateStart;
    parameters.dateEnd = calendarDefaultDates.dateEnd;
  }

  return parameters;
};

export const standardDates = (inputParameters: CalendarDataParameters): CalendarDataParameters => {
  const parameters = { ...inputParameters };
  const defaultDatesForParameters = defaultDates(parameters);
  const customDatesAreSelected =
    defaultDatesForParameters.dateStart !== parameters.dateStart ||
    defaultDatesForParameters.dateEnd !== parameters.dateEnd;

  if (customDatesAreSelected) {
    const { dateEnd, dateStart } = defaultDatesForParameters;
    return { ...parameters, dateEnd, dateStart };
  }
  return parameters;
};
