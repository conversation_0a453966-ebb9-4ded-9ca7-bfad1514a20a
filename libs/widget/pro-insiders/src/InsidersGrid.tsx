'use client';
import { Grid<PERSON><PERSON>, ColDef, GridOptions, RowDoubleClickedEvent } from '@ag-grid-community/core';
import { BenzingaGrid, gridOptions, SendLinkContext } from '@benzinga/pro-ui';
import React, { useMemo, useRef } from 'react';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { InsidersFiling } from '@benzinga/insider-trades-manager';
import { useColumnDef } from './gridColumnDef';
import { PresetConfig } from './preset_configs';
import { getTimeDisplayFormat } from '@benzinga/date-utils';
import { useTime } from '@benzinga/time-manager-hooks';
import { DateTime } from 'luxon';
import { SessionContext } from '@benzinga/session-context';
import { LoggingManager } from '@benzinga/session';

export interface ColumnConfig {
  name: string;
  width?: number;
}

interface Props {
  gridLayout: TableParameters;
  onGridLayoutChanged: (parameters: Partial<TableParameters>) => void;
  onRowDoubleClicked: (filing: InsidersFiling) => void;
  rowData: InsidersFiling[];
  config?: PresetConfig;
}

export const InsidersGrid: React.FC<Props> = props => {
  const ref = useRef<HTMLDivElement>(null);
  const gridApi = useRef<GridApi | null>(null);
  const exportParams = useMemo(
    () => ({
      filename: props.config?.title ?? 'insider',
    }),
    [props.config?.title],
  );

  const columnDef = useColumnDef();
  const time = useTime();
  const session = React.useContext(SessionContext);
  const defaultColDef = React.useMemo<ColDef>(
    () => ({
      cellClass: 'u-alignRight',
      headerClass: ['u-alignLeft'],
      minWidth: 100,
      onCellClicked: cell => {
        session.getManager(LoggingManager).log('debug', { category: 'insider', message: `${cell}` }, ['console']);
      },
      resizable: true,
      sortable: true,
    }),
    [session],
  );

  const myGridOptions = React.useMemo<GridOptions>(() => {
    const onRowDoubleClicked = props.onRowDoubleClicked;
    return {
      defaultColDef: {
        cellClass: 'u-alignRight',
        filter: 'number',
        filterParams: {},
        headerClass: ['u-alignLeft'],
        minWidth: 100,
        onCellClicked: cell => {
          session.getManager(LoggingManager).log('debug', { category: 'insider', message: `${cell}` }, ['console']);
        },
        resizable: true,
        sortable: true,
      },
      enableRangeSelection: true,
      getRowId: props => props.data.company_ticker + props.data.last_filing_date,
      headerHeight: 30,
      immutableData: true,
      onGridReady: params => {
        if (params) {
          gridApi.current = params.api;
        }
      },
      onRowDoubleClicked: (event: RowDoubleClickedEvent) => {
        onRowDoubleClicked(event.data);
      },
      processCellForClipboard: params => {
        if (params.column.getColId() === 'last_filing_date') {
          const format = getTimeDisplayFormat({ timeFormat: time.timeFormat });
          const icoTime = DateTime.fromISO(params.value)
            .plus({ minutes: time.timeOffset })
            .toFormat(`yyyy-MM-dd ${format}`);
          return icoTime !== 'Invalid DateTime' ? icoTime : params.value;
        }
        return params.value;
      },
      rowHeight: 20,
      rowSelection: 'multiple' as GridOptions['rowSelection'],
      sideBar: gridOptions.sideBar,
      statusBar: gridOptions.statusBar,
    };
  }, [props.onRowDoubleClicked, session, time.timeFormat, time.timeOffset]);

  const sendLink = React.useContext(SendLinkContext);

  return (
    <div ref={ref} style={{ height: '100%' }}>
      <BenzingaGrid
        columnDefs={columnDef}
        defaultColDef={defaultColDef}
        exportParams={exportParams}
        gridLayout={props.gridLayout}
        gridOptions={myGridOptions}
        onGridLayoutChanged={props.onGridLayoutChanged}
        onSymbolClick={sendLink.onSymbolClick}
        rowClass="TUTORIAL_ag_header_Peers"
        rowData={props.rowData}
        suppressRowTransform
        symbolColIDs={['company_ticker']}
        tooltipShowDelay={0}
      />
    </div>
  );
};

export default InsidersGrid;
