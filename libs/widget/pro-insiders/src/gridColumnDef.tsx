import { cellClassRules, ChangePercentColDef, DateColDef, SymbolColDef } from '@benzinga/pro-ui';
import { CellClassRules, ColDef, ColGroupDef, ValueFormatterParams } from '@ag-grid-community/core';
import { useTime } from '@benzinga/time-manager-hooks';
import React from 'react';

const valueCompare = (valueA: any, valueB: any) => {
  return (valueA || -9999999999) - (valueB || -9999999999);
};

const formatLargeNumber = (value: number | undefined): string => {
  if (!value || value === 0) {
    return '-';
  } // terminate early

  const b = value.toPrecision(2).split('e'); // get power
  const k = b.length === 1 ? 0 : Math.floor(Math.min(parseFloat(b[1].slice(1)), 14) / 3); // floor at decimals, ceiling at trillions
  const c = k < 1 ? value.toFixed(2) : (value / Math.pow(10, k * 3)).toFixed(2); // divide by power
  const f = parseFloat(c);
  const d = f < 0 ? f : Math.abs(f); // enforce -0 is 0
  const e = d + ['', 'K', 'M', 'B', 'T'][k]; // append power
  return e;
};

const shortFormatter = ({ value }: ValueFormatterParams) => {
  return isNaN(value) ? '-' : formatLargeNumber(value);
};

export const useColumnDef = (): (ColDef | ColGroupDef)[] => {
  const time = useTime();
  const timeInfo = React.useMemo(
    () => ({ timeFormat: time.timeFormat, timeOffset: time.timeOffset }),
    [time.timeFormat, time.timeOffset],
  );
  return React.useMemo<(ColDef | ColGroupDef)[]>(
    () => [
      SymbolColDef({
        field: 'company_ticker',
        headerName: 'Ticker',
        minWidth: 100,
      }),
      {
        cellClass: ['u-alignLeft'],
        field: 'company_name',
        filter: 'agTextColumnFilter',
        filterParams: {
          caseSensitive: false,
        },
        headerName: 'Company Name',
        minWidth: 100,
      },
      {
        cellClass: ['u-alignLeft'],
        cellClassRules: cellClassRules.upDownColorState,
        field: 'trade_types',
        filter: 'agSetColumnFilter',
        headerName: 'Trade Type',
        headerTooltip: 'A breakdown of the type of transaction this filing reported',
      },
      ChangePercentColDef({
        field: 'traded_percentage_string',
        headerName: 'Δ Own',
        headerTooltip: 'The percentage change in shares held',
        minWidth: 100,
      }),
      {
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        comparator: valueCompare,
        field: 'traded_shares',
        filter: 'number',
        filterParams: {
          defaultOption: 'greaterThan',
        },
        headerName: 'Quantity',
        headerTooltip: 'The total shares acquired/disposed',
        minWidth: 100,
        valueFormatter: shortFormatter,
      },
      {
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        comparator: valueCompare,
        field: 'traded_share_price',
        filter: 'number',
        filterParams: {
          defaultOption: 'greaterThan',
        },
        headerName: 'Price',
        headerTooltip: 'The average price of acquired/disposed shares',
        minWidth: 100,
        valueFormatter: ({ value }: ValueFormatterParams) => (isNaN(value) ? '-' : '$' + value.toFixed(2)),
      },
      {
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        comparator: valueCompare,
        field: 'traded_value',
        filter: 'number',
        filterParams: {
          defaultOption: 'greaterThan',
        },
        headerName: 'Size ($)',
        headerTooltip: 'The total dollar value of shares traded',
        minWidth: 100,
        valueFormatter: shortFormatter,
      },
      {
        cellClass: ['u-alignLeft'],
        field: 'insider_names',
        filter: 'agTextColumnFilter',
        headerName: 'Insider Names',
        headerTooltip: 'All the names of insiders included in this filing',
        minWidth: 100,
      },
      {
        cellClass: ['u-alignLeft'],
        field: 'insider_titles',
        filter: 'agSetColumnFilter',
        headerName: 'Title',
        headerTooltip: 'All the titles for insiders included in this filing',
        minWidth: 100,
      },
      {
        cellClassRules: cellClassRules.upDownColorState as CellClassRules,
        comparator: valueCompare,
        field: 'owned',
        filter: 'number',
        filterParams: {
          defaultOption: 'greaterThan',
        },
        headerName: 'Owned Shares',
        headerTooltip: 'Remaining shares still owned',
        minWidth: 100,
        valueFormatter: shortFormatter,
      },
      DateColDef(
        {
          cellClass: ['u-alignLeft'],
          field: 'last_filing_date',
          headerName: 'Filing Date',
          headerTooltip: 'The date on which this was filed',
          minWidth: 100,
        },
        timeInfo,
      ),
    ],
    [timeInfo],
  );
};
