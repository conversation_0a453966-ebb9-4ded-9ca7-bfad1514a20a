import React from 'react';
import { getCalendarColDefs } from '../calendarDefinitions';
import { ColDef } from '@ag-grid-community/core';
import { useTime } from '@benzinga/time-manager-hooks';
import { useLayoutFill, useQuoteColDef } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { CalendarType } from '@benzinga/calendar-manager-hooks';

export const useColDef = (table: TableParameters, calendarType: CalendarType) => {
  const { timeFormat, timeOffsetFromEastern: timeOffset } = useTime();
  const columnLayout = useLayoutFill(table.columns ?? []);
  const quoteFieldsDefs = useQuoteColDef(columnLayout);
  const bzColumnStateConfig = React.useMemo(
    () =>
      table.bzColumnState.reduce(
        (acc, column) => {
          acc[column.colId] = column;
          return acc;
        },
        {} as Record<string, ColDef>,
      ),
    [table.bzColumnState],
  );
  return React.useMemo(() => {
    const calendarDefinitions = getCalendarColDefs(calendarType, {
      timeFormat,
      timeOffset,
    }).map(category => {
      if (category.headerName?.toLowerCase() === calendarType.toLowerCase() && 'children' in category) {
        return {
          ...category,
          children: category.children.map(col => {
            const field = 'field' in col ? col.field : undefined;
            const bzColumnState = field ? bzColumnStateConfig[field] : undefined;
            return {
              ...col,
              ...bzColumnState,
            };
          }),
        };
      }
      return category;
    });

    return [
      ...calendarDefinitions.reduce((defs, colDef) => {
        if (colDef && Object.keys(colDef).length) {
          // const tooltipGetter = (params: ITooltipParams): string => {
          //   const text = params.valueFormatted || params.value;
          //   const cellWidth = params.column?.getActualWidth() || 0;
          //   return isTextOverflowingAgGridCell(text, cellWidth) ? text : null;
          // };
          // const tooltipValueGetter = colDef.tooltipValueGetter ?? ((!colDef.cellRenderer && tooltipGetter) || undefined);

          const headerTooltip = colDef.headerTooltip ?? colDef.headerName ?? undefined;

          return [
            ...defs,
            {
              ...colDef,
              headerTooltip,
              // tooltipValueGetter,
            },
          ];
        }
        return defs;
      }, [] as ColDef[]),
      ...quoteFieldsDefs,
    ];
  }, [bzColumnStateConfig, calendarType, quoteFieldsDefs, timeFormat, timeOffset]);
};
