'use client';
import React, { CSSProperties, MutableRefObject, useState } from 'react';
import { sanitizeUrl } from '@braintree/sanitize-url';
import styled, { css } from '@benzinga/themetron';
import { ImageModal } from './ImageModal';

export type GalleryProps = {
  images: ({
    image_url?: string | undefined;
    thumb_url?: string | undefined;
  } & { previewUrl?: string; style?: CSSProperties })[];
  innerRefs?: MutableRefObject<(HTMLElement | null)[]>;
};

const UnMemoizedGallery = (props: GalleryProps) => {
  const { images, innerRefs } = props;

  const [index, setIndex] = useState(0);
  const [modalOpen, setModalOpen] = useState(false);

  const countImagesDisplayedInPreview = 4;
  const lastImageIndexInPreview = countImagesDisplayedInPreview - 1;

  const keypressDivRef = React.useRef<HTMLDivElement>(null);

  const toggleModal = (selectedIndex: number) => {
    if (modalOpen) {
      setModalOpen(false);
    } else {
      setIndex(selectedIndex);
      setModalOpen(true);
    }
  };

  const renderImages = images.slice(0, countImagesDisplayedInPreview).map((image, i) =>
    i === lastImageIndexInPreview && images.length > countImagesDisplayedInPreview ? (
      <ImgThumbnailButton
        key={`gallery-image-${i}`}
        onClick={() => toggleModal(i)}
        style={{
          backgroundImage: `url(${
            images[lastImageIndexInPreview].previewUrl ||
            images[lastImageIndexInPreview].image_url ||
            images[lastImageIndexInPreview].thumb_url
          })`,
          ...image.style,
        }}
        {...(innerRefs?.current && { ref: r => (innerRefs.current[i] = r) })}
      >
        <ImgThumbnailMore>
          <p>{`${images.length - countImagesDisplayedInPreview + 1} more`}</p>
        </ImgThumbnailMore>
      </ImgThumbnailButton>
    ) : (
      <ImgThumbnailButton key={`gallery-image-${i}`} onClick={() => toggleModal(i)}>
        <ImgThumbnail
          alt="User uploaded content"
          src={sanitizeUrl(image.previewUrl || image.image_url || image.thumb_url)}
          style={image.style}
          {...(innerRefs?.current && { ref: r => (innerRefs.current[i] = r) })}
        />
      </ImgThumbnailButton>
    ),
  );

  React.useEffect(() => {
    if (modalOpen) {
      setTimeout(() => keypressDivRef.current?.focus(), 0);
    }
  }, [modalOpen]);

  return (
    <GalleryContainer imgCount={images.length}>
      {renderImages}
      <ImageModal
        images={images}
        index={index}
        modalOpen={modalOpen}
        ref={keypressDivRef}
        setIndex={setIndex}
        setModalOpen={setModalOpen}
      />
    </GalleryContainer>
  );
};

/**
 * Displays images in a simple responsive grid with a light box to view the images.
 */
export const Gallery = React.memo(UnMemoizedGallery) as typeof UnMemoizedGallery;

const GalleryContainer = styled.div<{ imgCount: number }>`
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: ${props => (props.imgCount > 2 ? css`1fr 1fr` : css`1fr`)};
  grid-gap: 2px;
  aspect-ratio: 1;
  overflow: hidden;
  max-width: 30vw;
  max-height: 30vh;
`;

const ImgThumbnailButton = styled.button`
  width: 100%;
  height: 100%;
  overflow: hidden;
`;
const ImgThumbnailMore = styled.button`
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
`;

const ImgThumbnail = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
`;
