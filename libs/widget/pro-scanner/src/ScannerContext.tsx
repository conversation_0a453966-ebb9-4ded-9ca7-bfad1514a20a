'use client';
import React from 'react';
import { Scanne<PERSON><PERSON><PERSON>, <PERSON>anner<PERSON>anager } from '@benzinga/scanner-manager';
import { DEFAULT_SCANNER_CONFIG, ScannerConfig } from '@benzinga/scanner-config-manager';
import { SessionContext } from '@benzinga/session-context';
import { useScannerFilterableDefs } from '@benzinga/scanner-manager-hooks';
import { useAutoCompleteSymbols } from '@benzinga/pro-ui';

export const ScannerFeedContext = React.createContext<ScannerFeed | undefined>(undefined);

export const ScannerFeedContextProvider: React.FC<React.PropsWithChildren<{ feed: ScannerFeed }>> = props => {
  return <ScannerFeedContext.Provider value={props.feed}> {props.children} </ScannerFeedContext.Provider>;
};

export const useScannerFeed = (userConfig: ScannerConfig | undefined) => {
  const session = React.useContext(SessionContext);
  const filtersDef = useScannerFilterableDefs();
  const symbols = useAutoCompleteSymbols(userConfig?.tags ?? []);

  const mappedConfig = React.useMemo<ScannerConfig>(
    () =>
      userConfig
        ? {
            ...userConfig,
            // add symbols to the filters
            filters: [...userConfig.filters, { field: 'symbol', operator: 'in', parameters: symbols ?? [] }].map(
              filter =>
                filtersDef?.find(def => def.name === filter.field)?.filterConvert?.(filter, session.getSession()) ??
                filter,
            ),
            tableParameters: {
              ...userConfig.tableParameters,
              columns: userConfig.tableParameters.columns.filter(col => !col.hide),
            },
          }
        : DEFAULT_SCANNER_CONFIG,
    [userConfig, symbols, filtersDef, session],
  );
  return session.getManager(ScannerManager).getFeed(mappedConfig);
};

export const useScannerFeedContext = () => {
  const session = React.useContext(SessionContext);
  return React.useContext(ScannerFeedContext) ?? session.getManager(ScannerManager).getFeed(DEFAULT_SCANNER_CONFIG);
};
