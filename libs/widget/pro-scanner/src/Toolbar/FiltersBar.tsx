'use client';
import React from 'react';
import styled, { css } from '@benzinga/themetron';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';

import { FiltersBarSummary } from './FiltersBarSummary';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons/faChevronDown';
import { faChevronRight } from '@fortawesome/pro-solid-svg-icons/faChevronRight';
import { Icon } from '@benzinga/core-ui';
import { FiltersPanel as ScannerFilterPanel } from '../FiltersPanel';
import { useWidgetParameters } from '@benzinga/widget-tools';
import { ScannerWidgetManifest } from '../widget';
import { arrayDifference } from '@benzinga/utils';
import { useScannerColumnableDefs } from '@benzinga/scanner-manager-hooks';

// For each filter options, make a set of parameter so that we can quickly check if a range
// is custom - and thus show a select box vs custom

export const FiltersBar: React.FC<{
  isScreener?: boolean;
}> = React.memo(({ isScreener }) => {
  const [selectedFilters, setSelectedFilters] = React.useState<FilterObject[]>([]);
  const widgetParams = useWidgetParameters(ScannerWidgetManifest);
  const config = widgetParams.parameters.config;
  const collapsed = widgetParams.parameters.filtersMenuCollapsed;
  const setCollapsed = widgetParams.setter.filtersMenuCollapsed;
  const setConfig = widgetParams.setter.config;
  const colDefs = useScannerColumnableDefs();

  const onFiltersChanged = React.useCallback(
    (filters: FilterObject[]) => {
      setConfig(old => {
        if (old.filters.length < filters.length) {
          const newItems = arrayDifference(
            filters.map(f => f.field),
            old.filters.map(f => f.field),
          );
          if (newItems.length > 0 && colDefs?.some(d => d.name === newItems[0])) {
            if (!old.tableParameters.columns.some(c => c.colId === newItems[0] && c.hide === false)) {
              return {
                ...old,
                filters,
                tableParameters: {
                  ...old.tableParameters,
                  columns: [
                    ...old.tableParameters.columns.filter(c => c.colId !== newItems[0]),
                    { colId: newItems[0], hide: false },
                  ],
                },
              };
            }
          }
        }
        return { ...old, filters };
      });
    },
    [colDefs, setConfig],
  );

  // Called when a filter is change from the filters bar, or child filter
  const updateFilters = React.useCallback(
    (filters: FilterObject[]) => {
      onFiltersChanged(filters);
    },
    [onFiltersChanged],
  );

  React.useEffect(() => {
    const newFilters = config.filters.map(filter => {
      if (filter.field === 'symbol' && filter.parameters[0]?.startsWith('watchlistId:')) {
        filter = { field: 'watchlist', operator: 'in', parameters: filter.parameters };
      }
      return filter;
    });
    setSelectedFilters(newFilters);
    // setColumnedFilters(config.columns);
  }, [config.tableParameters.columns, config.filters]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <Div isOpen={!collapsed} isScreener={isScreener}>
      <FiltersSummary isScreener={isScreener} onClick={toggleCollapsed}>
        <Arrow>
          <StyledIcon className="filter_summary" icon={collapsed ? faChevronRight : faChevronDown} />
          <StyledFilterTest>Filters:</StyledFilterTest>
        </Arrow>
        <FiltersBarSummary filters={widgetParams.parameters.config.filters} />
      </FiltersSummary>
      <StyledFilterHolder collapsed={collapsed}>
        <StyledScannerFilterPanel
          isScreener={isScreener}
          onFiltersChanged={updateFilters}
          selectedFilters={selectedFilters}
        />
      </StyledFilterHolder>
    </Div>
  );
});

const Div = styled.div<{ isOpen?: boolean; isScrollable?: boolean; isScreener?: boolean }>`
  ${props =>
    props.isOpen
      ? css`
          color: ${props => props.theme.colors.foreground};
          border: 1px solid ${props => props.theme.colors.brandMuted};
          background-color: ${props => props.theme.colors.backgroundActive}88;
          ${FiltersSummary} {
            background: ${props => props.theme.colors.brandMuted}44;
          }
        `
      : css`
          border: 1px solid ${props => props.theme.colors.background};
          border-left: 2px solid ${props => props.theme.colors.brandMuted};
          background-color: ${props => props.theme.colors.backgroundActive}88;

          :hover {
            color: ${props => props.theme.colors.brand};
            background: ${props => props.theme.colors.backgroundActive}88;
          }
        `}
  /* max-height: 100%; */
  // overflow: hidden;
  ${props =>
    props.isScreener
      ? css`
          width: 100% !important;
          max-height: 50%;
          border: 1px solid ${props => props.theme.colors.brandMuted};
        `
      : css``}
  display: flex;
  flex-direction: column;

  .ant-tabs-content {
    flex-wrap: wrap !important;
    max-height: 200px !important;
    overflow: hidden !important;
  }

  .ant-tabs-tabpane {
    overflow: hidden !important;
  }

  .ant-tabs-content {
    overflow: auto !important;
  }
  .ant-tabs-tabpane {
    overflow: auto !important;
  }
`;

const FiltersSummary = styled.div<{ isScreener?: boolean }>`
  display: flex;
  align-items: center;
  .clear-filters {
    font-weight: bold;
    margin-right: 10px;
    margin-left: 10px;
    border: 1px solid;
    padding: 4px;
    margin-top: -3px;
  }
  &:hover {
    cursor: pointer;
  }
  ${props =>
    props.isScreener
      ? css`
          color: #283d59;
        `
      : css``}
`;

const Arrow = styled.div`
  margin-top: auto;
  line-height: 26px;
  white-space: nowrap;
  margin-left: 6px;
  display: flex;
  svg {
    font-size: 10px;
  }
`;

const StyledIcon = styled(Icon)`
  margin-right: 4px;
  font-size: 12px !important;
`;

const StyledFilterTest = styled.span`
  margin-right: 4px;
`;

const StyledFilterHolder = styled.div<{ collapsed: boolean }>`
  display: ${props => (props.collapsed ? 'none' : 'block')};
  flex: 1;
  overflow: hidden;
`;

const StyledScannerFilterPanel = styled(ScannerFilterPanel)`
  height: 100%;
  padding: 12px;
  min-width: fit-content;
  width: 100%;
  overflow: hidden;
`;

export default FiltersBar;
