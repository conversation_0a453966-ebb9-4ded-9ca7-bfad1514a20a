'use client';
import React, { useEffect } from 'react';
import styled, { TC, css } from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { Input, Radio, RadioChangeEvent, Slider } from 'antd';
import { FilterObject, QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';
import { WidgetContext } from '@benzinga/widget-tools';

import { isEmpty } from '@benzinga/utils';
import { findNearestDot, fromShorthand, paramToValue, toShorthand } from '../utils';
const { Button: RadioButton, Group: RadioGroup } = Radio;
const { Group: InputGroup } = Input;

/**
 * If options is supplied, then a select is shown if the current value is either: 1) undefined, or 2) matches
 * the (human format) value.
 */

export const RangeInput: React.FC<{
  filter: FilterObject;
  onChange: (filter: FilterObject) => void;
  onOpenChanged?: (isOpen: boolean) => void;
  isScreener?: boolean;
}> = props => {
  const session = React.useContext(SessionContext);
  const enhancedFilters = React.useMemo(() => {
    if (props.filter.operator === 'lt') {
      return { ...props.filter, operator: 'lt', parameters: ['', props?.filter?.parameters?.[0]] };
    } else if (props.filter.operator === 'gt') {
      return { ...props.filter, operator: 'gt', parameters: [props?.filter?.parameters?.[0], ''] };
    }
    return props.filter;
  }, [props.filter]);
  const widgetContext = React.useContext(WidgetContext);
  const field = React.useMemo(
    () => session.getManager(QuotesV3FieldsManager).getScannerDefFromId(props.filter.field),
    [props.filter.field, session],
  );
  const [custom, setCustom] = React.useState(() => !field?.filterOptions || props.isScreener);
  const [interleaverMultiplier, setInterleaverMultiplier] = React.useState(1);
  const lessThanSign = '\u003c';
  const greaterThanSign = '\u003e';
  const RadioOptions = React.useMemo(() => {
    const inputOptions = [
      { id: false, label: 'Slider' },
      { id: true, label: 'Custom' },
    ];
    return inputOptions.map(({ id, label }) => (
      <RadioButton key={id.toString()} value={label}>
        {label}
      </RadioButton>
    ));
  }, []);

  const marks = React.useMemo(() => {
    if (!field?.filterOptions) return { 0: '0', 1: '∞' };

    const values = new Set(
      Object.values(field?.filterOptions ?? [])
        .flat()
        .map(value => {
          if (!value) return null;
          if (value === greaterThanSign) return '∞';
          if (value === lessThanSign) return '-∞';
          return toShorthand(fromShorthand(String(value)));
        })
        .filter(Boolean),
    );

    return Array.from(values)
      .sort((a, b) =>
        a === '-∞' || b === '∞'
          ? -1
          : a === '∞' || b === '-∞'
            ? 1
            : Number(fromShorthand(a ?? '')) - Number(fromShorthand(b ?? '')),
      )
      .reduce((acc, value, index) => ({ ...acc, [index]: value }), {});
  }, [field]);

  // Interleaver Logic
  // 10/marks * 10 = variance allowable
  // marks*40 / (widgetContext?.widgetRef?.clientWidth - 540) = interleaver multiplier, 40 is considered minimum mark text width
  // interleaver multiplier - (variance*0.1) = adjusted interleaver
  // ceiling(adjusted interleaver) - 1 = interleave multiplier
  useEffect(() => {
    if (!widgetContext?.widgetRef) return;
    const resizeObserver = new ResizeObserver(() => {
      const marksLength = Object.keys(marks)?.length;
      if (widgetContext?.widgetRef?.clientWidth) {
        const interleaver = Math.ceil(
          (marksLength * 40) / (widgetContext?.widgetRef?.clientWidth - 540) - 10 / marksLength,
        );
        setInterleaverMultiplier(interleaver);
      }
    });
    resizeObserver.observe(widgetContext?.widgetRef);
    return () => resizeObserver.disconnect(); // clean up
  }, [widgetContext?.widgetRef, marks]);

  // Interleaver Logic
  // 10/marks * 10 = variance allowable
  // marks*40 / (widgetContext?.widgetRef?.clientWidth - 540) = interleaver multiplier, 40 is considered minimum mark text width
  // interleaver multiplier - (variance*0.1) = adjusted interleaver
  // ceiling(adjusted interleaver) - 1 = interleave multiplier
  useEffect(() => {
    if (!widgetContext?.widgetRef) return;
    const resizeObserver = new ResizeObserver(() => {
      const marksLength = Object.keys(marks)?.length;
      if (widgetContext?.widgetRef?.clientWidth) {
        const interleaver = Math.ceil(
          (marksLength * 40) / (widgetContext?.widgetRef?.clientWidth - 540) - 10 / marksLength,
        );
        setInterleaverMultiplier(interleaver);
      }
    });
    resizeObserver.observe(widgetContext?.widgetRef);
    return () => resizeObserver.disconnect(); // clean up
  }, [widgetContext?.widgetRef, marks]);

  const params = React.useRef(enhancedFilters.parameters);
  params.current = enhancedFilters.parameters ?? [params.current[0], params.current[1]];

  const onMinChange: React.ChangeEventHandler<HTMLInputElement> = React.useCallback(e => {
    params.current = [`${fromShorthand(e.target.value) ?? ''}`, params.current[1] ?? ''];
  }, []);

  const onMaxChange: React.ChangeEventHandler<HTMLInputElement> = React.useCallback(e => {
    params.current = [params.current[0] ?? '', `${fromShorthand(e.target.value) ?? ''}`];
  }, []);

  const onBlur = React.useCallback(() => {
    const onChange = props.onChange;
    onChange({ ...props.filter, parameters: params.current });
  }, [props.filter, props.onChange]);

  const onParametersChange = React.useCallback(
    (parameters: number[]) => {
      const onChange = props.onChange;

      onChange({
        ...props.filter,
        parameters: parameters.map(a => {
          const val = marks[a];
          switch (val) {
            case '∞':
            case '-∞':
              return '';
            default:
              return fromShorthand(val)?.toString() ?? '';
          }
        }),
      });
    },
    [marks, props.filter, props.onChange],
  );
  const onKeyDown = React.useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === ' ') {
      e.preventDefault();
    }
  }, []);

  const handleCustomChange = React.useCallback((event: RadioChangeEvent) => {
    setCustom(event.target.value === 'Custom');
  }, []);

  if (!field) {
    return null;
  }

  const renderCustomFloatFilter = () => {
    return (
      <InputGroup className="movers-input-group" compact style={{ alignItems: 'center', display: 'flex' }}>
        <Input
          addonBefore="Min"
          onBlur={onBlur}
          onChange={onMinChange}
          onKeyDown={onKeyDown}
          placeholder={isEmpty(params.current[0]) ? marks?.[0] : params.current?.[0]}
          size="small"
          style={{ marginRight: '16px' }}
        />

        <Input
          addonBefore="Max"
          onBlur={onBlur}
          onChange={onMaxChange}
          onKeyDown={onKeyDown}
          placeholder={isEmpty(params.current?.[1]) ? marks[Object.values(marks).length - 1] : params.current?.[1]}
          size="small"
        />
      </InputGroup>
    );
  };

  const renderBaseFloatFilter = () => {
    const getMarkIndex = (param: string | undefined, defaultValue: string, type = 'min' || 'max') => {
      const value = param ? paramToValue(param) : defaultValue;
      const nearestDigit = findNearestDot(marks, Number(value), type);
      return Number(Object.entries(marks).find(([_, v]) => v === value)?.[0] ?? nearestDigit ?? 0);
    };

    const minIndex = getMarkIndex(params.current?.[0], String(Object.values(marks)[0]), 'min') ?? 0;
    const maxIndex = getMarkIndex(
      params.current?.[1],
      String(Object.values(marks)[Object.values(marks).length - 1]),
      'max',
    );
    const maxMarkIndex = Object.keys(marks).length - 1;
    return (
      <SliderContainer interleaverMultiplier={interleaverMultiplier}>
        <Slider
          className={`${interleaverMultiplier ? 'shorter-layout' : 'another'}`}
          defaultValue={[minIndex, maxIndex]}
          dots
          marks={marks}
          max={maxMarkIndex}
          min={0}
          onChange={onParametersChange}
          range
          step={1}
          style={{ margin: '0 6px 14px' }}
          tooltip={{ formatter: null }}
          value={[minIndex, maxIndex]}
        />
      </SliderContainer>
    );
  };

  return (
    <Parameter>
      <ColumnStyles isScreener={props.isScreener}>
        <div>{custom ? renderCustomFloatFilter() : renderBaseFloatFilter()}</div>

        {!props.isScreener ? (
          <RadioGroup
            buttonStyle="solid"
            onChange={handleCustomChange}
            size="small"
            style={{ width: 'max-content' }}
            value={custom ? 'Custom' : 'Slider'}
          >
            {RadioOptions}
          </RadioGroup>
        ) : null}
      </ColumnStyles>
    </Parameter>
  );
};

export const Parameter = styled(TC.Row)`
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  padding: 0em 0em;
`;

export const ParameterLabel = styled(TC.Row)`
  align-items: center;
  display: inline-flex;
  margin: 0.5em 0;
  min-width: 100px;
  white-space: nowrap;
`;

export const ColumnStyles = styled(TC.Column)<{ isScreener?: boolean }>`
  align-items: center;
  display: grid;
  grid-gap: 16px;
  ${props =>
    !props.isScreener
      ? css`
          grid-template-columns: auto 106px;
        `
      : css``}
  width: 100%;
  margin-bottom: 2px;
`;

export const SliderContainer = styled(TC.Inline)<{ interleaverMultiplier?: number }>`
  flex-grow: 1;
  height: 30px;
  margin-top: -20px;
  /* max-width: 300px; */
  width: 100%;
  .shorter-layout {
    span.ant-slider-mark-text {
      visibility: hidden;
    }
    .ant-slider-mark span:nth-child(1) {
      visibility: visible;
    }
    .ant-slider-mark span:nth-last-child(1) {
      visibility: visible;
    }

    ${props =>
      props.interleaverMultiplier
        ? css`
            .ant-slider-mark span:nth-child(${props.interleaverMultiplier}n+1) {
              visibility: visible;
            }
          `
        : css``}
  }
`;
