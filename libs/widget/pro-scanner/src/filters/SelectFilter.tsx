'use client';
import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { SessionContext } from '@benzinga/session-context';
import { FilterObject, QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';

const SelectFilter: React.FC<{
  filter: FilterObject;
  multiple: boolean;
  onChange: (filter: FilterObject) => void;
  onOpenChanged?: (isOpen: boolean) => void;
}> = props => {
  const [filter, setFilter] = useState(props.filter);

  useEffect(() => setFilter(props.filter), [props.filter]);

  const session = React.useContext(SessionContext);
  const fieldDef = React.useMemo(
    () => session.getManager(QuotesV3FieldsManager).getScannerDefFromId(props.filter.field),
    [session, props.filter.field],
  );
  // A bit hacky, add s to the label for placeholder text.
  // Select are also "all" by default for now.
  const label = fieldDef?.label ?? props.filter.field;
  const setFilterCriteriaIn = (values: string[]) => {
    if (!Array.isArray(values)) {
      values = [values];
    }
    const filter = { field: props.filter.field, operator: 'in', parameters: values };
    setFilter(filter);
    props.onChange(filter);
  };

  const options = fieldDef?.filterOptions ?? [];
  return (
    <Select
      maxTagCount="responsive"
      mode={props.multiple ? 'multiple' : undefined}
      onChange={setFilterCriteriaIn}
      onDropdownVisibleChange={props.onOpenChanged}
      placeholder={`All ${label?.endsWith('y') ? label.slice(0, -1) + 'ies' : label + 's'}`}
      size="small"
      style={{ width: '100%' }}
      value={filter.parameters}
    >
      {Object.entries(options).map(([key, value]) => (
        <Select.Option key={key} value={value}>
          {key}
        </Select.Option>
      ))}
    </Select>
  );
};
export default SelectFilter;
