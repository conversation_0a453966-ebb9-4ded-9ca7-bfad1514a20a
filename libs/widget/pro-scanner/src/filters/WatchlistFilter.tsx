'use client';
import React, { useContext, useEffect } from 'react';

import { Select } from 'antd';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import styled from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { Watchlist, WatchlistManager, WatchlistManagerEvent } from '@benzinga/watchlist-manager';
import Hooks from '@benzinga/hooks';
import { useState } from 'react';
import { TrackingManager } from '@benzinga/tracking-manager';

export function getWatchlistIdsFromFilter(filter: FilterObject): string[] {
  return filter.parameters
    .filter(symbol => symbol.startsWith('watchlistId:'))
    .map(symbol => symbol?.replace('watchlistId:', ''));
}

const WatchlistFilter: React.FC<{
  filter: FilterObject;
  onChange: (filter: FilterObject) => void;
  onOpenChanged?: (isOpen: boolean) => void;
}> = props => {
  const session = useContext(SessionContext);

  const [filter, setFilter] = useState(props.filter);
  const [selectedWatchlistIds, setSelectedWatchlistIds] = useState(getWatchlistIdsFromFilter(filter));

  const [watchlists, setWatchlists] = React.useState<Watchlist[]>(() =>
    session.getManager(WatchlistManager).getStoredWatchlists(),
  );

  useEffect(() => {
    setFilter(props.filter);
    setSelectedWatchlistIds(getWatchlistIdsFromFilter(props.filter));
  }, [props.filter]);

  useEffect(() => {
    session.getManager(TrackingManager).trackWidgetEvent('remove_filter', 'scanner', {
      filter_value: selectedWatchlistIds.join(','),
    });
  }, [session, selectedWatchlistIds]);

  React.useEffect(() => {
    session.getManager(WatchlistManager).getWatchlists();
  }, [session]);

  const handleChange = React.useCallback(
    (value: string[]) => {
      props.onChange({ field: filter.field, operator: 'in', parameters: value.map(v => `watchlistId:${v}`) });
      setSelectedWatchlistIds(value);
    },
    [props, filter.field],
  );

  //eslint-disable-next-line react-hooks/exhaustive-deps
  React.useEffect(() => handleChange(selectedWatchlistIds), [watchlists]);

  const watchlistEventHandler = React.useCallback((event: WatchlistManagerEvent) => {
    switch (event.type) {
      case 'watchlist:updated_watchlists':
        if (!event.watchlists) {
          event.watchlists = [];
        }
        setWatchlists(event.watchlists);
        break;
    }
  }, []);

  Hooks.useSubscriber(session.getManager(WatchlistManager), watchlistEventHandler);

  const options = watchlists
    .sort((a, b) => (a.name.toUpperCase() > b.name.toUpperCase() ? 1 : -1))
    .map(watchlist => (
      <Select.Option key={watchlist.watchlistId} label={watchlist.name} value={watchlist.watchlistId}>
        {watchlist.name}
      </Select.Option>
    ));

  return (
    <SelectContainer>
      <Select
        mode="multiple"
        onChange={handleChange}
        onDropdownVisibleChange={props.onOpenChanged}
        optionFilterProp="label"
        placeholder="Select Watchlist(s)"
        size="small"
        style={{ width: '100%' }}
        value={selectedWatchlistIds}
      >
        {options}
      </Select>
    </SelectContainer>
  );
};

export default WatchlistFilter;

const SelectContainer = styled.div`
  display: flex;
  flex: 1;
`;
