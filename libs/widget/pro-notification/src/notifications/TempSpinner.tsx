'use client';
import React from 'react';
import styled, { TC } from '@benzinga/themetron';
import { Spinner } from '@benzinga/core-ui';

interface Props {
  destroy?: boolean;
  height?: string;
}

export const TempSpinner: React.FC<Props> = ({ destroy = true, height = '96px' }) => {
  const [showSpinner, setShowSpinner] = React.useState(false);
  const timeoutRef = React.useRef<NodeJS.Timeout>();

  React.useEffect(() => {
    setShowSpinner(true);
    if (destroy) {
      timeoutRef.current = setTimeout(() => {
        setShowSpinner(false);
      }, 3000);
    }
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [destroy]);

  return showSpinner ? (
    <SpinnerContainer height={height}>
      <Spinner />
    </SpinnerContainer>
  ) : null;
};

const SpinnerContainer = styled(TC.Row)<{ height: string }>`
  height: ${({ height }) => height} !important;
  justify-content: start;
  align-items: center;
`;
