import React from 'react';

import styled from '@benzinga/themetron';

import { BZ_CONTACT_PHONE } from '@benzinga/user-context';
import { openWindow } from '@benzinga/frontend-utils';

function newIntercomWindow() {
  const url = '/helpchat/';
  const name = 'Benzinga Pro Help Chat';
  const options = 'width=425,height=550,left=0,resizable';
  openWindow(url, name, options, undefined, { free: true });
}

export interface Props {
  hideSearchTips?: boolean;
  message?: React.ReactNode;
}

const NoResultsContainer = styled.div`
  font-size: 15px;
  margin-left: 3em;
  margin-top: 2em;
  text-align: left;
`;

const NoResultsTitle = styled.div`
  font-size: 1.25em;
  font-weight: lighter;
  padding-bottom: 10px;
`;

const Tips = styled.div`
  font-weight: lighter;
  margin-top: 2em;
`;

const Header = styled.div`
  font-size: 1em;
`;

const Ul = styled.ul`
  margin-top: 0.5em;
`;

const Li = styled.li`
  margin-top: 0.25em;
`;

const Resources = styled.div`
  margin-top: 2em;
`;

const SupportButton = styled.button`
  &:hover {
    background-color: ${({ theme }) => theme.colors.brand};
  }

  background-color: ${({ theme }) => theme.colors.brandMuted};
  color: ${({ theme }) => theme.colors.brandForeground};
  cursor: pointer;
  display: inline-block;
  font-size: 1em;
  margin-top: 1em;
  padding-bottom: 0.75em;
  padding-left: 1.5em;
  padding-right: 1.5em;
  padding-top: 0.75em;
  position: relative;
  text-align: center;
  text-transform: uppercase;
  white-space: nowrap;
`;

const SupportTextContainer = styled.div`
  margin-top: 1em;
`;

const SupportText = styled.p`
  font-weight: lighter;
  margin-bottom: 0;
`;

const NoResults: React.FC<Props> = ({ hideSearchTips, message }) => (
  <NoResultsContainer>
    {message || <NoResultsTitle>No results found.</NoResultsTitle>}
    {!hideSearchTips && (
      <Tips>
        <Header>Search Tips:</Header>
        <Ul>
          <Li>Make your search as concise as possible.</Li>
          <Li>Ensure words are spelled correctly.</Li>
          <Li>Try less specific keywords.</Li>
        </Ul>
      </Tips>
    )}
    <Resources>
      <Header>Not finding what you need?</Header>
      <SupportButton onClick={newIntercomWindow}>chat with support</SupportButton>
      <SupportTextContainer>
        <SupportText>Contact support by clicking above</SupportText>
        <SupportText>or call us at {BZ_CONTACT_PHONE}.</SupportText>
      </SupportTextContainer>
    </Resources>
  </NoResultsContainer>
);

export default NoResults;
