'use client';
import { ColDef, GridApi, GridOptions } from '@ag-grid-community/core';
import React from 'react';
import { addTutorialClasses } from '@benzinga/frontend-utils';
import {
  ChangeColDef,
  ChangePercentColDef,
  gridOptions,
  ShortenedNumberColDef,
  SendLinkContext,
  SymbolColDef,
  PriceColDef,
  NumberColDef,
} from '@benzinga/pro-ui';
import { BenzingaGrid, GridExportParams } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { unshorthandValue } from '@benzinga/utils';
import { shorthandValue } from './utils';

export interface OwnProps {
  className?: string;
  rowData: null | any[];
  showMarketCap: boolean;
  showPrice: boolean;
  table: TableParameters;
  loadGrid(parameters: Partial<TableParameters>): void;
}

type Props = OwnProps;

export interface State {
  columnDefs: ColDef[];
  defaultColDef: ColDef;
}

const MoversGrid: React.FC<Props> = props => {
  const gridApi = React.useRef<GridApi | null>(null);
  const exportParams: GridExportParams = {
    filename: 'Movers',
  };

  const sendLink = React.useContext(SendLinkContext);

  const moversColumnDefs = React.useMemo(() => {
    const moversColumnDefs: ColDef[] = [];
    moversColumnDefs.push(
      SymbolColDef({
        field: 'symbol',
        headerName: 'Symbol',
        initialWidth: 100,
        minWidth: 75,
      }),
    );
    moversColumnDefs.push({
      cellStyle: { 'text-align': 'left' },
      field: 'companyName',
      filter: 'agTextColumnFilter',
      headerName: 'Name',
      initialWidth: 275,
      minWidth: 100,
    });
    if (props.showMarketCap) {
      moversColumnDefs.push({
        cellStyle: { 'text-align': 'left' },
        comparator: (valueA, valueB) => {
          if (valueA === undefined || valueA === '') {
            return -1;
          }
          if (valueB === undefined || valueB === '') {
            return 1;
          }
          return parseInt(unshorthandValue(valueA)) - parseInt(unshorthandValue(valueB));
        },
        field: 'marketCap',
        headerName: 'Market Cap',
        initialHide: true,
        initialWidth: 175,
        minWidth: 100,
        valueGetter: shorthandValue('marketCap'),
      });
    }
    moversColumnDefs.push({
      cellStyle: { 'text-align': 'left' },
      field: 'gicsSectorName',
      filter: 'agTextColumnFilter',
      headerName: 'Sector',
      initialHide: true,
      initialWidth: 275,
      minWidth: 100,
    });
    if (props.showPrice) {
      moversColumnDefs.push(
        PriceColDef({
          cellStyle: { 'text-align': 'left' },
          field: 'close',
          headerName: 'Price',
          initialWidth: 110,
          minWidth: 100,
        }),
      );
    }
    moversColumnDefs.push(
      ChangePercentColDef({
        field: 'changePercent',
        headerName: 'Change %',
        initialWidth: 125,
        minWidth: 85,
        sort: 'desc',
      }),
    );
    moversColumnDefs.push(
      ChangeColDef({
        absSort: true,
        field: 'change',
        headerName: 'Change',
        initialWidth: 115,
        minWidth: 85,
      } as any),
    );
    moversColumnDefs.push(
      NumberColDef({
        field: 'previousClose',
        headerName: 'Previous Close',
        initialWidth: 150,
        minWidth: 85,
      }),
    );
    moversColumnDefs.push(
      ShortenedNumberColDef({
        comparator: (valueA: number | string, valueB: number | string) => {
          if (valueA === null || valueA === undefined) return -1;
          if (valueB === null || valueB === undefined) return 1;

          const parseNumber = (value: number | string): number =>
            typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value;
          return parseNumber(valueA) - parseNumber(valueB);
        },
        field: 'volume',
        headerName: 'Volume',
        initialWidth: 150,
        minWidth: 85,
      }),
    );
    moversColumnDefs.push(
      ShortenedNumberColDef({
        field: 'averageVolume',
        headerName: 'Average Volume (3 Month)',
        initialWidth: 150,
        minWidth: 85,
      }),
    );
    moversColumnDefs.push(
      ShortenedNumberColDef({
        comparator: (valueA: number | string, valueB: number | string) => {
          if (valueA === null || valueA === undefined) return -1;
          if (valueB === null || valueB === undefined) return 1;

          const parseNumber = (value: number | string): number =>
            typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value;
          return parseNumber(valueA) - parseNumber(valueB);
        },
        field: 'shareFloat',
        headerName: 'Float',
        initialWidth: 150,
        minWidth: 85,
      }),
    );
    return moversColumnDefs;
  }, [props.showMarketCap, props.showPrice]);

  const state = React.useMemo(
    () => ({
      columnDefs: addTutorialClasses(moversColumnDefs, 'Movers'),
      defaultColDef: {
        cellStyle: { 'text-align': 'right' },
        filter: undefined,
        headerClass: ['u-uppercase'],
        resizable: true,
        sortable: true,
      },
    }),
    [moversColumnDefs],
  );

  const myGridOptions = React.useMemo<GridOptions>(
    () => ({
      animateRows: true,
      enableRangeSelection: true,
      getRowId: props => props.data.symbol,
      headerHeight: 30,
      immutableData: true,
      onGridReady: params => {
        gridApi.current = params.api;
      },
      rowHeight: 20,
      rowSelection: 'multiple',
      sideBar: gridOptions.sideBar,
      statusBar: gridOptions.statusBar,
    }),
    [],
  );

  const { className, loadGrid, rowData, table } = props;
  return (
    <BenzingaGrid
      className={className}
      columnDefs={state.columnDefs}
      defaultColDef={state.defaultColDef}
      exportParams={exportParams}
      gridLayout={table}
      gridOptions={myGridOptions}
      onGridLayoutChanged={loadGrid}
      onSymbolClick={sendLink.onSymbolClick}
      rowClass="TUTORIAL_ag_header_Movers"
      rowData={rowData || []}
      sizeColumnsToFit={true}
      suppressRowTransform
      symbolColIDs={['symbol']}
    />
  );
};

export default MoversGrid;
