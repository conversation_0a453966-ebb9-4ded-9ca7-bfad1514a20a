import React from 'react';

import { NO_VALUE, numberInMillions } from '@benzinga/fission';
import withSpinner from './withSpinner';
import renderDates from './renderDates';
import { BalanceSheet, Financial } from '@benzinga/securities-manager';

interface Props {
  financials?: Financial[];
  isLoading: boolean;
}

class BalanceSheetTab extends React.Component<Props> {
  renderField(financials: Financial[] | undefined, field: keyof BalanceSheet) {
    return (
      financials?.map((statements, index) => {
        let cellValue = NO_VALUE;
        if (statements.balanceSheet) {
          cellValue = numberInMillions(statements.balanceSheet[field]) || NO_VALUE;
        }
        const key = `${index}-field`;
        return <td key={key}>{cellValue}</td>;
      }) ?? []
    );
  }

  renderOtherAssets(financials?: Financial[]) {
    return (
      financials?.map((statements, index) => {
        const other =
          statements.balanceSheet.totalAssets -
          (statements.balanceSheet.cashAndCashEquivalents || 0) -
          (statements.balanceSheet.otherShortTermInvestments || 0) -
          (statements.balanceSheet.receivables || 0) -
          (statements.balanceSheet.inventory || 0) -
          (statements.balanceSheet.otherCurrentAssets || 0) -
          (statements.balanceSheet.netPpe || 0) -
          (statements.balanceSheet.goodwill || 0) -
          (statements.balanceSheet.otherIntangibleAssets || 0);
        const key = `${index}-otherAssets`;

        return <td key={key}>{numberInMillions(other) || NO_VALUE}</td>;
      }) ?? []
    );
  }

  renderOtherLiabilities(financials?: Financial[]) {
    return (
      financials?.map((statements, index) => {
        const other =
          statements.balanceSheet.totalLiabilities -
          (statements.balanceSheet.accountsPayable || 0) -
          (statements.balanceSheet.currentAccruedExpenses || 0) -
          (statements.balanceSheet.currentDebtAndCapitalLeaseObligation || 0) -
          (statements.balanceSheet.longTermDebtAndCapitalLeaseObligation || 0) -
          (statements.balanceSheet.longTermDebtAndCapitalLeaseObligation || 0);
        const key = `${index}-otherLiabilities`;
        return <td key={key}>{numberInMillions(other) || NO_VALUE}</td>;
      }) ?? []
    );
  }

  renderOtherEquity(financials?: Financial[]) {
    return (
      financials?.map((statements, index) => {
        const other =
          statements.balanceSheet.totalEquity -
          (statements.balanceSheet.commonStock || 0) -
          (statements.balanceSheet.retainedEarnings || 0);
        const key = `${index}-otherEquity`;
        return <td key={key}>{numberInMillions(other) || NO_VALUE}</td>;
      }) ?? []
    );
  }

  render() {
    const { financials } = this.props;
    return (
      <table className="FinancialTable" id="Financial-Balance_Sheet-Table">
        <thead>
          <tr>
            <th>In Millions (Except per share Items)</th>
            {renderDates(financials)}
          </tr>
        </thead>
        <tbody>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td colSpan={5}>Assets</td>
          </tr>
          <tr>
            <td>Cash & Equivalents</td>
            {this.renderField(financials, 'cashAndCashEquivalents')}
          </tr>
          <tr>
            <td>Short Term Investments</td>
            {this.renderField(financials, 'otherShortTermInvestments')}
          </tr>
          <tr>
            <td>Accounts Receivable</td>
            {this.renderField(financials, 'receivables')}
          </tr>
          <tr>
            <td>Inventory</td>
            {this.renderField(financials, 'inventory')}
          </tr>
          <tr>
            <td>Other Current Assets</td>
            {this.renderField(financials, 'otherCurrentAssets')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Current Assets</td>
            {this.renderField(financials, 'currentAssets')}
          </tr>
          <tr>
            <td>Property, Plant, and Equipment</td>
            {this.renderField(financials, 'netPpe')}
          </tr>
          <tr>
            <td>LT Investments & Advances</td>
            {this.renderField(financials, 'investmentsAndAdvances')}
          </tr>
          <tr>
            <td>Goodwill</td>
            {this.renderField(financials, 'goodwill')}
          </tr>
          <tr>
            <td>Intangible Assets</td>
            {this.renderField(financials, 'otherIntangibleAssets')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Non-Current Assets</td>
            {this.renderField(financials, 'totalNonCurrentAssets')}
          </tr>
          <tr>
            <td>Other Assets</td>
            {this.renderOtherAssets(financials)}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Assets</td>
            {this.renderField(financials, 'totalAssets')}
          </tr>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td colSpan={5}>Liabilities</td>
          </tr>
          <tr>
            <td>Accounts Payable</td>
            {this.renderField(financials, 'accountsPayable')}
          </tr>
          <tr>
            <td>Current Accrued Expenses</td>
            {this.renderField(financials, 'currentAccruedExpenses')}
          </tr>
          <tr>
            <td>Current Portion of LT Debt</td>
            {this.renderField(financials, 'currentDebtAndCapitalLeaseObligation')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Current Liabilities</td>
            {this.renderField(financials, 'currentLiabilities')}
          </tr>
          <tr>
            <td>LT Debt and Lease Obligation</td>
            {this.renderField(financials, 'longTermDebtAndCapitalLeaseObligation')}
          </tr>
          <tr>
            <td>Non-Current Deferred Liabilities</td>
            {this.renderField(financials, 'longTermDebtAndCapitalLeaseObligation')}
          </tr>
          <tr>
            <td>Other Liabilities</td>
            {this.renderOtherLiabilities(financials)}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Non-Current Liabilities</td>
            {this.renderField(financials, 'totalNonCurrentLiabilities')}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Liabilities</td>
            {this.renderField(financials, 'totalLiabilities')}
          </tr>
          <tr></tr>
          <tr className="FinancialTable-section">
            <td colSpan={5}>ShareHolder Equity</td>
          </tr>
          <tr>
            <td>Common Stock</td>
            {this.renderField(financials, 'commonStock')}
          </tr>
          <tr>
            <td>Retained Earnings</td>
            {this.renderField(financials, 'retainedEarnings')}
          </tr>
          <tr>
            <td>Other Shareholder Equity</td>
            {this.renderOtherEquity(financials)}
          </tr>
          <tr className="FinancialTable-total">
            <td>Total Equity</td>
            {this.renderField(financials, 'totalEquity')}
          </tr>
        </tbody>
      </table>
    );
  }
}

export default withSpinner<Props>(BalanceSheetTab);
