'use client';
import React, { useEffect, useState } from 'react';
import Hooks from '@benzinga/hooks';
import styled from '@benzinga/themetron';
import AnimatedNumber from 'animated-number-react';
import CurrencyInputWrapper from '../MortgageCalculator/CurrencyInputWrapper';
import { sanitizeHTML } from '@benzinga/frontend-utils';
const ReactECharts = React.lazy(() => import('echarts-for-react'));

export interface HELOCCalculatorProps {
  description?: string;
  title?: string;
  link?: string;
}

const StyledButton = styled.button`
  background-color: #ff7b00;
  color: #fefefe;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.21);
  text-shadow: 1px 0 0 rgba(0, 0, 0, 0.1);
  transition: all 0.15s ease-in-out;
  font-family: sans-serif;
  letter-spacing: 0.09em;
  min-width: 120px;
  &:hover {
    transform: translateY(-1px);
    box-shadow:
      0 2px 1px rgba(50, 50, 93, 0.2),
      0 3px 6px rgba(0, 0, 0, 0.1);
  }
`;

const inputStyles = {
  backgroundColor: ' #fefefe',
  border: '1px solid #ccc',
  color: '#0a0a0a',
} as React.CSSProperties;

export const HELOCCalculator: React.FC<HELOCCalculatorProps> = ({ description, link, title }) => {
  const [loanAmount, setLoanAmount] = useState<number>(2000);
  const [interestRate, setInterestRate] = useState<number>(7);
  const [interestOnlyPeriod, setInterestOnlyPeriod] = useState<number>(5);
  const [repaymentPeriod, setRepaymentPeriod] = useState<number>(5);
  const [update, setUpdate] = useState<boolean>(false);

  const [totalPayment, setTotalPayment] = useState<number>(100);
  const [totalInterest, setTotalInterest] = useState<number>(100);

  const [remainingBalanceSeries, setRemainingBalanceSeries] = useState<number[]>([]);
  const [principalPaymentSeries, setprincipalPaymentSeries] = useState<number[]>([]);
  const [interestPaymentSeries, setInterestPaymentSeries] = useState<number[]>([]);
  const [itemsPrepared, setItemsPrepared] = useState<boolean>(false);
  const [dataView, setDataView] = useState<string>('chart');

  const calculateEstimatedMontlyPayment = (rate, principal, totalMonths) => {
    const monthlyRate = rate / 100 / 12;
    const part1 = principal * monthlyRate;
    const part2 = 1 - Math.pow(1 + monthlyRate, -totalMonths);
    const estM = part1 / part2;
    return Number(estM.toFixed(2));
  };

  const refreshHELOCCalculation = React.useCallback(() => {
    setRemainingBalanceSeries([]);
    setprincipalPaymentSeries([]);
    setInterestPaymentSeries([]);

    const monthlyRemainingBalance: number[] | null = [];
    const monthlyPrincipalPayment: number[] | null = [];
    const monthlyInerestPayment: number[] | null = [];
    let remainingBalance = loanAmount;

    const totalYear = interestOnlyPeriod + repaymentPeriod;

    const estimatedMonthlyPayment = calculateEstimatedMontlyPayment(interestRate, loanAmount, repaymentPeriod * 12);

    for (let index = 1; index <= totalYear; index++) {
      let interest = 0;
      if (index <= interestOnlyPeriod) {
        for (let j = 1; j <= 12; j++) {
          monthlyRemainingBalance.push(remainingBalance);
          monthlyPrincipalPayment.push(0);

          interest = Number(((interestRate / 100 / 12) * remainingBalance).toFixed(2));
          monthlyInerestPayment.push(interest);
        }
      } else {
        for (let j = 1; j <= 12; j++) {
          monthlyRemainingBalance.push(remainingBalance);

          interest = Number((remainingBalance * (interestRate / 100 / 12)).toFixed(2));
          monthlyInerestPayment.push(interest);

          const principalPayment = Number((estimatedMonthlyPayment - interest).toFixed(2));
          monthlyPrincipalPayment.push(principalPayment);

          remainingBalance = Number((remainingBalance - principalPayment).toFixed(2));
        }
      }
    }

    setTotalPayment(
      monthlyPrincipalPayment.reduce((a, b) => a + b, 0) + monthlyInerestPayment.reduce((a, b) => a + b, 0),
    );
    setTotalInterest(monthlyInerestPayment.reduce((a, b) => a + b, 0));

    setRemainingBalanceSeries(monthlyRemainingBalance);
    setInterestPaymentSeries(monthlyInerestPayment);
    setprincipalPaymentSeries(monthlyPrincipalPayment);
    setItemsPrepared(true);
  }, [loanAmount, interestRate, interestOnlyPeriod, repaymentPeriod]);

  Hooks.useEffectDidMount(() => {
    refreshHELOCCalculation();
  });

  useEffect(() => {
    if (update) {
      refreshHELOCCalculation();
      setUpdate(false);
    }
  }, [loanAmount, interestRate, interestOnlyPeriod, repaymentPeriod, update, refreshHELOCCalculation]);

  const onChangeSetLoanValue = React.useCallback((floatvalue: string) => {
    setLoanAmount(Number(floatvalue));
    setUpdate(true);
  }, []);

  const onChangeInterestRate = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInterestRate(Number(e.target.value));
    setUpdate(true);
  }, []);

  const onChangeSetInterestOnlyPeriod = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInterestOnlyPeriod(Number(e.target.value));
    setUpdate(true);
  }, []);

  const onChangeSetRepaymentPeriod = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setRepaymentPeriod(Number(e.target.value));
    setUpdate(true);
  }, []);

  const goToHELOC = () => {
    window.open(link, '_blank');
  };

  const topChartOptions = React.useMemo(() => {
    const emphasisStyle = {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0,0,0,0.3)',
      },
    };
    const xAxisData: string[] = [];

    for (let i = 0; i < interestPaymentSeries.length / 12; i++) {
      xAxisData.push('Year ' + (i + 1));
    }

    const batchSize = 12;
    const interestPaymentBatch: number[] = [];
    const principalPaymentBatch: number[] = [];

    const remainingBalanceBatch: number[] = remainingBalanceSeries.filter((_, index) => (index + 1) % 12 === 0);

    for (let i = 0; i < interestPaymentSeries.length; i += batchSize) {
      const interestbatch = interestPaymentSeries.slice(i, i + batchSize);
      const interestbatchSum = interestbatch.reduce((sum, num) => sum + num, 0);
      interestPaymentBatch.push(interestbatchSum);

      const princiapalbatch = principalPaymentSeries.slice(i, i + batchSize);
      const princiapalbatchSum = princiapalbatch.reduce((sum, num) => sum + num, 0);
      principalPaymentBatch.push(princiapalbatchSum);
    }

    if (itemsPrepared) {
      return {
        grid: { containLabel: true, left: 0 },
        legend: {
          data: ['Interest Payment', 'Remaining Balance', 'Principal'],
        },
        series: [
          {
            data: interestPaymentBatch,
            emphasis: emphasisStyle,
            name: 'Interest Payment',
            stack: 'one',
            type: 'bar',
          },
          {
            data: remainingBalanceBatch,
            emphasis: emphasisStyle,
            name: 'Remaining Balance',
            stack: 'two',
            type: 'bar',
          },
          {
            data: principalPaymentBatch,
            emphasis: emphasisStyle,
            name: 'Principal',
            stack: 'three',
            type: 'bar',
          },
        ],
        tooltip: {},
        xAxis: {
          axisLine: { onZero: true },
          data: xAxisData,
          name: 'Years',
          splitArea: { show: false },
          splitLine: { show: false },
        },
        yAxis: {},
      };
    } else {
      return {};
    }
  }, [itemsPrepared, interestPaymentSeries, remainingBalanceSeries, principalPaymentSeries]);

  const tabStyles = (tabType: string) => {
    const initialStyles = { paddingBottom: '4px', width: '50%' };
    if (dataView === tabType) {
      return { borderBottom: '3px solid rgb(255, 90, 31)', fontWeight: 'bold', ...initialStyles };
    }
    return { borderBottom: '3px solid #c4c4c4', ...initialStyles };
  };

  return (
    <HELOCCalculatorWrap className="bg-white box-border flex flex-col h-auto w-full justify-center p-4 py-8 relative wrap-block flex-col bg-white border border-t-4 shadow-sm">
      {title && <h3>{title}</h3>}
      {description && <p className=" mt-2" dangerouslySetInnerHTML={{ __html: sanitizeHTML(description) }} />}

      <div className="heloc-calculator mt-5" id="purchase-calculator">
        <div className="heloc-calculator-inputs">
          <div className="mb-2">
            <CurrencyInputWrapper
              inputName="loan_amount"
              labelText="Loan Amount"
              onChange={(floatvalue: string) => onChangeSetLoanValue(floatvalue)}
              value={loanAmount}
            />
          </div>
          <div className="mb-2">
            <label className="w-full text-left text-xs font-semibold" htmlFor="interest_rate">
              Interest Rate (%)
            </label>
            <input
              className="appearance-none relative w-full h-10 box-border block overflow-visible p-2 mb-2 text-base rounded"
              id="interest_rate"
              min={1}
              name="interest_rate"
              onChange={onChangeInterestRate}
              style={inputStyles}
              type="number"
              value={interestRate}
            />
          </div>
          <div className="mb-2">
            <label className="w-full text-left text-xs font-semibold" htmlFor="interest_only_period">
              Interest-only period (years)
            </label>
            <input
              className="appearance-none relative w-full h-10 box-border block overflow-visible p-2 mb-2 text-base rounded"
              id="interest_only_period"
              min={1}
              name="interest_only_period"
              onChange={onChangeSetInterestOnlyPeriod}
              style={inputStyles}
              type="number"
              value={interestOnlyPeriod}
            />
          </div>

          <div>
            <label className="w-full text-left text-xs font-semibold" htmlFor="repayment_period">
              Repayment period (years)
            </label>
            <input
              className="appearance-none relative w-full h-10 box-border block overflow-visible p-2 mb-2 text-base rounded"
              id="repayment_period"
              min={1}
              name="repayment_period"
              onChange={onChangeSetRepaymentPeriod}
              style={inputStyles}
              type="number"
              value={repaymentPeriod}
            />
          </div>
        </div>
        <div className="heloc-calculator-results">
          <div className="item-data mb-4">
            <div className="label">Total Payments:</div>
            <h3>
              <AnimatedNumber
                formatValue={(value: number) =>
                  value ? `$${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}` : '–'
                }
                value={totalPayment}
              />
            </h3>
          </div>
          <div className="item-data mb-4">
            <div className="label">Total Principal:</div>
            <h3>
              <AnimatedNumber
                formatValue={(value: number) =>
                  value ? `$${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}` : '–'
                }
                value={loanAmount}
              />
            </h3>
          </div>
          <div className="item-data mb-4">
            <div className="label">Total Interest:</div>
            <h3>
              <AnimatedNumber
                formatValue={(value: number) =>
                  value ? `$${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}` : '–'
                }
                value={totalInterest}
              />
            </h3>
          </div>
          {link && (
            <StyledButton
              className="appearance-none antialiased rounded text-center w-full h-10 mt-1 font-semibold uppercase text-sm flex items-center justify-center translate-x-0"
              onClick={goToHELOC}
              type="button"
            >
              Get Best Rate
            </StyledButton>
          )}
        </div>
      </div>
      <div className="overview-data mt-5">
        <div className="flex text-center">
          <div
            className="tab"
            onClick={() => {
              setDataView('payment');
            }}
            style={tabStyles('payment')}
          >
            Monthly Payment
          </div>
          <div
            className="tab"
            onClick={() => {
              setDataView('chart');
            }}
            style={tabStyles('chart')}
          >
            Yearly Chart
          </div>
        </div>
        <div className="data-view mt-2">
          {dataView == 'payment' ? (
            <>
              <div className="payment-detail">
                <table className="table-auto h-[500px] w-full">
                  <thead>
                    <tr>
                      <th>Month</th>
                      <th>Balance</th>
                      <th>Principal Paid</th>
                      <th>Interest Paid</th>
                    </tr>
                  </thead>

                  {interestPaymentSeries &&
                    interestPaymentSeries?.length > 0 &&
                    interestPaymentSeries.map((item, i) =>
                      item ? (
                        <tr key={`interest-${i}`}>
                          <td>{i + 1}</td>
                          <td>${remainingBalanceSeries[i]}</td>
                          <td>${principalPaymentSeries[i]}</td>
                          <td>${item}</td>
                        </tr>
                      ) : null,
                    )}
                </table>
              </div>
            </>
          ) : (
            <>
              <div className="chart-detail">
                <ReactECharts
                  className="chart-body mt-4 z-[1]"
                  key="performance"
                  notMerge={true}
                  option={topChartOptions}
                  style={{ height: '325px', overflow: 'hidden', width: '100%' }}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </HELOCCalculatorWrap>
  );
};

const HELOCCalculatorWrap = styled.div`
  border-top-color: rgb(255, 90, 31);
  .heloc-calculator {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 2rem;
    .heloc-calculator-inputs,
    .heloc-calculator-results {
      grid-column: span 1 / span 1;
    }
    .error {
      color: red;
      display: block;
      font-size: 0.8rem;
    }
    @media (max-width: 800px) {
      .heloc-calculator-inputs,
      .heloc-calculator-results {
        grid-column: span 2 / span 2;
      }
    }
  }
  .overview-data {
    .tab {
      cursor: pointer;
    }
  }
  .item-data {
    .label {
      font-weight: 500;
    }
    h3 {
      font-size: 1.4rem;
    }
  }

  .payment-detail {
    max-height: 400px;
    overflow: auto;
    table {
      td,
      th {
        text-algin: center;
        border-width: 2px;
        padding: 4px;
      }
    }
  }
`;
