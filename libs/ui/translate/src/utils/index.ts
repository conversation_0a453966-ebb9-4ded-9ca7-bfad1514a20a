import i18n, {
  DEFAULT_LANGUAGE,
  DEFAULT_NAMESPACE,
  TRANSLATION_RESOURCES,
  LOCALES,
  LocaleType,
} from '../config/config';
import type { TOptions, Resource } from 'i18next';

export const translate = (slug: string, options?: TOptions) => {
  const t = i18n.getFixedT(i18n.language, options?.ns ?? DEFAULT_NAMESPACE);

  return t(slug, options);
};

export const setLanguage = async (lang: string, namespaces?: string[]) => {
  if (Array.isArray(namespaces)) {
    for (const namespace of namespaces) {
      const translations = await import(`../locales/${lang}/${namespace}.json`);
      await i18n.addResourceBundle(lang, namespace, translations.default, true, true);
    }
  } else {
    const translations = await import(`../locales/${lang}/common.json`);
    await i18n.addResourceBundle(lang, 'common', translations.default, true, true);
  }

  await i18n.changeLanguage(lang);
  return i18n.getDataByLanguage(lang);
};

export const isTranslationAvailable = (translationCode: string): boolean => {
  return !!TRANSLATION_RESOURCES[translationCode];
};

export const HOST_LOCALE_MAPPING = {
  es: LOCALES.ES,
  fr: LOCALES.FR,
  it: LOCALES.IT,
  jp: LOCALES.JA,
  kr: LOCALES.KO,
};

export const getLanguageCodeByHost = (host: string): LocaleType => {
  const subdomain = host?.split('.')?.[0];

  if (subdomain === 'www' || subdomain === 'benzinga') {
    return DEFAULT_LANGUAGE;
  }

  if (isTranslationAvailable(HOST_LOCALE_MAPPING[subdomain])) {
    return HOST_LOCALE_MAPPING[subdomain];
  }

  return DEFAULT_LANGUAGE;
};

export const setLanguageByHost = async (host: string, namespaces?: string[]) => {
  const translationCode = getLanguageCodeByHost(host);

  if (translationCode) {
    return await setLanguage(translationCode, namespaces);
  }

  return false;
};

export const CONTENT_LANGUAGE = {
  es: 'es',
  in: 'en-IN',
  it: 'it',
  jp: 'ja',
  kr: 'ko',
} as const;
export const getResources = (language: string, targetResources?: Record<string, unknown>): Resource => {
  const resources = { ...TRANSLATION_RESOURCES };

  if (language !== LOCALES.EN && targetResources) {
    resources[language] = targetResources;
  } else if (language === LOCALES.EN) {
    resources[LOCALES.EN] = {
      ...resources[LOCALES.EN],
      ...targetResources,
    };
  }

  return resources;
};
