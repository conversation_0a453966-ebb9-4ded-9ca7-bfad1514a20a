import i18n from 'i18next';

import common_en from '../locales/en/common.json';
import auth_en from '../locales/en/auth.json';
import welcome_en from '../locales/en/welcome.json';

import { initReactI18next } from './commonExports';

export const DEFAULT_LANGUAGE = 'en';
export const DEFAULT_NAMESPACE = 'common';

export let language = DEFAULT_LANGUAGE;

if (typeof window !== 'undefined' && window['language']) {
  language = window['language'] ?? DEFAULT_LANGUAGE;
}

export const LOCALES = {
  EN: 'en',
  ES: 'es',
  FR: 'fr',
  IT: 'it',
  JA: 'ja',
  KO: 'ko',
} as const;

type LocaleKeys = keyof typeof LOCALES;
export type LocaleType = (typeof LOCALES)[LocaleKeys];
// console.log("LOAD i18n: ", language)

export const TRANSLATION_RESOURCES = {
  [LOCALES.EN]: {
    auth: auth_en,
    common: common_en,
    welcome: welcome_en,
  },
  [LOCALES.ES]: {},
  [LOCALES.FR]: {},
  [LOCALES.IT]: {},
  [LOCALES.KO]: {},
  [LOCALES.JA]: {},
};

i18n.use(initReactI18next).init({
  fallbackLng: 'en',
  fallbackNS: 'common',
  interpolation: { escapeValue: false },
  lng: language,
  resources: TRANSLATION_RESOURCES,
});
i18n.changeLanguage(language);

export default i18n;
