'use client';
import React, { useEffect } from 'react';
import styled from '@benzinga/themetron';
import { Dividend, TradeAlertsData } from '@benzinga/calendar-manager';
import { DelayedQuote } from '@benzinga/quotes-manager';
import { Table } from '@benzinga/table';
import { StockAlertsColumnsDef } from './StockAlerts/StockAlertsColumnsDef';
import { StockAlertPaywall } from './StockAlerts/StockAlertPaywall';
import { TradeAlertVideos } from '@benzinga/plus-manager';
import { StockAlertVideos } from './StockAlerts/StockAlertVideos';
import { EdgeHardIterations } from '@benzinga/auth-ui';
import { isMobile } from '@benzinga/device-utils';
import { Switch } from 'antd';
import { usePermission } from '@benzinga/user-context';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useSearchParams } from 'next/navigation';

export interface LayoutStockAlertProps {
  bypassPaywall?: boolean;
  paywall_label?: string;
  symbols?: string[];
  quotes?: DelayedQuote[];
  initialDividends?: Dividend[];
  tradeAlertsData?: TradeAlertsData[];
  tradeAlertVideos?: TradeAlertVideos;
}

interface PortfolioAlertData extends DelayedQuote, TradeAlertsData {}

export const LayoutStockAlert: React.FC<LayoutStockAlertProps> = ({
  bypassPaywall = false,
  quotes,
  tradeAlertsData,
  tradeAlertVideos,
}) => {
  const getDataSource = (quotes, tradeAlertsDataQuotes) => {
    const data: PortfolioAlertData[] = quotes.map(stock => {
      const symbol = stock.symbol;

      const sellingPrice =
        tradeAlertsDataQuotes[symbol]?.exit_price > 0
          ? tradeAlertsDataQuotes[symbol]?.exit_price
          : stock.lastTradePrice;

      const buyingPrice = Number(tradeAlertsDataQuotes[symbol]?.entry_price.replace('$', ''));

      const current_return = Number(((sellingPrice - buyingPrice) / buyingPrice) * 100).toFixed(2);

      let positionStatus = '';
      if (tradeAlertsDataQuotes[symbol]?.exit_price > 0) {
        positionStatus = Number(current_return) > 0 ? 'closed-position-positive' : 'closed-position-negative';
      }

      return {
        ...stock,
        current_return,
        positionStatus,
        ...tradeAlertsDataQuotes[symbol],
      };
    });
    return data;
  };

  const searchParams = useSearchParams();
  const slug = searchParams?.get('slug')?.replaceAll('-', '_');
  const session = React.useContext(SessionContext);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const [isPaywallActive, setIsPaywallActive] = React.useState(false);
  const [onlyShowClosedPositions, setOnlyShowClosedPositions] = React.useState<boolean>(false);
  const [dataToDisplay, setDataToDisplay] = React.useState<PortfolioAlertData[]>(
    getDataSource(quotes, tradeAlertsData),
  );
  const isMobileDevice = isMobile();

  useEffect(() => {
    if (!hasPermission && !bypassPaywall) {
      setIsPaywallActive(true);
      session.getManager(TrackingManager).trackPaywallEvent('view', {
        paywall_id: 'stock-alert-table-benzinga-edge',
        paywall_type: 'soft',
        placement: slug ?? 'screener',
      });
    }
  }, [hasPermission, bypassPaywall, session, slug]);

  const onChangeClosedPosition = React.useCallback(
    (flag: boolean) => {
      if (flag) {
        const filteredArray = dataToDisplay?.filter(function (itm) {
          return itm?.exit_price && Number(itm?.exit_price) > 0;
        });
        setDataToDisplay(filteredArray);
      } else {
        const data = getDataSource(quotes, tradeAlertsData);
        setDataToDisplay(data);
      }
      setOnlyShowClosedPositions(flag);
    },
    [dataToDisplay, quotes, tradeAlertsData],
  );

  return (
    <>
      <LayoutStockAlertContainer>
        {tradeAlertsData && (
          <>
            <Table
              className="stock-alert-table font-manrope"
              columnsDef={StockAlertsColumnsDef({ isPaywallActive })}
              rowData={dataToDisplay}
            />
            {isPaywallActive && !isMobileDevice && (
              <div className="absolute top-1/2 w-full -translate-y-2/4">
                <StockAlertPaywall />
              </div>
            )}
          </>
        )}
      </LayoutStockAlertContainer>
      {!isPaywallActive && (
        <div className="float-right">
          <Switch checked={onlyShowClosedPositions} onChange={onChangeClosedPosition} />
          <span className="px-2">Closed Position Only</span>
        </div>
      )}
      {isMobileDevice && isPaywallActive && (
        <EdgeHardIterations authMode="login" contentType="rankings-page" iterationVersion={3} />
      )}

      {tradeAlertVideos && tradeAlertVideos?.videos?.length > 0 && (
        <div className="stock-alert-videos">
          <StockAlertVideos isPaywallActive={isPaywallActive} videos={tradeAlertVideos?.videos} />
        </div>
      )}
    </>
  );
};

const LayoutStockAlertContainer = styled.div`
  width: 100%;
  display: block;
  clear: both;
  border: 1px solid #e1ebfa;
  border-radius: 4px;
  max-height: 700px;
  position: relative;

  .benzinga-core-table-wrapper {
    height: 700px !important;
    max-height: 700px !important;
    overflow-x: auto;
    @media (max-width: 567px) {
      height: 600px !important;
      max-height: 600px !important;
    }
    table {
      width: 100%;
      margin-bottom: 0.2rem;
      table-layout: auto;
      display: table;
      height: 100%;
      font-family: 'Manrope', 'Inter', 'Open Sans', sans-serif;
    }
  }

  thead {
    border-bottom: 1px solid #e1ebfa;

    tr {
      text-align: left;
      background: #f2f8ff;
      th {
        font-size: 16px;
        color: #5b7292;
        padding: 12px 10px 12px 12px !important;
        min-width: 120px;
        &:first-of-type {
          border: none;
        }
        &:nth-child(6) {
          max-width: 140px;
          span {
            text-align: left;
            white-space: break-spaces;
          }
        }
        .header-sort-icons {
          gap: 2px;
        }

        &:last-of-type {
          text-align: left;
          border: none;
        }
      }
    }
  }

  tbody {
    overflow: auto;

    tr {
      border-collapse: collapse;
      box-sizing: inherit;
      border-bottom: 1px solid #e1ebfa;
      width: 100%;
      background: white;

      td {
        font-size: 16px;
        padding: 6px 10px 6px 0;
        color: ${({ theme }) => theme.colorPalette.black};
        &:first-of-type {
          .cell-item {
            border: none;
          }
        }
        &:nth-child(6) {
          max-width: 80px;
        }
        &.ticker-data {
          img {
            width: 35px;
          }
          .company-info {
            a {
              color: #000000;
            }
            .company-name {
              padding: 2px 4px;
              border-radius: 4px;
              background: #e1ebfa;
              font-size: 12px;
              line-height: 16px;
            }
            .industry {
              font-size: 0.8rem;
              color: #395173;
            }
          }
        }
        &.closed-position-positive {
          background: rgba(48, 191, 96, 0.4);
        }
        &.closed-position-negative {
          background: rgba(255, 64, 80, 0.4);
        }

        &.price {
          .change-percent {
            padding: 0px 4px;
            border-radius: 4px;
          }
        }
        &.button-wrap {
          width: 120px;
        }
      }
      &:nth-child(odd),
      &:nth-child(even):hover {
        background: white;
      }
      &:last-of-type {
        border: none;
      }
    }
  }
`;
