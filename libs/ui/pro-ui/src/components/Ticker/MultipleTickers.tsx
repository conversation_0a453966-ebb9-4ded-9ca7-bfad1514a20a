'use client';
import React, { RefObject } from 'react';

import styled from '@benzinga/themetron';
import { deepEqual, Optional } from '@benzinga/utils';

import { StockSymbol, UnixTimestamp } from '@benzinga/session';
import { BaseQuote, StoryCategory } from '@benzinga/advanced-news-manager';
import MultipleTickerHover from './MultipleTickerHover';
import { ContextMenu, linkingContextOption, openWithContextOption, watchlistContextOption } from '../ContextMenu';
import { SessionContext } from '@benzinga/session-context';
import { TickerSelectedEvent, WidgetLinkingManager } from '@benzinga/widget-linking';
import { WatchlistManager } from '@benzinga/watchlist-manager';
import Hooks from '@benzinga/hooks';
import { ProContext } from '@benzinga/pro-tools';

interface Props {
  baseQuotes?: Record<StockSymbol, Optional<BaseQuote, 'symbol'>>;
  isFresh: boolean;
  isHoverShowAll?: boolean;
  limit: number;
  referenceTime?: UnixTimestamp;
  render: (symbol: string) => JSX.Element;
  renderMore: (extraTickers: number) => JSX.Element;
  symbols?: StoryCategory[];
  onSymbolClick: (symbol: StockSymbol) => void;
}

const shouldUpdate = (prevProps: React.PropsWithChildren<Props>, nextProps: React.PropsWithChildren<Props>) => {
  return !(
    !deepEqual(prevProps.baseQuotes, nextProps.baseQuotes) ||
    !deepEqual(prevProps.symbols, nextProps.symbols) ||
    prevProps.onSymbolClick !== nextProps.onSymbolClick ||
    prevProps.children !== nextProps.children
  );
};

export const MultipleTickers: React.FC<Props> = React.memo(props => {
  const [isHovered, setIsHovered] = React.useState<boolean>(false);
  const ref: RefObject<HTMLDivElement> = React.useRef<HTMLDivElement>(null);
  const session = React.useContext(SessionContext);
  const proContext = React.useContext(ProContext);
  const linkingManager = session.getManager(WidgetLinkingManager);
  const watchlistManager = session.getManager(WatchlistManager);

  const handleMouseEnter = React.useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = React.useCallback(() => {
    setIsHovered(false);
  }, []);

  const stopPropagation = React.useCallback((e: React.MouseEvent<HTMLDivElement>) => e.stopPropagation(), []);

  const sortedSymbols = React.useMemo(() => {
    // We're doing a stable sort manually here because R.sortWith / R.sortBy do not provide the behavior of _.orderBy.
    let primaryIndex = 0;
    return (
      props.symbols?.reduce<StoryCategory[]>((accumulator, symbol) => {
        if (symbol.primary) {
          accumulator.splice(primaryIndex++, 0, symbol);
          return accumulator;
        } else {
          return [...accumulator, symbol];
        }
      }, []) ?? []
    );
  }, [props.symbols]);

  const [widgetLinks, setWidgetLinks] = React.useState(() => linkingManager.getWidgetLinks());
  Hooks.useSubscriber(linkingManager, e => {
    switch (e.type) {
      case 'linking:link_updated': {
        setWidgetLinks(linkingManager.getWidgetLinks());
        break;
      }
    }
  });

  if (props.symbols === undefined || props.symbols.length === 0) {
    return null;
  }

  const extraTickers = props.symbols.length > props.limit ? props.symbols.length - props.limit : 0;

  return (
    <MultipleTickerDiv
      hasShadow={props.isFresh}
      onClick={stopPropagation}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      ref={ref}
    >
      {sortedSymbols.slice(0, props.limit).map(ticker => {
        const displayedSymbolName = ticker.name ?? '';
        return (
          <StyledMultipleTickerHover
            baseQuotes={props.baseQuotes}
            displayedSymbolName={displayedSymbolName}
            isHovered={isHovered}
            isHoverShowAll={props.isHoverShowAll}
            key={ticker.name}
            onSymbolClick={props.onSymbolClick}
            referenceTime={props.referenceTime}
            refHeight={ref.current?.offsetHeight}
            sortedSymbols={sortedSymbols}
          >
            <StyledContextMenu
              menu={[
                linkingContextOption(widgetLinks, linkId => {
                  const feed = linkingManager.getWidgetLinkFeedByID(linkId);
                  feed?.pushEvent({
                    symbol: ticker.name,
                    type: 'ticker_selected',
                  } as TickerSelectedEvent);
                }),
                watchlistContextOption(ticker.name, watchlistManager),
                openWithContextOption(ticker.name, proContext.addWidgetWithSymbol),
              ]}
            >
              <MultipleTickerWrapper>
                {displayedSymbolName && props.render(displayedSymbolName)}
                {extraTickers > 0 && props.renderMore(extraTickers)}
              </MultipleTickerWrapper>
            </StyledContextMenu>
          </StyledMultipleTickerHover>
        );
      })}
    </MultipleTickerDiv>
  );
}, shouldUpdate);

const MultipleTickerDiv = styled.div<{ hasShadow: boolean }>`
  cursor: pointer;
  display: flex;
  flex-wrap: wrap;
  position: relative;
  width: 100%;
  ${props => (props.hasShadow ? 'text-shadow: black 0.01em 0.05em 0.05em;' : undefined)}
`;

const MultipleTickerWrapper = styled.div`
  display: flex;
  width: 100%;
`;

const StyledContextMenu = styled(ContextMenu)`
  width: 100%;
`;

const StyledMultipleTickerHover = styled(MultipleTickerHover)`
  width: 100%;
`;
