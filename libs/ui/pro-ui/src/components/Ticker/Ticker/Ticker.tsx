'use client';
import React from 'react';
import { StockSymbol, UnixTimestamp } from '@benzinga/session';
import { BaseQuote } from '@benzinga/advanced-news-manager';
import { Quote } from '@benzinga/quotes-manager';
import styled, { TC } from '@benzinga/themetron';
import { isEmpty, isString, numberShorthand, Optional } from '@benzinga/utils';
import { PerformanceColor } from '../PerformanceColor';
import TickerSnippet from '../TickerSnippet';
import { TickerNotes } from '../Modules/TickerNote';
import { TickerContext, TickerContextProvider } from '../TickerContext';
import { TickerHeader } from '../Modules/TickerHeader';
import { useDetailedQuote, useQuoteSubscription } from '@benzinga/quotes-manager-hooks';
import { PermissionedComponent } from '@benzinga/user-context';
import { Sparkline } from '../../Sparkline';

export interface TickerHoverModules {
  changeSincePublish?: Optional<BaseQuote, 'symbol'>;
  chart?: {
    referenceTime?: UnixTimestamp;
  };
  notesDisabled?: boolean;
}

export interface Props {
  isOnlySymbol?: boolean;
  modules: TickerHoverModules;
  onSymbolClick: (symbol: StockSymbol) => void;
  onDialogModeChange?: (isFocused: boolean) => void;
  onTickerIsHovering?: (isHovering: boolean) => void;
  setTooltipHeight?: (tickerHeight: number) => void;
  symbol: StockSymbol;
}

const shouldUpdate = (prevProps: React.PropsWithChildren<Props>, nextProps: React.PropsWithChildren<Props>) => {
  return !(
    prevProps.symbol !== nextProps.symbol ||
    prevProps.modules !== nextProps.modules ||
    prevProps.isOnlySymbol !== nextProps.isOnlySymbol
  );
};

const Ticker: React.FC<Props> = React.memo(props => {
  const tickerDivRef = React.useRef<HTMLDivElement | null>(null);
  const [dialogMode, setDialogMode] = React.useState<boolean>(false);
  const ticker = React.useContext(TickerContext);
  const shareFloat = useDetailedQuote(props.symbol)?.ok?.sharesFloat;
  const { quote } = useQuoteSubscription(props.symbol);

  const currentPrice = useQuoteSubscription(props.symbol)?.quote?.currentPriceFormatted;
  const setDialogModeCall = React.useCallback(
    (isModel: boolean) => {
      setDialogMode(isModel);
      const onDialogModeChange = props.onDialogModeChange;
      onDialogModeChange?.(isModel);
    },
    [props.onDialogModeChange],
  );

  const updateHeight = React.useCallback(() => {
    const setTooltipHeight = props.setTooltipHeight;
    if (setTooltipHeight && tickerDivRef.current?.clientHeight) {
      setTooltipHeight(tickerDivRef.current.clientHeight);
    }
  }, [props.setTooltipHeight]);

  const onTickerMouseOverEvent = () => {
    const { onTickerIsHovering } = props;
    if (onTickerIsHovering) onTickerIsHovering(true);
  };

  const onTickerMouseLeaveEvent = () => {
    const { onTickerIsHovering } = props;
    if (onTickerIsHovering) onTickerIsHovering(false);
  };

  const { isOnlySymbol, modules: { changeSincePublish = null, chart = null } = {}, symbol } = props;
  let changeSincePublishSection: React.ReactElement | null = null;

  if (changeSincePublish && isString(changeSincePublish.price) && isString(quote?.close)) {
    changeSincePublishSection = (
      <>
        <HorizontalRuleRow />
        <ChangeSincePublishModule currentPrice={quote?.close} storyPrice={changeSincePublish.price} />
      </>
    );
  }

  const tickerHover = (
    <TickerHoverDiv
      dialogMode={dialogMode}
      onMouseLeave={onTickerMouseLeaveEvent}
      onMouseOver={onTickerMouseOverEvent}
      ref={tickerDivRef}
    >
      <TickerHeader onSymbolClick={props.onSymbolClick} symbol={props.symbol} />
      {changeSincePublishSection}
      {chart && (
        <>
          <HorizontalRuleRow />
          <Sparkline referenceTime={chart.referenceTime} symbol={symbol} updateHeight={ticker.updateHeight} />
        </>
      )}
      <QuoteData>
        <div>Volume : {!quote?.volume || isEmpty([quote?.volume]) ? '--' : numberShorthand(quote?.volume)}</div>
        <div>Float : {!shareFloat || isEmpty([shareFloat]) ? '--' : numberShorthand(shareFloat)}</div>
      </QuoteData>
      {props.modules.notesDisabled ? null : (
        <PermissionedComponent permission={{ action: 'bzpro/widget/use', resource: 'watchlist' }}>
          {access => (access ? <TickerNotes symbol={props.symbol} /> : <></>)}
        </PermissionedComponent>
      )}
    </TickerHoverDiv>
  );

  return (
    <TickerContextProvider setDialogMode={setDialogModeCall} updateHeight={updateHeight}>
      <TickerDiv className="Ticker" data-test="ticker-pop-up">
        {isOnlySymbol ? (
          tickerHover
        ) : (
          <TickerSnippet
            dialogMode={dialogMode}
            onSymbolClick={props.onSymbolClick}
            percentChange={quote?.percentChange}
            price={currentPrice}
            symbol={symbol}
          >
            {tickerHover}
          </TickerSnippet>
        )}
      </TickerDiv>
    </TickerContextProvider>
  );
}, shouldUpdate);

const QuoteData = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  font-size: 1em;
  color: ${props => props.theme.colors.foregroundInactive};
  font-weight: 300;
  white-space: nowrap;
  line-height: 1.5;
  :first-child {
    font-weight: 500;
  }
  :last-child {
    font-weight: 500;
  }
`;

const TickerDiv = styled.div`
  display: table-row-group;
  cursor: pointer;
  :not(:last-child) {
    border-bottom: 1px solid ${props => props.theme.colors.background};
  }

  :hover {
    background: ${props => props.theme.colors.background};
  }
`;

const TickerHoverDiv = styled.div<{ dialogMode: boolean }>`
  background-color: ${props => props.theme.colors.backgroundActive};
  color: ${props => props.theme.colors.foreground};
  border-color: ${props => props.theme.colors.brand};
  border-width: ${props => (props.dialogMode ? 2 : 0)};
  display: flex;
  flex-direction: column;
  white-space: nowrap;
  z-index: 20000000;
`;

const PerformanceColorPaddingDiv = styled.div`
  padding-right: 0.25em;
`;

const HorizontalRuleRow = styled(TC.Row)`
  border-top: 1px solid ${props => props.theme.colors.border};
  height: 1px;
  width: 100%;
`;

interface ChangeSincePublishModuleProps {
  currentPrice: Quote['currentPriceFormatted'];
  storyPrice: BaseQuote['price'];
}

const ChangeSincePublishModule: React.FC<ChangeSincePublishModuleProps> = props => {
  let storyPercent = 0;
  let storyNominal = 0;

  if (props.storyPrice) {
    const publishQuotePrice = Number(props.storyPrice);
    storyNominal = Math.round((Number(props.currentPrice) - publishQuotePrice) * 100) / 100;
    storyPercent = (storyNominal / publishQuotePrice) * 100 || 0;
  }

  return (
    <ChangeSincePublishedContainerRow>
      <DescriptionTextDiv>Change Since Publish</DescriptionTextDiv>

      <ChangeSincePublishedInnerRow>
        <PerformanceColorPaddingDiv>
          <PerformanceColor hasArrow stat={storyNominal} />
        </PerformanceColorPaddingDiv>{' '}
        <PerformanceColorPaddingDiv>
          <PerformanceColor isPercentage stat={storyPercent} />
        </PerformanceColorPaddingDiv>
      </ChangeSincePublishedInnerRow>
    </ChangeSincePublishedContainerRow>
  );
};

const ChangeSincePublishedInnerRow = styled(TC.Row)`
  flex-grow: 1;
  font-size: 0.875em;
  justify-content: center;
`;

const ChangeSincePublishedContainerRow = styled(TC.Row)`
  font-size: 1em;
  padding: 5px 10px;
`;

const DescriptionTextDiv = styled(TC.Div)`
  color: ${props => props.theme.colors.foregroundInactive};
  font-size: 0.75em;
  font-weight: 300;
  white-space: nowrap;
`;

Ticker.displayName = 'Ticker';

export default Ticker;
