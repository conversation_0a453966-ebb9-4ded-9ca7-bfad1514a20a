import { ValueFormatterParams, ValueGetterParams } from '@ag-grid-community/core';
import { NO_VALUE } from '../../Ticker';
import { formatLarge, isNotNil } from '@benzinga/utils';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';

export const ShortenedNumberColDef = (colDef: AdvancedColDef): AdvancedColDef => ({
  cellStyle: { 'text-align': 'right' },
  filter: 'agNumberColumnFilter',
  valueFormatter: (data: ValueFormatterParams) => (isNotNil(data.value) ? formatLarge(Number(data.value)) : NO_VALUE),
  valueGetter: (params: ValueGetterParams) =>
    params.colDef.field && isNotNil(params.data[params.colDef.field]) ? Number(params.data[params.colDef.field]) : 0,
  ...colDef,
  ...absColDef(colDef),
});
