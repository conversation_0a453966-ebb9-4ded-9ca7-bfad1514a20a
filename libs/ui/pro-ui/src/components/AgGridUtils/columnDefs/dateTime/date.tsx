import { ColDef, ValueFormatterParams, ValueGetterParams } from '@ag-grid-community/core';
import { TimeFormat } from '@benzinga/date-utils';
import { DateTime } from 'luxon';
import { NO_VALUE } from '../../../Ticker';

const checkDateTime = (valueA: string, valueB: string): number => {
  const dateTimeA = DateTime.fromJSDate(new Date(valueA));
  const dateTimeB = DateTime.fromJSDate(new Date(valueB));

  if (!dateTimeA.isValid || !dateTimeB.isValid) {
    return 0;
  }

  if (dateTimeA < dateTimeB) {
    return -1;
  } else if (dateTimeA > dateTimeB) {
    return 1;
  }
  return 0;
};

export interface DateColDefTimeSettings {
  timeFormat: TimeFormat;
  timeOffset: number;
}

export const DateColDef = (colDef: ColDef, { timeOffset }: DateColDefTimeSettings): ColDef => ({
  cellStyle: { 'text-align': 'left' },
  comparator: checkDateTime,
  filter: 'agDateColumnFilter',
  filterParams: {
    comparator: checkDateTime,
  },
  valueFormatter: ({ value }: ValueFormatterParams): string => (value !== 0 ? value : NO_VALUE),
  valueGetter: ({ colDef: { field = 'time' }, data }: ValueGetterParams) => {
    const value = data[field];
    const time = DateTime.fromISO(value).plus({ minutes: timeOffset }).toFormat(`yyyy-MM-dd HH:mm:ss`);
    return time !== 'Invalid DateTime' ? time : value;
  },
  ...colDef,
});
