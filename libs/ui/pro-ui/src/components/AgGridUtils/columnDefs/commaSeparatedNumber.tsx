import { IRowNode, ValueFormatterParams } from '@ag-grid-community/core';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';

export const CommaSeparatedNumberColDef = (colDef: AdvancedColDef): AdvancedColDef => ({
  cellStyle: { 'text-align': 'left' },

  // renderer handles
  comparator: (_valueA: number, _valueB: number, nodeA: IRowNode, nodeB: IRowNode) =>
    nodeA.data[nodeA.id ?? ''] - nodeB.data[nodeB.id ?? ''],

  filter: 'agNumberColumnFilter',
  valueFormatter: (data: ValueFormatterParams) => String(data.value)?.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
  ...colDef,
  ...absColDef(colDef),
});
