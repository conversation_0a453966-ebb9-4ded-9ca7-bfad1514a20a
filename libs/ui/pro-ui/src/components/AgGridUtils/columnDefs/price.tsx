import styled, { Theme, keyframes } from '@benzinga/themetron';
import { ColDef, ValueFormatterParams } from '@ag-grid-community/core';
import React from 'react';
import Hooks from '@benzinga/hooks';
import { NO_VALUE } from '../../Ticker';
import { CustomCellRendererProps } from '@ag-grid-community/react';
import { formatLarge, isNil, isNotNil } from '@benzinga/utils';
import { AdvancedColDef, absColDef } from '../helpers/advancedHeader';

const AnimateChangeRenderer: React.FC<CustomCellRendererProps> = props => {
  const prevValue = Hooks.usePrevious(props.value);

  const getHolder = (lastValue: number | null, value: number) => {
    switch (Math.sign(lastValue ? value - lastValue : 0)) {
      case -1:
        return DownFlash;
      case 1:
        return UpFlash;
      default:
        return NoneFlash;
    }
  };

  const Holder = prevValue ? getHolder(prevValue, props.value) : NoneFlash;
  if (isNil(props.value)) {
    return <Holder>{NO_VALUE}</Holder>;
  } else {
    return <Holder>{props.valueFormatted}</Holder>;
  }
};

const FlashRed = ({ theme }: { theme: Theme }) => keyframes`
  0% {
    background-color: ${theme.colors.statistic.negative}C0;
    color: ${theme.colors.foreground};
  }
  50% {
    background-color: ${theme.colors.statistic.negative}C0;
    color: ${theme.colors.foreground};
  }
  100% {
    background-color: inherit;
    color: inherit;
  }
`;

const FlashGreen = ({ theme }: { theme: Theme }) => keyframes`
  0% {
    background-color: ${theme.colors.statistic.positive}C0;
    color: ${theme.colors.foreground};
  }
  50% {
    background-color: ${theme.colors.statistic.positive}C0;
    color: ${theme.colors.foreground};
  }
  100% {
    background-color: inherit;
    color: inherit;
  }
`;

const FlashBase = styled.div`
  padding: 0 6px;
  /* text-align: left; */
`;

const UpFlash = styled(FlashBase)`
  animation: ${FlashGreen} 0.5s linear;
`;

const DownFlash = styled(FlashBase)`
  animation: ${FlashRed} 0.5s linear;
`;

const NoneFlash = styled(FlashBase)``;

export const PriceColDef = (colDef: AdvancedColDef): ColDef => ({
  cellRenderer: AnimateChangeRenderer,
  cellStyle: { padding: '0', 'text-align': 'right' },

  // renderer handles
  filter: 'agNumberColumnFilter',
  valueFormatter: ({ value }: ValueFormatterParams) =>
    isNotNil(value)
      ? value < 1 && value !== 0
        ? `$${Number(value).toFixed(5)}`
        : `$${formatLarge(Number(value))}`
      : NO_VALUE,
  ...colDef,
  ...absColDef(colDef),
});
