import { getCellValue } from '../helpers';
import { absColDef, AdvancedColDef } from '../helpers/advancedHeader';
import { comparators } from '@benzinga/utils';

export const NumberColDef = (colDef: AdvancedColDef): AdvancedColDef => ({
  cellStyle: { 'text-align': 'right' },
  comparator: comparators.numberComparator,
  filter: 'agNumberColumnFilter',
  valueFormatter: params => getCellValue(params),
  valueGetter: params => params.data[colDef.field ?? colDef.colId ?? ''],
  ...colDef,
  ...absColDef(colDef),
});
