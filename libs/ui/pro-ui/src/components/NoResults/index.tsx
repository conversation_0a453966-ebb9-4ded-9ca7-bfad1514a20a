'use client';
import React from 'react';

import styled from '@benzinga/themetron';
import { openWindow } from '@benzinga/frontend-utils';
import { BenzingaContext } from '@benzinga/user-context';

interface Props {
  hideSearchTips?: boolean;
  message?: React.ReactNode;
}

export const NoResults: React.FC<Props> = ({ hideSearchTips, message }) => {
  const newIntercomWindow = React.useCallback(() => {
    const url = '/helpchat/';
    const name = 'Benzinga Pro Help Chat';
    const options = 'width=425,height=550,left=0,resizable';
    openWindow(url, name, options, undefined, { free: true });
  }, []);

  const { contactPhoneNumber } = React.useContext(BenzingaContext);

  return (
    <NoResultsContainer>
      <NoResultsTitle>{message ?? 'No results found.'}</NoResultsTitle>
      {!hideSearchTips && (
        <Tips>
          <Header>Search Tips:</Header>
          <Ul>
            <Li>Make your search as concise as possible.</Li>
            <Li>Ensure words are spelled correctly.</Li>
            <Li>Try less specific keywords.</Li>
          </Ul>
        </Tips>
      )}
      <Resources>
        <Header>Not finding what you need?</Header>
        <SupportButton onClick={newIntercomWindow}>chat with support</SupportButton>
        <SupportTextContainer>
          <SupportText>Contact support by clicking above</SupportText>
          <SupportText>or call us at {contactPhoneNumber}.</SupportText>
        </SupportTextContainer>
      </Resources>
    </NoResultsContainer>
  );
};

const NoResultsContainer = styled.div({
  fontSize: '15px',
  marginLeft: '3em',
  marginTop: '2em',
  textAlign: 'left',
});

const NoResultsTitle = styled.div({
  fontSize: '1.25em',
  fontWeight: 'lighter',
  paddingBottom: '10px',
});

const Tips = styled.div({
  fontWeight: 'lighter',
  marginTop: '2em',
});

const Header = styled.div({
  fontSize: '1em',
});

const Ul = styled.ul({
  marginTop: '0.5em',
});

const Li = styled.li({
  marginTop: '0.25em',
});

const Resources = styled.div({
  marginTop: '2em',
});

const SupportButton = styled.button`
  &:hover {
    background-color: ${({ theme }) => theme.colors.brand} !important;
  }

  background-color: ${({ theme }) => theme.colors.brandMuted} !important;
  color: ${({ theme }) => theme.colors.brandForeground} !important;
  cursor: pointer !important;
  display: inline-block !important;
  font-size: 1em !important;
  margin-top: 1em !important;
  padding-bottom: 0.75em !important;
  padding-left: 1.5em !important;
  padding-right: 1.5em !important;
  padding-top: 0.75em !important;
  position: relative !important;
  text-align: center !important;
  text-transform: uppercase !important;
  white-space: nowrap !important;
`;

const SupportTextContainer = styled.div({
  marginTop: '1em',
});

const SupportText = styled.p({
  fontWeight: 'lighter',
  marginBottom: 0,
});
