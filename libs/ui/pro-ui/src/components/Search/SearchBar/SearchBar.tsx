'use client';
import React, { ChangeEvent, ClipboardEvent, DragEvent, KeyboardEvent, MouseEvent, ReactNode } from 'react';

import Hooks from '@benzinga/hooks';
import styled, { css, TC } from '@benzinga/themetron';
import { isArray, toTitleCase } from '@benzinga/utils';
import { copyTextToClipboard, pasteTextFromClipboard } from '@benzinga/frontend-utils';
import { useDrag, useDrop } from 'react-dnd';

import { calculateTextWidth } from '../../../utils/html';
import { DynamicWidthInput } from './SearchBar.styled';
import { getEndOfGroup, getStartOfGroup, groupOperator } from './group';
import { genExistingSearchItems } from './existingItems';
import { isNoCommaString, ModuleState, SearchModule, TagModule } from '../Modules';
import { SearchItem } from '@benzinga/search-modules';
import { useAutocomplete, useStatefulAutocomplete } from '../hooks';
import { SearchContext } from '../context';
import { safeJsonParse } from '@benzinga/safe-await';
import { Spinner } from '@benzinga/core-ui';
import { usePermission } from '@benzinga/user-context';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SearchTool } from './Tools/SearchBarTool';
import { ChevronDown, Lock } from '@benzinga/themed-icons';

export type DropdownComponent = React.FC<{ isSelected: boolean; isLocked?: boolean }>;

export interface BaseDropdownItem {
  component: DropdownComponent;
  locked?: boolean;
  id: string;
  type: string;
}

export interface AutocompleteDropdownItem<T = unknown> extends BaseDropdownItem {
  searchItem: SearchItem<T>;
  type: 'AutocompleteDropdown';
}

export function isAutocompleteDropdownItem(item: BaseDropdownItem): item is AutocompleteDropdownItem {
  return item.type === 'AutocompleteDropdown';
}

export interface ActionDropdownItem extends BaseDropdownItem {
  action?: (indexedExpression: IndexedExpression) => [IndexedExpression, Partial<State>];
  type: 'ActionDropdown';
}

export function isActionDropdownItem(item: BaseDropdownItem): item is ActionDropdownItem {
  return item.type === 'ActionDropdown';
}

export const dropdownDescriptionHelper =
  (type: string, label: string): DropdownComponent =>
  ({ isLocked, isSelected }) => {
    return (
      <>
        {isLocked && <Locked />}
        <SearchResultHead isSelected={isSelected}>{toTitleCase(type)}</SearchResultHead>
        <SearchResultDescription isSelected={isSelected}>{label}</SearchResultDescription>
      </>
    );
  };

interface Props {
  displayOr?: boolean;
  excludeAnd?: boolean;
  expression: TokenizedExpression[];

  iconOverride?: React.ReactNode;
  initExpandedMode?: boolean;
  minimumSize?: number;
  modules: SearchModule[];
  newsConfigSelector?: React.ElementType | null;
  /**
   *   A string to display as placeholder text if no tags were added and the input is empty.
   */
  placeholder: string;
  thinUI?: boolean;
  tools?: SearchTool[];

  onDropDownStateChange?(isOpen: boolean): void;
  onExpressionUpdate(expression: TokenizedExpression[]): void;
  onLockedItemsSelected(token: BaseDropdownItem): void;
}

export type SearchBarProps = Props;

export enum KeyboardKey {
  ArrowDown = 'ArrowDown',
  ArrowLeft = 'ArrowLeft',
  ArrowRight = 'ArrowRight',
  ArrowUp = 'ArrowUp',
  Backspace = 'Backspace',
  Comma = ',',
  Delete = 'Delete',
  Enter = 'Enter',
  End = 'End',
  Esc = 'Escape',
  Home = 'Home',
}

interface State {
  defaultOperator: 'or' | 'and';
  defaultOperatorNot: boolean;
  enterKeyPressed: boolean;
  expandedMode: boolean;
  highlightIndex: number;
  horizontalOverflow: boolean;
  inFocus: boolean;
  input: string;
  inputIndex: number;
  isOpen: boolean;
  copied: boolean;
  orSelected: boolean;
  parsedExpression: TokenizedExpression[];
  toggledModules: (SearchModule & { enabled: boolean })[];
}

export type SearchBarState = State;

export interface IndexedExpression {
  index: number;
  expressions: TokenizedExpression[];
}

export interface ExpressionToken<T = unknown> extends SearchItem<T> {
  operator: 'expression';
}

export type TokenizedExpression<T = unknown> =
  | { id?: string; groupIndex: number; operator: 'group_start' }
  | { id?: string; groupIndex: number; operator: 'group_end' }
  | { id?: string; groupIndex: number; operator: 'not_start' }
  | { id?: string; groupIndex: number; operator: 'not_end' }
  | { id?: string; operator: 'position' }
  | { id?: string; operator: 'or' }
  | { id?: string; operator: 'and' }
  | ExpressionToken<T>;

/**
 * An interface for autocompletes where you are building a list out of the
 *   completions the user selects.
 * Can handle pasting lists of tags and drag-and-dropping csv files.
 */

export interface SearchBarForwardRef {
  blur: () => void;
  focus: () => void;
}

const SearchBarInner = (props: React.PropsWithChildren<Props>, ref: React.ForwardedRef<SearchBarForwardRef>) => {
  const searchBarRef = React.useRef<HTMLDivElement | null>(null);
  const searchBarTagsRef = React.useRef<HTMLDivElement | null>(null);
  const selectedDropDown = React.useRef<BaseDropdownItem[]>([]);
  const searchContext = React.useContext(SearchContext);
  const session = React.useContext(SessionContext);

  const advancedQueryPermission = usePermission('bzpro/newsfeed/advancedQuery', '#');

  props = {
    excludeAnd: props.excludeAnd || !advancedQueryPermission,
    minimumSize: 46,
    ...props,
  };
  const NewsConfigSelector = React.useMemo(() => {
    if (props?.newsConfigSelector) return props?.newsConfigSelector;
    return null;
  }, [props?.newsConfigSelector]);
  const parseExpression = React.useCallback(
    (tokens: TokenizedExpression[]): TokenizedExpression[] => {
      let appearedTokens: ExpressionToken<unknown>[] = [];
      let itemRemoved = false;
      const onExpressionUpdate = props.onExpressionUpdate;
      const expr = [
        ...tokens
          .flatMap(value => {
            if (value.operator === 'expression') {
              if (appearedTokens.some(token => token.id === value.id && token.moduleId === value.moduleId)) {
                itemRemoved = true;
                return [];
              } else {
                appearedTokens.push(value);
              }
            } else if (
              value.operator === 'group_start' ||
              value.operator === 'group_end' ||
              value.operator === 'not_start' ||
              value.operator === 'not_end'
            ) {
              appearedTokens = [];
            }
            return [value];
          })
          .flatMap((value, index) => [{ id: `position_${index}`, operator: 'position' } as TokenizedExpression, value]),
        { id: `position_${tokens.length}`, operator: 'position' } as TokenizedExpression,
      ];
      if (itemRemoved) {
        onExpressionUpdate(expr);
      }
      return expr;
    },
    [props.onExpressionUpdate],
  );

  const [state, setState] = Hooks.useStateCallback<State>(() => {
    const parsedExpression = parseExpression(props.expression);
    return {
      copied: false,
      defaultOperator: 'or' as const,
      defaultOperatorNot: false,
      enterKeyPressed: false,
      expandedMode: props.initExpandedMode ?? false,
      highlightIndex: -1,
      horizontalOverflow: false,
      inFocus: false,
      input: '',
      inputIndex: parsedExpression.length - 1,
      isOpen: false,
      orSelected: false,
      parsedExpression,
      toggledModules: props.modules.map(module => ({ ...module, enabled: true })),
    };
  });

  const oldIsOpen = Hooks.usePrevious(state.isOpen);
  React.useEffect(() => {
    if (oldIsOpen !== state.isOpen) {
      const onDropDownStateChange = props.onDropDownStateChange;
      onDropDownStateChange?.(state.isOpen);
    }
  }, [oldIsOpen, props.onDropDownStateChange, state.isOpen]);

  const tools = React.useMemo(
    () => props.tools?.map(toolStruct => toolStruct.getTool(props, state, setState)) ?? [],
    [props, setState, state],
  );

  React.useEffect(() => {
    const dropdownStateChange = props.onDropDownStateChange;
    dropdownStateChange?.(state.isOpen);
  }, [props.onDropDownStateChange, state.isOpen]);

  const horizontalVal = Hooks.useScrollBarVisible(searchBarTagsRef.current).horizontal;
  React.useEffect(() => {
    setState(old => ({ ...old, horizontalOverflow: horizontalVal }));
  }, [horizontalVal, setState]);

  React.useEffect(() => {
    setState(old => {
      return {
        ...old,
        toggledModules: props.modules.map(module => {
          const oldModule = old.toggledModules.find(oldModule => oldModule.id === module.id);
          return {
            ...module,
            enabled: oldModule?.enabled ?? true,
          };
        }),
      };
    });
  }, [props.modules, setState]);

  const autocompleteModules = React.useMemo(
    () => state.toggledModules.filter(module => module.enabled).map(module => module.autocomplete),
    [state.toggledModules],
  );

  const getModule = React.useCallback(
    (key: string) => state.toggledModules.find(module => module.id === key),
    [state.toggledModules],
  );

  const parsedExpression = state.parsedExpression;

  const existingSearchItems = React.useMemo<ExpressionToken<unknown>[]>(
    () => genExistingSearchItems({ expressions: parsedExpression, index: state.inputIndex }),
    [parsedExpression, state.inputIndex],
  );

  const focusInput = React.useCallback(() => {
    if (INPUT.current) {
      INPUT.current.focus();
    }
  }, []);

  const focus = React.useCallback(
    () =>
      setState(old =>
        old.inFocus !== true || old.isOpen !== true ? { ...old, highlightIndex: -1, inFocus: true, isOpen: true } : old,
      ),
    [setState],
  );

  const blur = React.useCallback(() => {
    setState(old => (old.inFocus === true || old.isOpen === true ? { ...old, inFocus: false, isOpen: false } : old));
  }, [setState]);

  React.useImperativeHandle(ref, () => ({
    blur,
    focus,
  }));

  const { search, searchResults, searchResultsReady } = useStatefulAutocomplete({
    autocompleteModules,
    queryLimit: 10,
  });

  const { search: pasteItemSearch } = useAutocomplete({
    autocompleteModules,
    queryLimit: 1,
  });

  const autocompleteDropdowns = React.useMemo(() => {
    return searchResults.flatMap(searchItem => {
      const dropdown = getModule(searchItem.moduleId)?.autocomplete.dropdown(searchItem);
      if (dropdown) return [dropdown];
      else return [];
    });
  }, [getModule, searchResults]);

  const onClick = React.useCallback(() => {
    focusInput();
    focus();
    search(state.input, existingSearchItems);
  }, [existingSearchItems, focus, focusInput, search, state.input]);

  React.useEffect(() => {
    if (state.inFocus) {
      focusInput();
    }
  }, [focusInput, state.inFocus]);

  const updateExpression = React.useCallback(
    ({ expressions, index }: IndexedExpression, state: Partial<State> = {}) => {
      setState(
        old => ({
          ...old,
          ...state,
          inputIndex: index,
        }),
        () => {
          focusInput();
        },
      );
      const onExpressionUpdate = props.onExpressionUpdate;
      onExpressionUpdate(expressions);
    },
    [focusInput, props.onExpressionUpdate, setState],
  );

  React.useEffect(() => {
    const expressions = parseExpression(props.expression);
    setState(old => {
      const index = old.inputIndex >= expressions.length ? expressions.length - 1 : old.inputIndex;
      if (old.input === '' && props.expression.length > 0) {
        search(undefined, genExistingSearchItems({ expressions, index }));
      } else {
        search(old.input, genExistingSearchItems({ expressions, index }));
      }

      return {
        ...old,
        inputIndex: index,
        isOpen: false,
        parsedExpression: expressions,
      };
    });
  }, [parseExpression, props.expression, setState, search]);

  const groupTag = React.useCallback(({ expressions, index }: IndexedExpression, not: boolean): IndexedExpression => {
    expressions.splice(
      index - 1,
      2,
      { operator: 'position' },
      { groupIndex: 9999, operator: not ? 'not_start' : 'group_start' },
      { operator: 'position' },
      expressions[index],
      { operator: 'position' },
      {
        groupIndex: 9999,
        operator: not ? 'not_end' : 'group_end',
      },
    );
    return {
      expressions,
      index: index + 3,
    };
  }, []);

  const addTag = React.useCallback(
    ({ expressions, index }: IndexedExpression, tag: AutocompleteDropdownItem): IndexedExpression => {
      const defaultOperator = state.defaultOperator;

      // Remove tag if it already exists and move to end
      // possible bug: equals(index, -1) means that tags array does not include tag, nothing to remove then
      const moduleItem = getModule(tag.searchItem.moduleId)?.autocomplete.onSelect(tag.searchItem);

      if (!moduleItem) return { expressions, index };

      searchContext.addRecentModuleItem(moduleItem);

      session.getManager(TrackingManager).trackSearchEvent('search', moduleItem.moduleId, {
        query: state.input,
      });

      if (
        index === 0 ||
        expressions[index - 1].operator === 'group_start' ||
        expressions[index - 1].operator === 'not_start'
      ) {
        if (
          expressions.length <= 1 ||
          expressions[index + 1].operator === 'group_end' ||
          expressions[index + 1].operator === 'not_end'
        ) {
          expressions.splice(
            index,
            0,
            { operator: 'position' },
            {
              ...moduleItem,
              operator: 'expression' as const,
            },
          );
          return { expressions, index: index + 2 };
        } else {
          if (props.displayOr || defaultOperator === 'and') {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              {
                ...moduleItem,
                operator: 'expression' as const,
              },
              { operator: 'position' },
              { operator: defaultOperator },
            );
          } else {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              {
                ...moduleItem,
                operator: 'expression' as const,
              },
            );
          }

          if (state.defaultOperatorNot) {
            ({ expressions } = groupTag({ expressions, index: index + 1 }, true));
            index = +4;
          }
          const group = groupOperator(
            { expressions, index: index + 3 },
            props.displayOr ?? false,
            props.excludeAnd ?? false,
          );

          return {
            ...group,
            index: index + (props.displayOr || defaultOperator === 'and' ? 4 : 2) - (state.defaultOperatorNot ? 2 : 0),
          };
        }
      } else if (
        index >= expressions.length - 1 ||
        expressions[index + 1].operator === 'group_end' ||
        expressions[index + 1].operator === 'not_end'
      ) {
        const location = index >= expressions.length - 1 ? expressions.length - 1 : index;
        if (props.displayOr || defaultOperator === 'and') {
          expressions.splice(
            location,
            0,
            { operator: 'position' },
            { operator: defaultOperator },
            { operator: 'position' },
            {
              ...moduleItem,
              operator: 'expression' as const,
            },
          );
        } else {
          expressions.splice(
            location,
            0,
            { operator: 'position' },
            {
              ...moduleItem,
              operator: 'expression' as const,
            },
          );
        }

        if (state.defaultOperatorNot) {
          ({ expressions } = groupTag(
            { expressions, index: index + (props.displayOr || defaultOperator === 'and' ? 3 : 1) },
            true,
          ));
        }

        const group = groupOperator(
          { expressions, index: location + 1 },
          props.displayOr ?? false,
          props.excludeAnd ?? false,
        );
        return {
          ...group,
          index:
            group.index + (props.displayOr || defaultOperator === 'and' ? 3 : 1) + (state.defaultOperatorNot ? 2 : 0),
        };
      } else {
        const preOperator =
          expressions[index - 1].operator === 'group_start' ||
          expressions[index - 1].operator === 'not_start' ||
          expressions[index - 1].operator === 'or' ||
          expressions[index - 1].operator === 'and';
        const postOperator =
          index >= expressions.length - 1 ||
          expressions[index + 1].operator === 'group_end' ||
          expressions[index + 1].operator === 'not_end' ||
          expressions[index + 1].operator === 'or' ||
          expressions[index + 1].operator === 'and';

        if (preOperator || !postOperator) {
          if (props.displayOr || defaultOperator === 'and') {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              {
                ...moduleItem,
                operator: 'expression' as const,
              },
              { operator: 'position' },
              { operator: defaultOperator },
            );
          } else {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              {
                ...moduleItem,
                operator: 'expression' as const,
              },
            );
          }
        } else {
          if (props.displayOr || defaultOperator === 'and') {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              { operator: defaultOperator },
              { operator: 'position' },
              {
                ...moduleItem,
                operator: 'expression' as const,
              },
            );
          } else {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              {
                ...moduleItem,
                operator: 'expression' as const,
              },
            );
          }
        }

        if (state.defaultOperatorNot) {
          if (preOperator || !postOperator) {
            ({ expressions } = groupTag({ expressions, index: index + 1 }, true));
            index += 4;
          } else {
            ({ expressions } = groupTag(
              { expressions, index: index + (props.displayOr || defaultOperator === 'and' ? 3 : 1) },
              true,
            ));
          }
        }

        const operatorIndex = index + (preOperator || !postOperator ? 3 : 1);
        const group = groupOperator(
          { expressions, index: operatorIndex },
          props.displayOr ?? false,
          props.excludeAnd ?? false,
        );
        return { ...group, index: group.index + (preOperator || !postOperator ? 1 : 3) };
      }
    },
    [
      getModule,
      groupTag,
      props.displayOr,
      props.excludeAnd,
      searchContext,
      session,
      state.defaultOperator,
      state.defaultOperatorNot,
      state.input,
    ],
  );

  const addGroup = React.useCallback(
    ({ expressions, index }: IndexedExpression): IndexedExpression => {
      const defaultOperator = state.defaultOperator;
      if (expressions[index].operator === 'position') {
        if (expressions.length === 1) {
          expressions.splice(
            0,
            0,
            { operator: 'position' },
            { groupIndex: 99999, operator: 'group_start' },
            { operator: 'position' },
            { groupIndex: 99999, operator: 'group_end' },
          );
          return { expressions, index: index + 2 };
        } else {
          const indexExpression = expressions[index - 1];
          const nextIndexExpression = expressions[index + 1];

          if (expressions.length - 1 === index) {
            if (props.displayOr || defaultOperator === 'and') {
              expressions.splice(
                index,
                0,
                { operator: 'position' },
                { operator: defaultOperator },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_start' },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_end' },
              );
            } else {
              expressions.splice(
                index,
                0,
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_start' },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_end' },
              );
            }
            return {
              expressions,
              index: index + (props.displayOr || defaultOperator === 'and' ? 4 : 2),
            };
          } else if (indexExpression?.operator === 'group_start' && nextIndexExpression?.operator === 'group_end') {
            expressions.splice(
              index,
              0,
              { operator: 'position' },
              { groupIndex: 99999, operator: 'group_start' },
              { operator: 'position' },
              { groupIndex: 99999, operator: 'group_end' },
            );
            return { expressions, index: index + 2 };
          } else if (nextIndexExpression?.operator === 'group_end') {
            if (props.displayOr || defaultOperator === 'and') {
              expressions.splice(
                index,
                0,
                { operator: 'position' },
                { operator: defaultOperator },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_start' },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_end' },
              );
            } else {
              expressions.splice(
                index,
                0,
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_start' },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_end' },
              );
            }

            return {
              expressions,
              index: index + (props.displayOr || defaultOperator === 'and' ? 4 : 2),
            };
          } else {
            if (props.displayOr || defaultOperator === 'and') {
              expressions.splice(
                index,
                0,
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_start' },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_end' },
                { operator: 'position' },
                { operator: defaultOperator },
              );
            } else {
              expressions.splice(
                index,
                0,
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_start' },
                { operator: 'position' },
                { groupIndex: 99999, operator: 'group_end' },
              );
            }
            return { expressions, index: index + (defaultOperator === 'or' ? 0 : 2) };
          }
        }
      }
      return { expressions, index };
    },
    [props.displayOr, state.defaultOperator],
  );

  const removeItem = React.useCallback(
    ({ expressions, index }: IndexedExpression): IndexedExpression => {
      expressions = expressions ?? parsedExpression;
      const expression = expressions[index];

      if (expression.operator === 'position') {
        console.error('cant remove position Item');
      } else if (expression.operator === 'and' || expression.operator === 'or') {
        console.error('cant remove and/or Item');
      }

      const IndexExpressionIndex = expression.operator === 'and' || expression.operator === 'or' ? index - 2 : index;

      let previousPreviousIndexExpression = expressions[IndexExpressionIndex - 4];
      let previousIndexExpression = expressions[IndexExpressionIndex - 2];
      const indexExpression = expressions[IndexExpressionIndex];
      const nextIndexExpression = expressions[IndexExpressionIndex + 2];
      const nextNextIndexExpression = expressions[IndexExpressionIndex + 4];

      if (index !== 0) {
        if (indexExpression.operator === 'expression') {
          if (previousIndexExpression === undefined) {
            expressions.splice(IndexExpressionIndex - 1, 2);
            return { expressions, index: 0 };
          } else if (previousIndexExpression.operator === 'or' || previousIndexExpression.operator === 'and') {
            expressions.splice(IndexExpressionIndex - 3, 4);
            return { expressions, index: index - 3 };
          } else {
            expressions.splice(IndexExpressionIndex - 1, 2);
            return { expressions, index: index - 1 };
          }
        } else if (
          (indexExpression.operator === 'group_start' && nextIndexExpression.operator === 'group_end') ||
          (indexExpression.operator === 'not_start' && nextIndexExpression.operator === 'not_end')
        ) {
          if (previousIndexExpression?.operator === 'and' || previousIndexExpression?.operator === 'or') {
            expressions.splice(IndexExpressionIndex - 2, 6);
            return { expressions, index: index - 3 };
          } else if (nextNextIndexExpression?.operator === 'and' || nextNextIndexExpression?.operator === 'or') {
            expressions.splice(IndexExpressionIndex - 1, 6);
            return { expressions, index: index - 1 };
          } else {
            expressions.splice(IndexExpressionIndex - 1, 4);
            return { expressions, index: index - 1 };
          }
        } else if (indexExpression.operator === 'group_end' || indexExpression.operator === 'not_end') {
          const startIndex = expressions.findIndex(
            i =>
              (i.operator === 'group_start' || i.operator === 'not_start') &&
              i.groupIndex === indexExpression.groupIndex,
          );

          previousPreviousIndexExpression = expressions[startIndex - 2];
          previousIndexExpression = expressions[startIndex];

          if (nextIndexExpression?.operator === 'and' || nextIndexExpression?.operator === 'or') {
            expressions.splice(startIndex - 1, IndexExpressionIndex - startIndex + 4);
            return { expressions, index: startIndex - 1 };
          } else if (
            previousPreviousIndexExpression?.operator === 'and' ||
            previousPreviousIndexExpression?.operator === 'or'
          ) {
            expressions.splice(startIndex - 3, IndexExpressionIndex - startIndex + 4);
            return { expressions, index: startIndex - 3 };
          } else {
            expressions.splice(startIndex - 1, IndexExpressionIndex - startIndex + 2);
            return { expressions, index: startIndex - 1 };
          }
        }
      }
      return { expressions, index };
    },
    [parsedExpression],
  );

  const copyGroup = React.useCallback(
    (groupStart: IndexedExpression) => {
      setState(s => ({
        ...s,
        inFocus: false,
        isOpen: false,
        orSelected: false,
      }));
      const groupEnd = getEndOfGroup(groupStart);
      const expr = groupStart.expressions.slice(groupStart.index, groupEnd + 1);

      let copiedString = '';

      if (
        expr.every((token, index, arr) => {
          const module = token.operator === 'expression' ? getModule(token.moduleId) : undefined;
          return (
            //ignore initial and final grouping
            index === 0 ||
            index === arr.length - 1 ||
            token.operator === 'or' ||
            token.operator === 'position' ||
            (token.operator === 'expression' && (!module || module.clipboard.onCopyString))
          );
        })
      ) {
        // simple copy
        copiedString = expr
          .reduce((str, token) => {
            if (token.operator === 'expression') {
              const tokenStr = getModule(token.moduleId)?.clipboard.onCopyString?.(token);
              if (tokenStr) {
                return str + ',' + tokenStr;
              }
            }
            return str;
          }, '')
          .slice(1); //remove extra , from the start of the string
      } else {
        // complex copy
        const copiedData = expr.flatMap<TokenizedExpression>(token => {
          switch (token.operator) {
            case 'expression': {
              const copiedToken = getModule(token.moduleId)?.clipboard.onCopyJSON(token);
              return copiedToken ? copiedToken.map(token => ({ operator: 'expression', ...token })) : [];
            }
            case 'position':
              return [];
            default:
              return [token];
          }
        });

        copiedString = JSON.stringify(copiedData);
      }
      return copyTextToClipboard(copiedString);
    },
    [getModule, setState],
  );
  const pasteHandler = React.useCallback(
    async (evt?: ClipboardEvent<HTMLInputElement>) => {
      setState(old => ({ ...old, inFocus: false, isOpen: false, orSelected: false }));
      evt?.preventDefault();
      let clipData = evt?.clipboardData.getData('text');
      if (!clipData) {
        clipData = await pasteTextFromClipboard();
      }
      if (!clipData) return;
      const tokens = safeJsonParse<TokenizedExpression[]>(clipData);

      const expr: TokenizedExpression[] = [];

      if (!tokens.err && isArray(tokens.ok)) {
        //Complex paste, since we got an object, parse object and try to match with modules
        expr.push(
          ...tokens.ok.flatMap<TokenizedExpression>(token => {
            switch (token.operator) {
              case 'expression': {
                const parsedToken = getModule(token.moduleId)?.clipboard.onPasteJSON(token);
                return parsedToken ? [{ operator: 'expression', ...parsedToken }, { operator: 'position' }] : [];
              }
              default:
                return token;
            }
          }),
        );
      } else {
        //Simple paste, assuming comma separated string.
        const pasteStrings = clipData.split(/[\n,]+/).flatMap(str => {
          const trimStr = str.trim();
          return isNoCommaString(trimStr) && trimStr.length > 0 ? [trimStr] : [];
        });

        for (const str of pasteStrings) {
          //Try to match each string to a module.
          let token = state.toggledModules.reduce<SearchItem | undefined>((t, module) => {
            if (!t) {
              const result = module.clipboard.onPasteString?.(str);
              if (
                !existingSearchItems.some(
                  existingItem => existingItem.moduleId === result?.moduleId && existingItem.id === result?.id,
                )
              ) {
                t = result;
              }
            }
            return t;
          }, undefined);

          //If no module accepts the string as its own, do a general search and pick the top result.
          if (!token) {
            token = await pasteItemSearch(str, existingSearchItems).then(items => items?.[0]);
          }
          if (token) {
            expr.push({ operator: 'expression', ...token }, { operator: 'position' });
          }
        }
      }

      if (expr.length !== 0) {
        // remove extra position element from the end
        parsedExpression.splice(state.inputIndex, 0, ...expr.slice(0, -1));
        setState(old => ({ ...old, inputIndex: old.inputIndex + expr.length - 1 }));
        props.onExpressionUpdate(parsedExpression);
      }
    },
    [
      existingSearchItems,
      getModule,
      parsedExpression,
      pasteItemSearch,
      props,
      setState,
      state.inputIndex,
      state.toggledModules,
    ],
  );

  const getExpressionInputValue = React.useCallback(
    (expression: TokenizedExpression) => {
      if (expression.operator === 'expression') {
        const module = getModule(expression.moduleId);
        if (!module) return '';
        return module.tag.tagInputText(expression) ?? '';
      }
      return '';
    },
    [getModule],
  );
  const ChangePositionSelector = React.useCallback(
    (index: number, orSelected = false) => {
      const inputIndex = index >= parsedExpression.length ? parsedExpression.length - 1 : index;
      const expression = parsedExpression[inputIndex];
      const inputText = getExpressionInputValue(expression);
      search(inputText, existingSearchItems);

      setState(
        old => ({
          ...old,
          inFocus: true,
          input: inputText,
          inputIndex: inputIndex,
          isOpen: expression.operator !== 'position',
          orSelected,
        }),
        () => focusInput(),
      );
    },
    [parsedExpression, getExpressionInputValue, search, existingSearchItems, setState, focusInput],
  );

  const moveTag = React.useCallback(
    (expressionIndex: number, positionIndex: number) => {
      const expression = parsedExpression[expressionIndex];
      const inputIndex = state.inputIndex;

      if (expression.operator === 'expression') {
        const expressionPosition = parsedExpression[expressionIndex - 1];
        const nextExpression = parsedExpression[expressionIndex + 2];
        const previousExpression = parsedExpression[expressionIndex - 2];

        const moveInputLeft = expressionIndex > inputIndex && positionIndex < inputIndex;
        const moveInputRight = expressionIndex < inputIndex && positionIndex > inputIndex;

        const insertShift = expressionIndex < positionIndex;

        if (
          nextExpression === undefined ||
          nextExpression.operator === 'group_end' ||
          nextExpression.operator === 'not_end'
        ) {
          if (previousExpression?.operator === 'and' || previousExpression?.operator === 'or') {
            parsedExpression.splice(expressionIndex - 3, 4);
            parsedExpression.splice(positionIndex + (insertShift ? -4 : 0), 0, expressionPosition, expression);

            setState(old => ({
              ...old,
              inputIndex: moveInputLeft ? old.inputIndex : old.inputIndex - 2,
              isOpen: false,
            }));
          } else {
            parsedExpression.splice(expressionIndex - 1, 2);
            parsedExpression.splice(positionIndex + (insertShift ? -2 : 0), 0, expressionPosition, expression);

            setState(old => ({
              ...old,
              inputIndex: moveInputLeft ? old.inputIndex + 2 : moveInputRight ? old.inputIndex - 2 : old.inputIndex,
              isOpen: false,
              parsedExpression,
            }));
          }
        } else if (
          previousExpression === undefined ||
          previousExpression.operator === 'group_start' ||
          previousExpression.operator === 'not_start'
        ) {
          if (nextExpression?.operator === 'and' || nextExpression?.operator === 'or') {
            parsedExpression.splice(expressionIndex - 1, 4);
            parsedExpression.splice(positionIndex + (insertShift ? -4 : 0), 0, expressionPosition, expression);

            setState(old => ({
              ...old,
              inputIndex: moveInputLeft ? old.inputIndex + 2 : moveInputRight ? old.inputIndex - 2 : old.inputIndex,
              isOpen: false,
            }));
          } else {
            parsedExpression.splice(expressionIndex - 1, 2);
            parsedExpression.splice(positionIndex + (insertShift ? -2 : 0), 0, expressionPosition, expression);

            setState(old => ({
              ...old,
              inputIndex: moveInputLeft ? old.inputIndex + 2 : moveInputRight ? old.inputIndex - 2 : old.inputIndex,
              isOpen: false,
              parsedExpression,
            }));
          }
        } else {
          parsedExpression.splice(expressionIndex - 1, 2);
          parsedExpression.splice(positionIndex + (insertShift ? -2 : 0), 0, expressionPosition, expression);

          setState(old => ({
            ...old,
            inputIndex: moveInputLeft ? old.inputIndex + 2 : moveInputRight ? old.inputIndex - 2 : old.inputIndex,
            isOpen: false,
            parsedExpression,
          }));
        }

        focusInput();
      }
    },
    [focusInput, parsedExpression, setState, state.inputIndex],
  );

  const prependAndToTag = React.useCallback(
    ({ expressions, index }: IndexedExpression, doNotMoveInputIndex = false): IndexedExpression => {
      expressions.splice(index, 0, { operator: 'and' }, { operator: 'position' });
      const result = groupOperator({ expressions, index }, props.displayOr ?? false, props.excludeAnd ?? false);
      return {
        expressions: result.expressions,
        index: result.index + (doNotMoveInputIndex ? 0 : 2),
      };
    },
    [props.displayOr, props.excludeAnd],
  );

  const EditDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'expression':
            return [removeItem(expr), { highlightIndex: -1, inFocus: true, isOpen: true }];
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('edit', 'Edit item'),
      id: 'edit',
      type: 'ActionDropdown',
    }),
    [removeItem],
  );

  const DeleteDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'expression':
          case 'group_end':
          case 'not_end':
            return [removeItem(expr), { input: '' }];
          case 'group_start':
          case 'not_start': {
            const endIndex = getEndOfGroup(expr);
            return [removeItem({ expressions: expr.expressions, index: endIndex }), { input: '' }];
          }
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('delete', 'Delete item'),
      id: 'delete',
      type: 'ActionDropdown',
    }),
    [removeItem],
  );

  const GroupDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'expression':
            return [groupTag(expr, false), { input: '' }];
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('group', 'Surround item with parentheses'),
      id: 'group',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission, groupTag],
  );

  const NotDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'expression':
            return [groupTag(expr, true), { input: '' }];
          case 'and':
          case 'or': {
            const nextExpression = expr.expressions[expr.index + 2];
            if (nextExpression.operator === 'expression') {
              return [groupTag({ expressions: expr.expressions, index: expr.index + 2 }, true), { input: '' }];
            } else if (nextExpression.operator === 'group_start') {
              expr.expressions[expr.index + 2] = { ...nextExpression, operator: 'not_start' };
              return [expr, {}];
            }
          }
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('not', 'Exclude from result'),
      id: 'not',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission, groupTag],
  );

  const PreAndDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'expression':
          case 'group_start':
          case 'not_start':
            return [prependAndToTag(expr), { orSelected: false }];
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('pre and', 'Change previous operator to (and)'),
      id: 'pre-and',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission, prependAndToTag],
  );

  const PostAndDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'expression':
            return [
              prependAndToTag({ expressions: expr.expressions, index: expr.index + 2 }),
              {
                orSelected: false,
              },
            ];
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('post and', 'Change following operator to (and)'),
      id: 'post-and',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission, prependAndToTag],
  );

  const OrDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'and': {
            expr.expressions[expr.index].operator = 'or';
            const result = groupOperator(expr, props.displayOr ?? false, props.excludeAnd ?? false);
            return [{ ...result, index: props.displayOr === false ? result.index - 1 : result.index }, {}];
          }
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('or', 'Change operator to (or)'),
      id: 'or',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission, props.displayOr, props.excludeAnd],
  );

  const AndDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'or': {
            expr.expressions[expr.index].operator = 'and';
            return [groupOperator(expr, props.displayOr ?? false, props.excludeAnd ?? false), {}];
          }
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('and', 'Change operator to (and)'),
      id: 'and',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission, props.displayOr, props.excludeAnd],
  );

  const SwapDropdown = React.useCallback<(GroupToNot: boolean) => ActionDropdownItem>(
    (GroupToNot: boolean) => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'group_end':
          case 'not_end': {
            const startIndex = getStartOfGroup(expr);
            const startToken = expr.expressions[startIndex];
            const index2 = expr.index === -1 ? 0 : expr.index;
            if (startToken.operator === 'group_start') {
              expr.expressions[startIndex] = { ...startToken, operator: 'not_start' };
              expr.expressions[index2] = { ...token, operator: 'not_end' };
            } else if (startToken.operator === 'not_start') {
              expr.expressions[startIndex] = { ...startToken, operator: 'group_start' };
              expr.expressions[index2] = { ...token, operator: 'group_end' };
            }
            return [expr, {}];
          }
          case 'group_start':
          case 'not_start': {
            const endIndex = getEndOfGroup(expr);
            const endToken = expr.expressions[endIndex];
            const index2 = expr.index === -1 ? 0 : expr.index;
            if (endToken.operator === 'group_end') {
              expr.expressions[endIndex] = { ...endToken, operator: 'not_end' };
              expr.expressions[index2] = { ...token, operator: 'not_start' };
            } else if (endToken.operator === 'not_end') {
              expr.expressions[endIndex] = { ...endToken, operator: 'group_end' };
              expr.expressions[index2] = { ...token, operator: 'group_start' };
            }
            return [expr, {}];
          }
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('swap', GroupToNot ? 'Change group to (not)' : 'Change not to (group)'),
      id: 'swap',
      locked: !advancedQueryPermission,
      type: 'ActionDropdown',
    }),
    [advancedQueryPermission],
  );

  const CopyDropdown = React.useMemo<ActionDropdownItem>(
    () => ({
      action: expr => {
        const token = expr.expressions[expr.index];
        switch (token.operator) {
          case 'group_end':
          case 'not_end':
            copyGroup({ ...expr, index: getStartOfGroup(expr) });
            break;
          case 'group_start':
          case 'not_start':
            copyGroup(expr);
            break;
        }
        return [expr, {}];
      },
      component: dropdownDescriptionHelper('copy', 'Copy expression'),
      id: 'copy',
      type: 'ActionDropdown',
    }),
    [copyGroup],
  );

  const expressionNoOperatorDropDown = React.useMemo<ActionDropdownItem[]>(
    () =>
      props.excludeAnd ? [EditDropdown, DeleteDropdown] : [EditDropdown, GroupDropdown, NotDropdown, DeleteDropdown],
    [DeleteDropdown, EditDropdown, GroupDropdown, NotDropdown, props.excludeAnd],
  );

  const expressionPreOperatorDropDown = React.useMemo<ActionDropdownItem[]>(
    () =>
      props.excludeAnd
        ? [EditDropdown, DeleteDropdown]
        : [EditDropdown, GroupDropdown, NotDropdown, PreAndDropdown, DeleteDropdown],
    [DeleteDropdown, EditDropdown, GroupDropdown, NotDropdown, PreAndDropdown, props.excludeAnd],
  );

  const expressionPrePostOperatorDropDown = React.useMemo<ActionDropdownItem[]>(
    () =>
      props.excludeAnd
        ? [EditDropdown, DeleteDropdown]
        : [EditDropdown, GroupDropdown, NotDropdown, PostAndDropdown, PreAndDropdown, DeleteDropdown],
    [DeleteDropdown, EditDropdown, GroupDropdown, NotDropdown, PostAndDropdown, PreAndDropdown, props.excludeAnd],
  );

  const expressionPostOperatorDropDown = React.useMemo<ActionDropdownItem[]>(
    () =>
      props.excludeAnd
        ? [EditDropdown, DeleteDropdown]
        : [EditDropdown, GroupDropdown, NotDropdown, PostAndDropdown, DeleteDropdown],
    [DeleteDropdown, EditDropdown, GroupDropdown, NotDropdown, PostAndDropdown, props.excludeAnd],
  );

  const andOperatorDropDown = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [] : [OrDropdown, NotDropdown]),
    [NotDropdown, OrDropdown, props.excludeAnd],
  );

  const orOperatorDropDown = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [] : [AndDropdown, NotDropdown]),
    [AndDropdown, NotDropdown, props.excludeAnd],
  );

  const andOperatorDropDownGroupNext = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [] : [OrDropdown]),
    [OrDropdown, props.excludeAnd],
  );

  const orOperatorDropDownGroupNext = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [] : [AndDropdown]),
    [AndDropdown, props.excludeAnd],
  );

  const groupOrDropDown = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [DeleteDropdown] : [SwapDropdown(true), PreAndDropdown, DeleteDropdown]),
    [DeleteDropdown, PreAndDropdown, SwapDropdown, props.excludeAnd],
  );

  const notOrDropDown = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [DeleteDropdown] : [SwapDropdown(false), PreAndDropdown, DeleteDropdown]),
    [DeleteDropdown, PreAndDropdown, SwapDropdown, props.excludeAnd],
  );

  const groupDropDown = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [DeleteDropdown] : [SwapDropdown(true), DeleteDropdown, CopyDropdown]),
    [CopyDropdown, DeleteDropdown, SwapDropdown, props.excludeAnd],
  );

  const notDropDown = React.useMemo<ActionDropdownItem[]>(
    () => (props.excludeAnd ? [DeleteDropdown] : [SwapDropdown(false), DeleteDropdown, CopyDropdown]),
    [CopyDropdown, DeleteDropdown, SwapDropdown, props.excludeAnd],
  );

  const preventCompletion = React.useRef<boolean>(false);
  const INPUT = React.useRef<HTMLInputElement | null>(null);

  const onExpressionUpdate = React.useCallback(
    (expression: TokenizedExpression[]) => {
      const onExpressionUpdate = props.onExpressionUpdate;
      onExpressionUpdate(expression);
    },
    [props.onExpressionUpdate],
  );

  const handleMouseEnter = React.useCallback(
    (index: number) => () => {
      setState(old => ({ ...old, highlightIndex: index }));
    },
    [setState],
  );

  const onTagDrag = React.useCallback(() => {
    setState(old => ({
      ...old,
      inFocus: false,
    }));
  }, [setState]);

  const onTagDrop = React.useCallback(
    (onTarget: boolean) => {
      if (onTarget) {
        onExpressionUpdate(parsedExpression);
        setState(old => ({
          ...old,
          inFocus: true,
        }));
      } else {
        const parsedExpression = parseExpression(props.expression);
        setState(old => ({
          ...old,
          inFocus: true,
          inputIndex: old.inputIndex >= parsedExpression.length ? parsedExpression.length - 1 : old.inputIndex,
          parsedExpression,
        }));
      }
    },
    [onExpressionUpdate, parseExpression, parsedExpression, props.expression, setState],
  );

  const handleSelect = React.useCallback(
    (index: number) => (evt?: MouseEvent<HTMLElement>) => {
      evt?.stopPropagation();
      const highlightIndex = index === -1 ? 0 : index;
      const selectedItem = selectedDropDown.current[highlightIndex];

      if (selectedItem.locked) {
        const onLockedItemsSelected = props.onLockedItemsSelected;
        onLockedItemsSelected(selectedItem);
        return;
      }
      const indexedExpression = { expressions: parsedExpression, index: state.inputIndex };

      if (isActionDropdownItem(selectedItem) && selectedItem.action) {
        updateExpression(...selectedItem.action(indexedExpression));
      } else if (isAutocompleteDropdownItem(selectedItem)) {
        updateExpression(addTag(indexedExpression, selectedItem), {
          input: '',
          isOpen: false,
          orSelected: false,
        });
      }
    },
    [state.inputIndex, props.onLockedItemsSelected, parsedExpression, updateExpression, addTag],
  );

  const onDrop = React.useCallback((evt: DragEvent<HTMLInputElement>) => {
    evt.preventDefault();

    // if (evt.dataTransfer && evt.dataTransfer.files) {
    //   onFileDrop?.(evt.dataTransfer.files, state.inputIndex / 2 + 1);
    // }
  }, []);

  const onInputChange = React.useCallback(
    (evt: ChangeEvent<HTMLInputElement>) => {
      evt.persist();
      const { value } = evt.currentTarget;
      const { inputIndex } = state;

      if (
        !props.excludeAnd &&
        parsedExpression[inputIndex].operator === 'position' &&
        (value === '[' || value === '(' || value === '{')
      ) {
        updateExpression(addGroup({ expressions: parsedExpression, index: inputIndex }));
      } else {
        setState(
          old => ({
            ...old,
            input: value,
          }),
          () => {
            if (
              parsedExpression[inputIndex].operator === 'position' ||
              parsedExpression[inputIndex].operator === 'expression'
            ) {
              if (value === '' && props.expression.length > 0) {
                search(undefined, existingSearchItems);
              } else {
                search(value, existingSearchItems);
              }
            }
          },
        );
      }
    },
    [
      search,
      props.excludeAnd,
      props.expression,
      state,
      parsedExpression,
      updateExpression,
      addGroup,
      setState,
      existingSearchItems,
    ],
  );

  const handleOperatorClick = (index: number) => (evt: MouseEvent<HTMLElement>) => {
    evt.stopPropagation();
    setState(old => ({
      ...old,
      highlightIndex: -1,
      inFocus: true,
      inputIndex: index,
      isOpen: true,
    }));
  };

  const onChangeDefaultOperator = React.useCallback(
    (_evt: MouseEvent<HTMLElement>) => {
      setState(old => ({
        ...old,
        defaultOperator: old.defaultOperator === 'or' ? 'and' : 'or',
        defaultOperatorNot: old.defaultOperator === 'and' ? !old.defaultOperatorNot : old.defaultOperatorNot,
      }));
      focusInput();
    },
    [focusInput, setState],
  );

  const handlePositionSelectorClick = (index: number) => (evt: MouseEvent<HTMLElement>) => {
    evt.stopPropagation();
    ChangePositionSelector(index);
  };

  const onChangeToAndClick = React.useCallback(
    (_evt: MouseEvent<HTMLElement>) => {
      setState(old => ({ ...old, orSelected: true }));
    },
    [setState],
  );

  const onKeyPress = React.useCallback(
    (evt: KeyboardEvent<HTMLInputElement>) => {
      if (KeyboardKey.Enter === evt.key) {
        // Only prevent default if we are selecting an autocomplete option.
        preventCompletion.current = true;
        evt.preventDefault();
        setState(s => ({ ...s, enterKeyPressed: true }));
      } else {
        setState(old => ({ ...old, highlightIndex: -1, isOpen: true }));
      }
    },
    [setState],
  );

  const prevEnterKeyPressed = Hooks.usePrevious(state.enterKeyPressed);
  const prevSearchResultsReady = Hooks.usePrevious(searchResultsReady);

  React.useEffect(() => {
    if ((!prevEnterKeyPressed || !prevSearchResultsReady) && state.enterKeyPressed && searchResultsReady) {
      setState(s => ({ ...s, enterKeyPressed: false }));
      const expression = parsedExpression[state.inputIndex];
      if (
        (expression.operator === 'position' && searchResults.length !== 0 && state.isOpen) ||
        expression.operator !== 'position'
      ) {
        handleSelect(state.highlightIndex)();
      }
    }
  }, [
    prevEnterKeyPressed,
    handleSelect,
    state.isOpen,
    parsedExpression,
    searchResults.length,
    prevSearchResultsReady,
    setState,
    state.enterKeyPressed,
    state.highlightIndex,
    state.inputIndex,
    searchResultsReady,
  ]);

  const onKeyDown = (evt: KeyboardEvent<HTMLInputElement>) => {
    const text = evt.currentTarget.value;
    const { highlightIndex, inputIndex } = state;
    const lastCompletionsIndex = searchResults.length - 1;
    const thereIsNoNextIndex = highlightIndex >= lastCompletionsIndex;

    switch (evt.key) {
      case KeyboardKey.ArrowDown:
        if (searchResults.length === 0 && state.input === '') {
          search(state.input, existingSearchItems);
        }
        setState(old => ({
          ...old,
          highlightIndex: thereIsNoNextIndex ? 0 : old.highlightIndex + 1,
          isOpen: true,
        }));
        break;

      case KeyboardKey.ArrowUp:
        if (searchResults.length === 0 && state.input === '') {
          search(state.input, existingSearchItems);
        }
        setState(old => ({
          ...old,
          highlightIndex: old.highlightIndex <= 0 ? lastCompletionsIndex : old.highlightIndex - 1,
          isOpen: true,
        }));
        break;

      case KeyboardKey.ArrowLeft:
        if ((parsedExpression[inputIndex].operator !== 'position' || text === '') && inputIndex > 0) {
          ChangePositionSelector(inputIndex - 1);
        }
        break;

      case KeyboardKey.ArrowRight:
        if (
          (parsedExpression[inputIndex].operator !== 'position' || text === '') &&
          inputIndex + 1 < parsedExpression.length
        ) {
          ChangePositionSelector(inputIndex + 1);
        }
        break;
      case KeyboardKey.Backspace: {
        const expression = parsedExpression[inputIndex];
        if (expression.operator === 'position' && text === '' && inputIndex > 0) {
          updateExpression(removeItem({ expressions: parsedExpression, index: inputIndex - 1 }), { input: '' });
        }
        break;
      }
      case KeyboardKey.Delete: {
        const expression = parsedExpression[inputIndex];
        if (expression.operator === 'position' && text === '') {
          const nextExpression = parsedExpression[inputIndex + 1];
          if (nextExpression.operator === 'or' || nextExpression.operator === 'and') {
            updateExpression(removeItem({ expressions: parsedExpression, index: inputIndex + 3 }), { input: '' });
          } else if (nextExpression.operator === 'group_start' || nextExpression.operator === 'not_start') {
            const startIndex = parsedExpression.findIndex(
              i =>
                (i.operator === 'group_end' || i.operator === 'not_end') && i.groupIndex === nextExpression.groupIndex,
            );
            updateExpression(removeItem({ expressions: parsedExpression, index: startIndex + 1 }), { input: '' });
          } else if (nextExpression.operator === 'group_end' || nextExpression.operator === 'not_end') {
            break;
          } else {
            updateExpression(removeItem({ expressions: parsedExpression, index: inputIndex + 1 }), { input: '' });
          }
        }
        break;
      }
      case KeyboardKey.Home: {
        const expression = parsedExpression[inputIndex];
        if (!(expression.operator === 'position' && text !== '')) {
          ChangePositionSelector(0);
        }
        break;
      }
      case KeyboardKey.End: {
        const expression = parsedExpression[inputIndex];
        if (!(expression.operator === 'position' && text !== '')) {
          ChangePositionSelector(parsedExpression.length - 1);
        }
        break;
      }
      case KeyboardKey.Esc:
        setState(old => ({
          ...old,
          inFocus: false,
          isOpen: false,
        }));
        break;
    }
  };

  const renderTags = (start: number, end: number) => {
    return parsedExpression.slice(start, end).map((tag, index) => {
      return (
        <RenderTag
          handleOperatorClick={handleOperatorClick}
          handlePositionSelectorClick={handlePositionSelectorClick}
          index={index}
          key={`${tag.operator}-${(tag.id ?? start.toString()) + index}`}
          moveTag={moveTag}
          onTagDrag={onTagDrag}
          onTagDrop={onTagDrop}
          parsedExpression={parsedExpression}
          selectedIndex={state.inFocus ? state.inputIndex : undefined}
          start={start}
          tag={tag}
          tagModule={tag.operator === 'expression' ? getModule(tag.moduleId)?.tag : undefined}
        />
      );
    });
  };

  const renderItem = React.useCallback(
    (item: BaseDropdownItem, index: number) => {
      const highlightIndex = state.highlightIndex;

      return (
        <SearchResult
          isSelected={highlightIndex === index}
          key={`${item.id ?? item.type}`}
          onClick={handleSelect(index)}
          onMouseEnter={handleMouseEnter(index)}
        >
          {item.component({ isLocked: item.locked, isSelected: highlightIndex === index })}
        </SearchResult>
      );
    },
    [handleMouseEnter, handleSelect, state.highlightIndex],
  );

  const invisibleInput = (inputIndex: number, value: string) => (
    <InvisibleInput
      onChange={onInputChange}
      onClick={handlePositionSelectorClick(inputIndex)}
      onDrop={onDrop}
      onKeyDown={onKeyDown}
      onKeyPress={onKeyPress}
      onPaste={pasteHandler}
      ref={INPUT}
      value={value}
    />
  );

  const headerProps = React.useMemo<DropdownHeaderProps>(() => {
    const allSelected = state.toggledModules.length > 1 && state.toggledModules.every(module => module.enabled);
    return {
      itemClicked: (id: string) => {
        setState(old => ({
          ...old,
          toggledModules:
            id === 'all'
              ? old.toggledModules.map(module => ({ ...module, enabled: true }))
              : old.toggledModules.map(module =>
                  module.id === id ? { ...module, enabled: true } : { ...module, enabled: false },
                ),
        }));
      },
      items: [
        { id: 'all', selected: allSelected, state: 'production', text: 'All' },
        ...state.toggledModules.map(module => ({
          id: module.id,
          selected: allSelected ? false : module.enabled,
          state: module.state ?? 'production',
          text: module.id,
        })),
      ],
      visible: state.toggledModules.length > 1,
    };
  }, [setState, state.toggledModules]);

  const renderInput = (inputIndex: number) => {
    const { minimumSize, placeholder } = props;
    const { defaultOperator, defaultOperatorNot, input, isOpen } = state;
    const currentElement = INPUT.current;
    const item = parsedExpression[inputIndex];
    if (!item) {
      return null;
    }
    let width = '100%';

    switch (item.operator) {
      case 'position':
        {
          const shouldShowPlaceholder = parsedExpression.length === 1;
          const placeholderText = shouldShowPlaceholder ? placeholder : '';

          const startOfGroup =
            inputIndex === 0 ||
            parsedExpression[inputIndex - 1].operator === 'group_start' ||
            parsedExpression[inputIndex - 1].operator === 'not_start' ||
            parsedExpression[inputIndex - 1].operator === 'or' ||
            parsedExpression[inputIndex - 1].operator === 'and';
          const endOfGroup =
            inputIndex >= parsedExpression.length - 1 ||
            parsedExpression[inputIndex + 1].operator === 'group_end' ||
            parsedExpression[inputIndex + 1].operator === 'not_end' ||
            parsedExpression[inputIndex + 1].operator === 'or' ||
            parsedExpression[inputIndex + 1].operator === 'and';
          const showOperator =
            !startOfGroup &&
            !endOfGroup &&
            parsedExpression[inputIndex - 1].operator !== 'and' &&
            parsedExpression[inputIndex + 1].operator !== 'and';

          if (currentElement) {
            if (shouldShowPlaceholder && input === '') {
              let calculatedWidth = calculateTextWidth(placeholderText, currentElement);

              if (calculatedWidth < (minimumSize as number)) {
                calculatedWidth = minimumSize as number;
              }

              // need at least 6px to show the text line cursor
              width = `${calculatedWidth + 14}px`;
            } else {
              let calculatedWidth = calculateTextWidth(input, currentElement);

              if (calculatedWidth < (minimumSize as number)) {
                calculatedWidth = minimumSize as number;
              }

              // need at least 6px to show the text line cursor
              width = `${calculatedWidth + 26}px`;
            }
          }

          if (props.excludeAnd) {
            selectedDropDown.current = autocompleteDropdowns;
            return (
              <PositionInputSpan>
                {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                <SearchInput
                  addBorder={parsedExpression.length !== 1}
                  onChange={onInputChange}
                  onClick={onClick}
                  onDrop={onDrop}
                  onKeyDown={onKeyDown}
                  onKeyPress={onKeyPress}
                  onPaste={pasteHandler}
                  placeholder={placeholderText}
                  ref={INPUT}
                  value={input}
                  width={width}
                />
                {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                {isOpen && (
                  <TC.ConstrainedFloater
                    minWidth={'100%'}
                    positionLeft={null}
                    positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
                  >
                    <SearchDropDown>
                      <DropdownHeader
                        itemClicked={headerProps.itemClicked}
                        items={headerProps.items}
                        visible={headerProps.visible}
                      />
                      {searchResultsReady ? (
                        [...selectedDropDown.current.map(renderItem)]
                      ) : (
                        <div style={{ height: '100px' }}>
                          <Spinner />
                        </div>
                      )}
                    </SearchDropDown>
                  </TC.ConstrainedFloater>
                )}
              </PositionInputSpan>
            );
          } else if (state.orSelected) {
            const nextOperator = parsedExpression[inputIndex + 1]?.operator;
            const groupNext = nextOperator === 'group_start' || nextOperator === 'not_start';
            selectedDropDown.current = groupNext ? orOperatorDropDownGroupNext : orOperatorDropDown;
            return (
              <PositionInputSpan>
                <AddPositionSelectorSpan />
                <SearchOperator selected>{'Or'}</SearchOperator>
                <SearchOperator noLeftBorder noPadding selected>
                  <ChevronDown />
                </SearchOperator>
                {invisibleInput(inputIndex, 'or')}
                {isOpen && (
                  <TC.ConstrainedFloater
                    minWidth={'100%'}
                    positionLeft={null}
                    positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
                  >
                    <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
                  </TC.ConstrainedFloater>
                )}
                <AddPositionSelectorSpan />
              </PositionInputSpan>
            );
          } else {
            selectedDropDown.current = autocompleteDropdowns;
            return (
              <PositionInputSpan>
                {showOperator && (
                  <>
                    {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                    <SearchOperator onClick={onChangeToAndClick}>Or</SearchOperator>
                    <SearchOperator noLeftBorder noPadding onClick={onChangeToAndClick}>
                      <ChevronDown />
                    </SearchOperator>
                  </>
                )}
                {!startOfGroup && endOfGroup ? (
                  <>
                    {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                    <SearchOperator onClick={onChangeDefaultOperator}>
                      {defaultOperatorNot ? toTitleCase(`${defaultOperator} Not`) : toTitleCase(defaultOperator)}
                    </SearchOperator>

                    {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                  </>
                ) : (
                  parsedExpression.length > 1 && <AddPositionSelectorSpan />
                )}
                <SearchInput
                  addBorder={parsedExpression.length !== 1}
                  onChange={onInputChange}
                  onClick={onClick}
                  onDrop={onDrop}
                  onKeyDown={onKeyDown}
                  onKeyPress={onKeyPress}
                  onPaste={pasteHandler}
                  placeholder={placeholderText}
                  ref={INPUT}
                  value={input}
                  width={width}
                />

                {!endOfGroup ? (
                  <>
                    {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                    <SearchOperator onClick={onChangeDefaultOperator}>
                      {defaultOperatorNot ? toTitleCase(`${defaultOperator} Not`) : toTitleCase(defaultOperator)}
                    </SearchOperator>

                    {parsedExpression.length > 1 && <AddPositionSelectorSpan />}
                  </>
                ) : (
                  parsedExpression.length > 1 && <AddPositionSelectorSpan />
                )}
                {isOpen && (
                  <TC.ConstrainedFloater
                    minWidth={'100%'}
                    positionLeft={null}
                    positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
                  >
                    <SearchDropDown>
                      <DropdownHeader
                        itemClicked={headerProps.itemClicked}
                        items={headerProps.items}
                        visible={headerProps.visible}
                      />
                      {searchResultsReady ? (
                        selectedDropDown.current.map(renderItem)
                      ) : (
                        <div style={{ display: 'flex', height: '100px' }}>
                          <Spinner />
                        </div>
                      )}
                    </SearchDropDown>
                  </TC.ConstrainedFloater>
                )}
              </PositionInputSpan>
            );
          }
        }
        break;
      case 'or':
      case 'and': {
        const nextOperator = parsedExpression[inputIndex + 2]?.operator;
        const groupNext = nextOperator === 'group_start' || nextOperator === 'not_start';
        selectedDropDown.current =
          item.operator === 'or'
            ? groupNext
              ? orOperatorDropDownGroupNext
              : orOperatorDropDown
            : groupNext
              ? andOperatorDropDownGroupNext
              : andOperatorDropDown;

        return (
          <InputSpan>
            <SearchOperator selected>{toTitleCase(item.operator)}</SearchOperator>
            <SearchOperator noLeftBorder noPadding selected>
              <ChevronDown />
            </SearchOperator>
            {invisibleInput(inputIndex, item.operator)}
            {isOpen && (
              <TC.ConstrainedFloater
                minWidth={'100%'}
                positionLeft={null}
                positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
              >
                <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
              </TC.ConstrainedFloater>
            )}
          </InputSpan>
        );
      }
      case 'expression': {
        if (currentElement) {
          // need at least 16px to show the text line cursor
          width = `${calculateTextWidth(getExpressionInputValue(item), currentElement) + 16}px`;
        }

        const hasPreviousOperator =
          inputIndex > 2 &&
          (parsedExpression[inputIndex - 2].operator === 'expression' ||
            parsedExpression[inputIndex - 2].operator === 'group_end' ||
            parsedExpression[inputIndex - 2].operator === 'not_end');

        const hasFollowingOperator =
          inputIndex < parsedExpression.length - 2 &&
          (parsedExpression[inputIndex + 2].operator === 'expression' ||
            parsedExpression[inputIndex + 2].operator === 'group_start' ||
            parsedExpression[inputIndex + 2].operator === 'not_start');

        let dropDown = expressionNoOperatorDropDown;
        if (!props.excludeAnd) {
          if (hasPreviousOperator && hasFollowingOperator) {
            dropDown = expressionPrePostOperatorDropDown;
          } else if (hasPreviousOperator) {
            dropDown = expressionPreOperatorDropDown;
          } else if (hasFollowingOperator) {
            dropDown = expressionPostOperatorDropDown;
          }
        }

        selectedDropDown.current = dropDown;
        selectedDropDown.current = selectedDropDown.current.concat(
          getModule(item.moduleId)?.action.dropdown(item) ?? [],
        );

        selectedDropDown.current = selectedDropDown.current.sort((a, b) => {
          if (a.locked && b.locked) {
            return 0;
          }
          if (a.locked) {
            return 1;
          }
          if (b.locked) {
            return -1;
          }
          return 0;
        });

        const renderer = getModule(item.moduleId)?.tag.render(item);
        const RenderedItem = (() => {
          if (renderer == null) {
            return <SearchTag> Unsupported Tag </SearchTag>;
          } else if (renderer.type === 'text') {
            return (
              <SearchTag>
                {renderer.icon ? <SearchTagIcon>{renderer.icon}</SearchTagIcon> : undefined} {renderer.text}
              </SearchTag>
            );
          } else {
            return renderer.jsx;
          }
        })();

        return (
          <InputSpan>
            {RenderedItem}
            {invisibleInput(inputIndex, input)}
            {isOpen && (
              <TC.ConstrainedFloater
                minWidth={'100%'}
                positionLeft={null}
                positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
              >
                <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
              </TC.ConstrainedFloater>
            )}
          </InputSpan>
        );
      }
      case 'not_start': {
        const hasHiddenOr =
          inputIndex > 2 &&
          (parsedExpression[inputIndex - 2].operator === 'expression' ||
            parsedExpression[inputIndex - 2].operator === 'group_end' ||
            parsedExpression[inputIndex - 2].operator === 'not_end');

        selectedDropDown.current = hasHiddenOr ? notOrDropDown : notDropDown;
        return (
          <InputSpan>
            <NotStartSpan onClick={handlePositionSelectorClick(inputIndex)} selected />
            {invisibleInput(inputIndex, '')}
            {isOpen && (
              <TC.ConstrainedFloater
                minWidth={'100%'}
                positionLeft={null}
                positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
              >
                <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
              </TC.ConstrainedFloater>
            )}
          </InputSpan>
        );
      }
      case 'group_start': {
        const hasHiddenOr =
          inputIndex > 2 &&
          (parsedExpression[inputIndex - 2].operator === 'expression' ||
            parsedExpression[inputIndex - 2].operator === 'group_end' ||
            parsedExpression[inputIndex - 2].operator === 'not_end');

        selectedDropDown.current = hasHiddenOr ? groupOrDropDown : groupDropDown;
        return (
          <InputSpan>
            <GroupStartSpan selected />
            {invisibleInput(inputIndex, '')}
            {isOpen && (
              <TC.ConstrainedFloater
                minWidth={'100%'}
                positionLeft={null}
                positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
              >
                <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
              </TC.ConstrainedFloater>
            )}
          </InputSpan>
        );
      }
      case 'group_end':
        selectedDropDown.current = groupDropDown;
        return (
          <InputSpan>
            <GroupEndSpan selected />
            {invisibleInput(inputIndex, '')}
            {isOpen && (
              <TC.ConstrainedFloater
                minWidth={'100%'}
                positionLeft={null}
                positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
              >
                <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
              </TC.ConstrainedFloater>
            )}
          </InputSpan>
        );

      case 'not_end':
        selectedDropDown.current = notDropDown;
        return (
          <InputSpan>
            <GroupEndSpan selected />
            {invisibleInput(inputIndex, '')}
            {isOpen && (
              <TC.ConstrainedFloater
                minWidth={'100%'}
                positionLeft={null}
                positionTop={searchBarRef.current?.getBoundingClientRect()?.bottom}
              >
                <SearchDropDown>{selectedDropDown.current.map(renderItem)}</SearchDropDown>
              </TC.ConstrainedFloater>
            )}
          </InputSpan>
        );
    }
  };

  const { inFocus, inputIndex } = state;

  Hooks.useClickOutside(searchBarRef, () => {
    setState(old =>
      old.inFocus !== false || old.isOpen !== false || old.orSelected !== false
        ? { ...old, inFocus: false, isOpen: false, orSelected: false }
        : old,
    );
  });

  const SearchWrapper = state.expandedMode ? WrappedSearchDisplay : SearchDisplay;
  const content = (
    <SearchStyle onClick={onClick} ref={searchBarRef}>
      {props.iconOverride ?? null}
      <SearchWrapper ref={searchBarTagsRef}>
        {inFocus || parsedExpression.length === 1 ? (
          <>
            {renderTags(0, inputIndex)}
            {renderInput(inputIndex)}
            {renderTags(inputIndex + 1, Infinity)}
          </>
        ) : (
          <>{renderTags(0, parsedExpression.length)}</>
        )}
      </SearchWrapper>
      {NewsConfigSelector && <NewsConfigSelector />}
      {[...tools]}
    </SearchStyle>
  );

  return props.thinUI ? <ThinSearchBarStyler>{content}</ThinSearchBarStyler> : content;
};

const mergeRefs = (...refs: any[]): any => {
  const filteredRefs = refs.filter(Boolean);
  if (!filteredRefs.length) return null;
  if (filteredRefs.length === 0) return filteredRefs[0];
  return (inst: any) => {
    for (const ref of filteredRefs) {
      if (typeof ref === 'function') {
        ref(inst);
      } else if (ref) {
        ref.current = inst;
      }
    }
  };
};

interface ExpressionRendererStringResult {
  text: string;
  node?: undefined;
}
interface ExpressionRendererReactNodeResult {
  text?: undefined;
  node: ReactNode;
}

export type ExpressionRendererResult = ExpressionRendererStringResult | ExpressionRendererReactNodeResult;

interface TagProps {
  index: number;
  tag: TokenizedExpression;
  start: number;
  selectedIndex: number | undefined;
  parsedExpression: TokenizedExpression[];
  moveTag: (expressionIndex: number, positionIndex: number) => void;
  onTagDrop: (onTarget: boolean) => void;
  onTagDrag: () => void;
  handlePositionSelectorClick: (index: number) => (evt: MouseEvent<HTMLElement>) => void;
  handleOperatorClick: (index: number) => (evt: MouseEvent<HTMLElement>) => void;
  tagModule?: TagModule;
}

const RenderTag = (props: TagProps) => {
  const selectedTag = props.selectedIndex ? props.parsedExpression[props.selectedIndex] : undefined;

  const [{ isDragging }, drag] = useDrag(
    () => ({
      collect: monitor => {
        return {
          isDragging: monitor.isDragging(),
        };
      },
      end: (_, monitor) => {
        props.onTagDrop(monitor.didDrop());
      },
      item: { id: props.tag.id, index: props.index, tag: props.tag },
      type: props.tag.operator,
    }),
    [props.tag.id, props.tag, props.tag.operator],
  );

  const [, drop] = useDrop(
    () => ({
      accept: 'expression',
      hover({ id: draggedId }: { id: string }) {
        const moveTag = props.moveTag;
        if (draggedId !== props.tag.id) {
          const expressionIndex = props.parsedExpression.findIndex(a => a.id === draggedId);
          const positionIndex = props.index + props.start;

          if (props.tag.operator === 'position') {
            moveTag(expressionIndex, positionIndex);
          } else {
            moveTag(expressionIndex, positionIndex + (expressionIndex < positionIndex ? 1 : -1));
          }
        }
      },
    }),
    [props.moveTag],
  );

  const previousIsDragging = Hooks.usePrevious(isDragging);

  React.useEffect(() => {
    if (isDragging && isDragging !== previousIsDragging) {
      props.onTagDrag();
    }
  }, [isDragging, previousIsDragging, props]);

  switch (props.tag.operator) {
    case 'position':
      if (props.index + props.start === props.parsedExpression.length - 1) {
        return (
          <AddPositionSelectorAutoWidth
            onClick={props.handlePositionSelectorClick(props.index + props.start)}
            ref={drop}
          />
        );
      } else {
        return (
          <AddPositionSelectorSpan onClick={props.handlePositionSelectorClick(props.index + props.start)} ref={drop} />
        );
      }
    case 'not_start': {
      const selected = selectedTag?.operator === 'not_end' && selectedTag?.groupIndex === props.tag.groupIndex;
      return (
        <NotStartSpan onClick={props.handlePositionSelectorClick(props.index + props.start)} selected={selected} />
      );
    }
    case 'group_start': {
      const selected = selectedTag?.operator === 'group_end' && selectedTag?.groupIndex === props.tag.groupIndex;
      return (
        <GroupStartSpan onClick={props.handlePositionSelectorClick(props.index + props.start)} selected={selected} />
      );
    }
    case 'not_end': {
      const selected = selectedTag?.operator === 'not_start' && selectedTag?.groupIndex === props.tag.groupIndex;
      return (
        <GroupEndSpan onClick={props.handlePositionSelectorClick(props.index + props.start)} selected={selected} />
      );
    }
    case 'group_end': {
      const selected = selectedTag?.operator === 'group_start' && selectedTag?.groupIndex === props.tag.groupIndex;
      return (
        <GroupEndSpan onClick={props.handlePositionSelectorClick(props.index + props.start)} selected={selected} />
      );
    }
    case 'or':
      return (
        <OperatorSpan>
          <SearchOperator onClick={props.handleOperatorClick(props.index + props.start)}>Or</SearchOperator>
          <SearchOperator noLeftBorder noPadding onClick={props.handleOperatorClick(props.index + props.start)}>
            <ChevronDown />
          </SearchOperator>
        </OperatorSpan>
      );
    case 'and':
      return (
        <OperatorSpan>
          <SearchOperator onClick={props.handleOperatorClick(props.index + props.start)}>And</SearchOperator>{' '}
          <SearchOperator noLeftBorder noPadding onClick={props.handleOperatorClick(props.index + props.start)}>
            <ChevronDown />
          </SearchOperator>
        </OperatorSpan>
      );
    case 'expression': {
      const renderer = props.tagModule?.render(props.tag);
      if (renderer == null) {
        return (
          <DraggingDiv
            onClick={props.handlePositionSelectorClick(props.index + props.start)}
            ref={mergeRefs(drag, drop)}
          >
            <SearchTag> Unsupported Tag </SearchTag>
          </DraggingDiv>
        );
      } else if (renderer.type === 'text') {
        return (
          <DraggingDiv
            onClick={props.handlePositionSelectorClick(props.index + props.start)}
            ref={mergeRefs(drag, drop)}
          >
            <SearchTag>
              {renderer.icon ? <SearchTagIcon>{renderer.icon}</SearchTagIcon> : undefined} {renderer.text}
            </SearchTag>
          </DraggingDiv>
        );
      } else {
        return (
          <DraggingDiv
            onClick={props.handlePositionSelectorClick(props.index + props.start)}
            ref={mergeRefs(drag, drop)}
          >
            {renderer.jsx}
          </DraggingDiv>
        );
      }
    }
  }
};

const NotStartSpan: React.FC<{
  onClick?: React.MouseEventHandler<HTMLSpanElement> | undefined;
  selected?: boolean;
}> = props => (
  <NotTag>
    <NotSearchOperator noRightBorder onClick={props.onClick} selected={props.selected}>
      Not
    </NotSearchOperator>
    <NotGroupStartSpan onClick={props.onClick} selected={props.selected} />
  </NotTag>
);

interface HeaderItem {
  id: string;
  text: string;
  selected: boolean;
  state: ModuleState;
}
interface DropdownHeaderProps {
  visible: boolean;
  items: HeaderItem[];
  itemClicked: (id: string) => void;
}

const ModuleStateTag: React.FC<{ state: ModuleState; selected: boolean }> = props => {
  switch (props.state) {
    case 'alpha':
    case 'beta':
    case 'deprecated':
      return <Tag selected={props.selected}>{props.state.toUpperCase()}</Tag>;
    case 'new':
      return (
        <Tag selected={props.selected} state={'new'}>
          NEW
        </Tag>
      );
    default:
      return null;
  }
};

const DropdownHeader: React.FC<DropdownHeaderProps> = props => {
  if (!props.visible) return null;
  return (
    <HeaderResult isSelected={false}>
      {props.items.map(item => (
        <TC.Contents key={item.id}>
          <HeaderTag
            onMouseDownCapture={e => {
              e.stopPropagation();
              e.preventDefault();
              props.itemClicked(item.id);
            }}
            selected={item.selected}
          >
            {item.text}
            {item.state && <ModuleStateTag selected={item.selected} state={item.state} />}
          </HeaderTag>
          <span style={{ display: 'inline-block', width: '10px' }} />
        </TC.Contents>
      ))}
    </HeaderResult>
  );
};

const Tag = styled.div<{ selected: boolean; state?: ModuleState }>`
  background: ${props =>
    props.state === 'new'
      ? props.theme.colors.accent
      : props.selected
        ? props.theme.colors.background
        : props.theme.colors.brand};
  color: ${props => (props.selected ? props.theme.colors.buttonPrimary : props.theme.colors.backgroundActive)};
  display: inline-block;
  font-size: 12px;
  margin-right: 4px;
  padding: 4px;
  border-radius: 8px;
  font-weight: 700;
`;

const DraggingDiv = styled.div`
  line-height: initial;
`;

const SearchStyle = styled.div`
  display: flex;
  flex: 1;
  flex-flow: row;

  font-size: 14px;
  // cursor: text;
  width: 100%;
  max-width: 100%; //search input max width to enable scroll in firefox;
  box-sizing: border-box;

  /* line-height: 30px; */
  align-items: center;

  position: static;
  background-color: ${props => props.theme.colors.background};

  height: 100%;
`;

export const SearchDisplay = styled.div`
  align-items: center;
  display: flex;
  flex-grow: 1;
  justify-content: flex-start;
  /* scrollbar-width: thin; // Firefox sprecific property */
  overflow: auto;

  margin: 4px;
  margin-left: 0px;

  cursor: default;
  /* overflow: auto; */
  width: inherit;

  &::-webkit-scrollbar {
    height: 6px;
    background-color: ${props => props.theme.colors.backgroundActive};
  }
`;

const WrappedSearchDisplay = styled(SearchDisplay)`
  flex-wrap: wrap;
  max-height: 150px;
  /* overflow-y: auto; */
`;

export const SearchInput = styled(DynamicWidthInput)<{ addBorder: boolean }>`
  border: ${props => (props.addBorder ? 1 : 0)};
  border-color: ${props => props.theme.colors.backgroundInactive};
  outline: 0;
  height: 30px;
  /* padding: 0 10px 0 5px; */
  flex-grow: 0;
  box-sizing: border-box;
  /* font-size: 1em; */
  background: ${props => props.theme.colors.border};

  font-size: 12px;
  color: ${props => props.theme.colors.foregroundInactive};
  padding: 0;
  padding-left: 11px;

  vertical-align: top;
  caret-color: ${props => props.theme.colors.accent};
  min-width: 80px;
  text-overflow: ellipsis;

  &::placeholder {
    color: ${props =>
      props.theme.name == 'highContrast'
        ? props.theme.colors.foregroundInactive
        : `rgba(${props.theme.colors.foregroundInactive}, 0.825)`};
  }
`;

const PositionInputSpan = styled.span`
  align-items: center;
  display: flex;
`;

const InputSpan = styled(PositionInputSpan)``;

const NotTag = styled.div`
  position: relative;
  margin-right: 4px;
  top: -2px;
`;

const AddPositionSelectorSpan = styled.div`
  cursor: text;
  display: inline-block;
  box-sizing: border-box;
  padding: 15px 5px;
`;

const AddPositionSelectorAutoWidth = styled.div`
  cursor: text;
  display: inline-block;
  box-sizing: border-box;
  flex-grow: 1;
  padding: 15px 3px;
`;

const GroupStartSpan = styled.div<{ selected?: boolean }>`
  cursor: pointer;
  display: inline-block;
  box-sizing: border-box;
  position: relative;
  top: -2px;
  /* padding: 12px 3px; */
  &::before {
    content: '(';
  }

  ${props =>
    props.selected
      ? css`
          font-size: 2.2em;
          color: ${props.theme.colors.foreground};
        `
      : css`
          font-size: 2em;
          color: ${props.theme.colors.foregroundMuted};
        `}
`;

const NotGroupStartSpan = styled(GroupStartSpan)`
  position: absolute;
  top: -2px;
  right: -5px;
`;

const GroupEndSpan = styled.div<{ selected?: boolean }>`
  cursor: pointer;
  display: block;
  box-sizing: border-box;
  position: relative;
  top: -2px;
  /* padding: 12px 3px; */
  &::after {
    content: ')';
  }

  ${props =>
    props.selected
      ? css`
          font-size: 2.2em;
          color: ${props => props.theme.colors.foreground};
        `
      : css`
          font-size: 2em;
          color: ${props => props.theme.colors.foregroundMuted};
        `}
`;

const InvisibleInput = styled.input`
  /* visibility: hidden; */
  border: 0px;
  padding: 0px;
  width: 0px;
  opacity: 0;
`;

const SearchDropDown = styled.ul`
  list-style-type: none;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.foreground};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-top-width: 0px;
  border-bottom-width: 2px;
  border-bottom-color: ${props => props.theme.colors.brand};

  font-size: 100%;
  margin: 0;
  outline: 0;
  padding: 0;
  vertical-align: baseline;
`;

export const SearchTag = styled.span<{ selected?: boolean }>`
  background: ${props => props.theme.colors.brand};
  box-sizing: border-box;
  /* display: inline-block; */
  color: ${props => props.theme.colors.brandForeground};
  cursor: pointer;
  /* height: 24px; */
  /* line-height: 17px; */
  padding: 3px 6px;
  white-space: nowrap;

  ${props =>
    props.selected
      ? css`
          border: 1px solid ${props.theme.colors.foreground};
        `
      : ``}
`;

const HeaderTag = styled.div<{ selected?: boolean }>`
  box-sizing: border-box;
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${props => props.theme.colors.brandForeground};
  cursor: pointer;
  padding: 3px 6px;
  white-space: nowrap;

  ${props =>
    props.selected
      ? css`
          border: 1px solid ${props.theme.colors.foreground};
        `
      : ``}

  background: ${props => (props.selected ? props.theme.colors.brand : props.theme.colors.backgroundActive)};
  color: ${props => (props.selected ? props.theme.colors.brandForeground : props.theme.colors.foreground)};
  ${props =>
    props.selected
      ? css`
          border: none;
        `
      : ``}
`;
export const SearchTagIcon = styled.span`
  vertical-align: middle;
  display: contents;
  width: 1em;
  height: 1em;
  margin-right: 4px;
`;

const SearchOperatorBase = styled.span`
  position: relative;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  height: 24px;
  line-height: 17px;
`;

const SearchOperator = styled(SearchOperatorBase)<{
  selected?: boolean;
  noLeftBorder?: boolean;
  noRightBorder?: boolean;
  noPadding?: boolean;
  moreRightPadding?: boolean;
}>`
  background: ${props => props.theme.colors.backgroundActive};
  color: ${props => props.theme.colors.foregroundActive};
  position: relative;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block;
  height: 24px;
  line-height: 17px;

  ${props =>
    props.selected
      ? css`
          border: 1px solid ${props.theme.colors.foreground};
        `
      : css`
          border: 1px solid ${props.theme.colors.backgroundInactive};
        `}
  ${props =>
    props.noLeftBorder
      ? css`
          border-left-width: 0px;
        `
      : css`
          border-left-width: 1px;
        `}
  ${props =>
    props.noRightBorder
      ? css`
          border-right-width: 0px;
        `
      : css`
          border-right-width: 1px;
        `}

  ${props =>
    props.noPadding
      ? css`
          padding: 3px 0px;
        `
      : css`
          padding: 3px 6px;
        `}
`;

const NotSearchOperator = styled(SearchOperator)`
  padding-right: 10px;
  top: 2px;
`;

const SearchResult = styled.li<{ isSelected: boolean }>`
  padding: 2px 5px;
  border-width: 1px;
  line-height: 1em;
  border-style: solid;
  border-color: ${props => props.theme.colors.backgroundInactive};
  background: ${props => props.theme.colors.backgroundActive};
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  ${props =>
    props.isSelected
      ? css`
          border-left-width: 5px;
          padding-left: 1px;
          border-color: ${props.theme.colors.brand} !important;
          cursor: pointer;
        `
      : ''}
`;

const HeaderResult = styled(SearchResult)`
  padding: 10px 10px;
  flex-wrap: nowrap;
  overflow: auto;

  &::-webkit-scrollbar {
    height: 6px;
    background-color: ${props => props.theme.colors.backgroundActive};
  }
`;

const OperatorSpan = styled.span`
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
`;

const Locked = styled(Lock)`
  fill: ${props => props.theme.colors.accent};
`;

const SearchResultHead = styled.div<{ isSelected: boolean }>`
  min-width: 68px;
  margin: 3px 0;
  padding-left: 0.5em;
  font-size: 0.9em;
  color: ${props => (props.isSelected ? props.theme.colors.foregroundActive : props.theme.colors.foregroundInactive)};
`;

const SearchResultDescription = styled.div<{ isSelected: boolean }>`
  display: flex;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 3px 0.75em;
  flex-shrink: 0;
  color: ${props => (props.isSelected ? props.theme.colors.foregroundActive : props.theme.colors.foreground)};
`;

export const SearchBar = React.forwardRef<SearchBarForwardRef, Props>(SearchBarInner) as (
  p: Props & { ref?: React.Ref<SearchBarForwardRef> },
) => React.ReactElement;

const ThinSearchBarStyler = styled.div`
  min-width: 0%;
  flex: 1;
  width: auto;
  /* overflow: hidden; */

  ${AddPositionSelectorSpan} {
    padding: 9px 3px;
  }
  ${NotSearchOperator} {
    display: inline-block;
  }
  ${AddPositionSelectorAutoWidth} {
    padding: 9px 4px;
  }
  ${GroupEndSpan} {
    font-size: 20px;
  }

  ${GroupStartSpan} {
    font-size: 20px;
  }

  ${SearchDisplay} {
    margin: 0;
    margin-bottom: 0;
    /* overflow-x: auto; */
  }

  ${InputSpan} {
    align-items: center;
    display: initial;
  }

  ${SearchInput} {
    height: 20px;
    border: 0;
  }
  ${SearchTag} {
    height: 20px;
    padding: 0px 4px;
  }

  ${SearchOperatorBase} {
    box-sizing: initial;
    height: initial;
    line-height: initial;
    padding: 0px 6px;
  }
`;
