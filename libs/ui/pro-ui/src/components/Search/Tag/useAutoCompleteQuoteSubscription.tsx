import { useQuoteHoldingsSubscription, useQuoteHoldingsSubscriptionCallback } from '@benzinga/quotes-v3-manager-hooks';
import { SymbolLikeModuleItem, useAutoCompleteSymbols } from './useAutoCompleteSymbols';
import { HoldingsQuote } from '@benzinga/quotes-v3-holdings-manager';
import React from 'react';
export const useAutoCompleteQuoteSubscription = (
  autoComplete: SymbolLikeModuleItem[],
  fields: Set<keyof HoldingsQuote>,
  shouldSubscribe = false,
) => {
  const cacheQuotesRef = React.useRef<HoldingsQuote[]>();
  const symbols = useAutoCompleteSymbols(autoComplete);
  const holdings = useQuoteHoldingsSubscription(symbols ?? [], fields);
  if (!holdings?.length) {
    cacheQuotesRef.current = holdings;
  }
  return shouldSubscribe ? holdings : cacheQuotesRef.current;
};

export const useAutoCompleteQuoteSubscriptionCall = (
  autoComplete: SymbolLikeModuleItem[],
  fields: Set<keyof HoldingsQuote>,
  callback: (quotes: HoldingsQuote[]) => void,
  shouldSubscribe = false,
) => {
  const cacheQuotesRef = React.useRef<HoldingsQuote[]>();
  const wasSubscribedRef = React.useRef(shouldSubscribe);
  const isInitialRender = React.useRef(true);
  const lastCallbackTimeRef = React.useRef(0);
  const callbackTTL = 2000;

  const symbols = useAutoCompleteSymbols(autoComplete);

  const stableCallback = React.useCallback(
    (quotes: HoldingsQuote[]) => {
      const now = Date.now();

      const shouldPassQuotes =
        isInitialRender.current ||
        (shouldSubscribe && quotes && quotes.length > 0) ||
        wasSubscribedRef.current !== shouldSubscribe ||
        now - lastCallbackTimeRef.current > callbackTTL;

      if (shouldPassQuotes) {
        lastCallbackTimeRef.current = now;
        isInitialRender.current = false;

        if (shouldSubscribe && quotes && quotes.length > 0) {
          cacheQuotesRef.current = quotes;
          callback(quotes);
        } else if (!shouldSubscribe && cacheQuotesRef.current) {
          callback(cacheQuotesRef.current);
        } else if (!cacheQuotesRef.current) {
          callback([]);
        }
      }

      wasSubscribedRef.current = shouldSubscribe;
    },
    [callback, shouldSubscribe],
  );

  useQuoteHoldingsSubscriptionCallback(
    shouldSubscribe ? symbols ?? [] : [],
    shouldSubscribe ? fields : new Set<keyof HoldingsQuote>(['symbol']),
    stableCallback,
  );

  return cacheQuotesRef.current;
};
