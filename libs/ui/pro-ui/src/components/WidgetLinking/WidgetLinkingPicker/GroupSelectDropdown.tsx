'use client';
import styled, { css } from '@benzinga/themetron';
import { WidgetLink, WidgetLinkingID } from '@benzinga/widget-linking';
import Hooks from '@benzinga/hooks';
import React, { ReactNode } from 'react';
import { useStatefulAutocomplete } from '../../Search/hooks/useAutocompleteDataHooks';
import { noop } from '@benzinga/utils';
import { useLinkingSearchModule } from '../../Search';

interface Props {
  editGroup: (g: WidgetLink | undefined) => void;
  group: WidgetLinkingID;
  onClose: () => void;
  open: boolean;
  position?: 'bottom' | 'left' | 'right';
  selectGroupID: (selection: WidgetLinkingID | 'Edit Groups') => void;
}

export const GroupSelectDropdown: React.FC<Props> = props => {
  const [highlightIndex, setHighlightIndex] = React.useState(0);

  const { onClose, open, position = 'bottom', selectGroupID } = props;

  const linkingModule = useLinkingSearchModule();
  const modules = React.useMemo(() => [linkingModule.autocomplete], [linkingModule]);

  const { search: handleSearchChange, searchResults } = useStatefulAutocomplete({
    autocompleteModules: modules,
  });

  const ref = React.useRef<HTMLUListElement>(null);

  Hooks.useClickOutside(ref, () => {
    onClose();
  });

  const handleSelect = React.useCallback(
    (groupId: WidgetLinkingID) => {
      selectGroupID(groupId);
    },
    [selectGroupID],
  );

  const handleMouseEnter = React.useCallback((index: number) => {
    setHighlightIndex(index);
  }, []);

  const handleKeyPress = React.useCallback<React.KeyboardEventHandler<HTMLUListElement>>(
    noop,
    // e => {
    // if (e.key === KeyboardKey.Enter) {
    //   if (highlightIndex < autocompleteItems.length) {
    //     handleSelect(autocompleteItems[highlightIndex].tagId);
    //   } else if (highlightIndex == autocompleteItems.length) {
    //     handleSelect(null);
    //   } else {
    //     handleSelect('New Group');
    //   }
    // } else if (e.key === KeyboardKey.ArrowDown) {
    //   setState(s => ({
    //     ...s,
    //     highlightIndex: min([s.highlightIndex + 1, autocompleteItems.length + 1]) ?? autocompleteItems.length + 1,
    //   }));
    // } else if (e.key === KeyboardKey.ArrowUp) {
    //   setState(s => ({
    //     ...s,
    //     highlightIndex: max([s.highlightIndex - 1, 0]) ?? 0,
    //   }));
    // }
    // }
    [],
  );

  const renderDropdown = React.useCallback(() => {
    const groups = searchResults;
    let mapped = 0;
    return (
      <SearchDropDown onKeyPress={handleKeyPress} position={position} ref={ref}>
        {groups.flatMap<ReactNode>((groupAC, idx) => {
          const isSelected = highlightIndex === idx;
          mapped++;
          const res = linkingModule.autocomplete.dropdown(groupAC);
          return res
            ? [
                <SearchResult
                  isSelected={highlightIndex === mapped}
                  key={'clear'}
                  onClick={() => handleSelect(null)}
                  onMouseEnter={() => handleMouseEnter(mapped)}
                >
                  {res.component({ isSelected })}
                </SearchResult>,
              ]
            : [];
        })}
      </SearchDropDown>
    );
  }, [
    handleKeyPress,
    handleMouseEnter,
    handleSelect,
    highlightIndex,
    linkingModule.autocomplete,
    position,
    searchResults,
  ]);

  React.useEffect(() => {
    handleSearchChange('+');
  }, [handleSearchChange, open]);

  return open ? renderDropdown() : null;
};

const SearchDropDown = styled.ul<{ position: 'bottom' | 'left' | 'right' }>`
  position: absolute;
  ${props => {
    switch (props.position) {
      case 'bottom':
        return css`
          top: calc(100% + 1px);
          left: -1px;
          right: 0;
        `;
      case 'right':
        return css`
          top: 0;
          left: calc(100% + 1px);
        `;
      case 'left':
        return css`
          top: 0;
          right: calc(100% + 1px);
        `;
    }
  }}
  list-style-type: none;
  z-index: 5;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.foreground};
  border: 1px solid ${props => props.theme.colors.borderLight};
  border-top-width: 0px;
  border-bottom-width: 2px;
  border-bottom-color: ${props => props.theme.colors.brand};

  font-size: 100%;
  margin: 0;
  outline: 0;
  padding: 0;
  vertical-align: baseline;
  margin-bottom: 1em;
`;

const SearchResult = styled.li<{ isSelected: boolean }>`
  padding: 2px 5px;
  border-width: 1px;
  line-height: 1em;
  border-style: solid;
  border-color: ${props => props.theme.colors.backgroundInactive};
  background: ${props => props.theme.colors.backgroundActive};
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  ${props =>
    props.isSelected
      ? css`
          border-left-width: 5px;
          padding-left: 1px;
          border-color: ${props.theme.colors.brand} !important;
          cursor: pointer;
        `
      : ''}
`;
