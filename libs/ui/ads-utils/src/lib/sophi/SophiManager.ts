'use client';
import { SessionPropsExtension } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { isBetaSite, isSandboxDomain, runningClientSide } from '@benzinga/utils';

declare global {
  interface Window {
    sophi: {
      abTests: {
        getAllExperiments: (type: 'external' | 'internal') => void;
        getExperiment: (experimentId: string) => void;
      };
      demeter: { actions: any[] };
      dataLayer?: {
        trace: string;
        context: string;
        inputs: string;
        experimentsCode: string;
        assignedGroup: 'control' | 'variant' | 'holdout';
      };
      quickStart: SophiDecision;
    };
    demeter: {
      (
        action: 'getDecision',
        options: { args: { visitor: 'anonymous' | 'registered' } },
      ): Promise<{ outcome: SophiDecisionOutcome }>;
      (action: 'pageview', options: { args: { article: boolean; section: string } }): Promise<void>;
      (action: 'wall', options: { args: { section: string; wallType: 'paywall' | 'regwall' } }): Promise<void>;
      (action: 'config', options: { args: { logLevel: 'debug' | 'warn' } }): Promise<void>;
      //(action: string, options?: any): Promise<any>;
    };
  }
}

interface SophiDecision {
  id: string;
  createdAt: string;
  trace: string;
  context: string;
  inputs: string;
  outcome: SophiDecisionOutcome;
  searchParams: string;
  userProperties: string;
  experiment: {
    experimentId: string;
    assignedGroup: string;
  };
}

interface SophiDecisionOutcome {
  wallVisibility: 'always' | 'never' | 'metered';
  wallType: 'paywall' | 'regwall';
  wallTypeCode: number;
  wallVisibilityCode: number;
}

class SophiManager {
  private INITIALIZED = false;
  private IS_SCRIPT_INJECTING = false;
  private IS_SCRIPT_INJECTED = false;
  private DISABLED = false;
  private READY = false;
  private hostId: number | null = 3918305594;
  private isConsented = true;

  constructor() {
    if (runningClientSide()) {
      if (window.sophi && typeof window.demeter === 'function') {
        this.IS_SCRIPT_INJECTED = true;
        this.INITIALIZED = true;
        this.READY = true;
        this.log('Sophi already loaded');
      }
    }
  }

  /**
   * Initialize Sophi module for Web
   */
  public init(): void {
    if (!runningClientSide() || this.DISABLED || this.INITIALIZED) return;

    this.hostId = this.getHostId();
    this.INITIALIZED = true;
    this.READY = true;
    this.loadSophiScript();
  }

  /**
   * Get host id for Sophi script
   */
  public getHostId(): number {
    if (isSandboxDomain()) {
      return 1114387380;
    } else if (isBetaSite()) {
      return 390599266;
    }
    return 3918305594;
  }

  /**
   * Check if Sophi is loaded
   */
  public isLoaded(): boolean {
    return this.IS_SCRIPT_INJECTED;
  }

  /**
   * Check if Sophi is disabled
   */
  public isDisabled(): boolean {
    return this.DISABLED;
  }

  /**
   * Check if Sophi is ready
   */
  public isReady(): boolean {
    return this.READY;
  }

  /**
   * Disable Sophi
   */
  public disable(): void {
    this.DISABLED = true;
  }

  /**
   * Enable Sophi
   */
  public enable(): void {
    this.DISABLED = false;
  }

  /**
   * Notify Sophi of a page view
   * @param isArticle - Whether the current page is an article
   * @param section - The section of the current page
   */
  public notifyPageView(isArticle: boolean, section: string): void {
    if (!runningClientSide() || !window.demeter) return;

    window.demeter('pageview', {
      args: { article: isArticle, section: section },
    });
  }

  /**
   * Notify Sophi of a wall encounter
   * @param wallType - The type of wall encountered ('paywall' or 'regwall')
   * @param section - The section of the current page
   */
  public notifyWall(wallType: 'paywall' | 'regwall', section: string): void {
    if (!runningClientSide() || !window.demeter) return;

    window.demeter('wall', {
      args: { section: section, wallType: wallType },
    });
  }

  /**
   * Get a decision from Sophi
   * @param userType - The type of user ('anonymous' or 'registered')
   * @returns A promise that resolves to the Sophi decision
   */
  public async getDecision(userType: 'anonymous' | 'registered'): Promise<{ outcome: SophiDecisionOutcome }> {
    if (!runningClientSide() || !window.demeter) {
      return { outcome: { wallType: 'paywall', wallTypeCode: 1, wallVisibility: 'never', wallVisibilityCode: 1 } };
    }

    try {
      return await window.demeter('getDecision', {
        args: { visitor: userType },
      });
    } catch (error) {
      console.error('Error getting Sophi decision:', error);
      return { outcome: { wallType: 'paywall', wallTypeCode: 1, wallVisibility: 'never', wallVisibilityCode: 1 } };
    }
  }

  /**
   * Add Sophi data to GA4 event
   * @param session - The session object
   * @param eventName - The name of the event
   * @param eventParams - Additional event parameters
   */
  public trackWithData(
    session: SessionPropsExtension,
    eventName: string,
    eventParams: Record<string, string> = {},
  ): void {
    if (!runningClientSide() || !window.sophi?.dataLayer) return;

    const sophiData = window.sophi.dataLayer;

    const params: Record<string, string> = {
      ...eventParams,
      sophi_context: sophiData.context,
      sophi_experiments_code: sophiData.experimentsCode,
      sophi_inputs: sophiData.inputs,
      sophi_trace: sophiData.trace,
    };

    // Use the existing tracking manager to send the event
    if (eventName === 'page_view') {
      //session.getManager(TrackingManager).trackPageEvent('view', params);
    } else if (eventName === 'wallhit') {
      session.getManager(TrackingManager).trackPaywallEvent('view', {
        ...params,
        paywall_type: params.wall_type || 'paywall',
      });
    }
  }

  /**
   * Check if the user is in the variant group for A/B testing
   * @returns Whether the user is in the variant group
   */
  public isUserInVariantGroup(): boolean {
    if (!runningClientSide() || !window.sophi?.dataLayer) return false;

    return window.sophi.dataLayer.assignedGroup === 'variant';
  }

  /**
   * Set Sophi configuration
   * @param logLevel - The log level for Sophi ('debug', 'info', 'warn', 'error')
   */
  public async configure(logLevel: 'debug' | 'warn'): Promise<void> {
    if (!runningClientSide() || !window.demeter) return;

    try {
      this.log(`Set logLevel to ${logLevel}`);
      await window.demeter('config', {
        args: {
          logLevel: logLevel,
        },
      });
    } catch (error) {
      console.error('Error configuring Sophi:', error);
    }
  }

  /**
   * Load the Sophi script
   */
  private loadSophiScript(): void {
    if (!runningClientSide() || this.DISABLED || this.IS_SCRIPT_INJECTED || this.IS_SCRIPT_INJECTING || !this.hostId)
      return;

    this.IS_SCRIPT_INJECTING = true;
    this.log('Loading Sophi script');

    // Initialize Sophi object if it doesn't exist
    window.sophi = window.sophi || {};
    window.sophi.demeter = { actions: [] };

    // Initialize demeter function
    window.demeter =
      window.demeter ||
      async function (...args) {
        let resolve, reject;
        const s = new Promise((res, rej) => {
          resolve = res;
          reject = rej;
        });
        window.sophi.demeter.actions.push([args, resolve, reject]);
        return s;
      };

    const script = document.createElement('script');
    script.id = 'sophi-loader';
    script.async = true;
    script.src = `https://cdn.sophi.io/assets/demeter/1/stable/${this.hostId}.js?isConsented=${this.isConsented}`;

    script.onload = () => {
      this.IS_SCRIPT_INJECTED = true;
      this.IS_SCRIPT_INJECTING = false;
      this.log('Sophi script loaded successfully');
      this.configure('debug');
    };

    script.onerror = () => {
      this.IS_SCRIPT_INJECTING = false;
      this.log('Error loading Sophi script', 'error');
    };

    document.head.appendChild(script);
  }

  /**
   * Log a message to the console
   * @param message - The message to log
   * @param method - The logging method to use
   * @param optionalParams - Additional parameters to log
   */
  private log(message: string, method: 'log' | 'warn' | 'error' = 'log', ...optionalParams: unknown[]): void {
    if (!runningClientSide()) return;
    if (new URLSearchParams(window.location.search).get('debug_sophi_manager') !== 'true') return;

    const prefix = '[Sophi Paywall Manager]';

    switch (method) {
      case 'warn':
        console.warn(`${prefix}${message}`, ...optionalParams);
        break;
      case 'error':
        console.error(`${prefix}${message}`, ...optionalParams);
        break;
      case 'log':
      default:
        console.log(`${prefix}${message}`, ...optionalParams);
        break;
    }
  }
}

export const sophiManager = new SophiManager();
