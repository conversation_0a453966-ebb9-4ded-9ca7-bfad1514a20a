'use client';

import React, { ReactNode, useCallback, useState, useMemo } from 'react';
import ReactDom from 'react-dom';
import { Placement } from '@popperjs/core';
import { usePopper } from 'react-popper';
import OutsideClickHandler from 'react-outside-click-handler';
import classNames from 'classnames';

export type DropdownProps = React.PropsWithChildren<{
  className?: string;
  closeWhenClickOutside?: boolean;
  distance?: number;
  fluid?: boolean;
  headless?: boolean;
  onClose?: () => void;
  onOpen?: () => void;
  onToggle?: (toggle: boolean) => void;
  open?: boolean;
  placement?: Placement;
  portalElement?: HTMLElement | Element | null;
  skidding?: number;
  shouldBeHoverable?: () => boolean;
  target: ReactNode;
  targetElement?: ReactNode;
  topDivHeight?: number;
  trigger?: 'click' | 'hover';
}>;

export const Dropdown = ({
  children,
  className,
  closeWhenClickOutside = true,
  distance = 0,
  fluid = false,
  headless = false,
  onClose,
  onOpen,
  onToggle,
  open,
  placement = 'bottom-start',
  portalElement,
  shouldBeHoverable,
  skidding = 0,
  target,
  targetElement,
  topDivHeight = 0,
  trigger = 'hover',
}: DropdownProps) => {
  const [isOpen, setOpen] = useState(false);
  const [referenceElement, setReferenceElement] = useState<HTMLDivElement | null>(null);
  const [popperElement, setPopperElement] = useState<HTMLDivElement | null>(null);
  const { attributes, styles } = usePopper(referenceElement, popperElement, {
    modifiers: [
      {
        name: 'offset',
        options: {
          offset: [skidding, distance],
        },
      },
    ],
    placement,
    strategy: 'fixed',
  });
  const isDropdownOpen = open || isOpen;

  const hasCallback = useMemo(() => Boolean(onToggle || onOpen || onClose), [onClose, onOpen, onToggle]);

  const handleToggle = useCallback(() => {
    const newValue = typeof open !== 'undefined' ? !open : !isOpen;

    if (typeof onToggle === 'function') onToggle(newValue);
    if (newValue && typeof onOpen === 'function') onOpen();
    if (!newValue && typeof onClose === 'function') onClose();

    if (!hasCallback) {
      setOpen(newValue);
    }
  }, [open, isOpen, onToggle, onOpen, onClose, hasCallback]);

  const handleClickToggle = useCallback(
    (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
      e.stopPropagation();
      if (trigger !== 'click') return;
      handleToggle();
    },
    [trigger, handleToggle],
  );

  const handleShow = useCallback(() => {
    const previousValue = typeof open !== 'undefined' ? open : isOpen;
    if (previousValue) return;

    if (typeof onOpen === 'function') onOpen();
    else if (!hasCallback) {
      setOpen(true);
    }
  }, [open, isOpen, onOpen, hasCallback]);

  const handleHide = useCallback(() => {
    const previousValue = typeof open !== 'undefined' ? open : isOpen;
    if (previousValue === false) return;

    if (typeof onClose === 'function') onClose();
    else if (!hasCallback) {
      setOpen(false);
    }
  }, [open, isOpen, onClose, hasCallback]);

  const handleMouseEnter = useCallback(() => {
    const isHoverable = shouldBeHoverable ? shouldBeHoverable() : true;
    if (trigger !== 'hover' || !isHoverable) return;
    handleShow();

    if (typeof onOpen !== 'function' && typeof onClose !== 'function' && typeof onToggle === 'function') onToggle(true);
  }, [trigger, handleShow, shouldBeHoverable, onOpen, onClose, onToggle]);

  const handleMouseLeave = useCallback(() => {
    if (trigger !== 'hover') return;
    handleHide();

    if (typeof onOpen !== 'function' && typeof onClose !== 'function' && typeof onToggle === 'function')
      onToggle(false);
  }, [trigger, handleHide, onOpen, onClose, onToggle]);

  const handleOutsideClick = useCallback(() => {
    if (isDropdownOpen) {
      setTimeout(() => {
        handleHide();
      });
    }
  }, [handleHide, isDropdownOpen]);

  const Wrapper = useMemo(
    () =>
      closeWhenClickOutside && !portalElement
        ? ({ children: child }: React.PropsWithChildren<{ children?: React.ReactNode }>) => (
            <OutsideClickHandler display="contents" onOutsideClick={handleOutsideClick}>
              <div className="relative w-fit" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
                {child}
              </div>
            </OutsideClickHandler>
          )
        : ({ children: child }: React.PropsWithChildren<{ children?: React.ReactNode }>) => (
            <div className="relative w-fit" onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
              {child}
            </div>
          ),
    [closeWhenClickOutside, handleMouseEnter, handleMouseLeave, handleOutsideClick, portalElement],
  );

  const DropDownBox = useMemo(
    () =>
      ({ children: child }: React.PropsWithChildren<{ children?: React.ReactNode }>) => (
        <div
          className={classNames('flex flex-col', {
            'bg-white': !headless,
            'max-w-72': !headless && !fluid,
            'overflow-hidden': !headless,
            'ring-1': !headless,
            'ring-black': !headless,
            'ring-opacity-5': !headless,
            rounded: !headless,
            'shadow-lg': !headless,
            'z-60': !headless,
            [`${className}`]: className,
          })}
        >
          {child}
        </div>
      ),
    [className, fluid, headless],
  );

  return (
    <Wrapper>
      <div className="inline-flex relative truncate w-full" onClick={handleClickToggle} ref={setReferenceElement}>
        {target}
      </div>
      {isDropdownOpen && <div className="absolute left-0 right-0 top-full" style={{ height: topDivHeight }} />}
      {isDropdownOpen && (
        <div>
          {portalElement ? (
            ReactDom.createPortal(
              closeWhenClickOutside ? (
                <OutsideClickHandler onOutsideClick={handleOutsideClick}>
                  <div className="z-20" ref={setPopperElement} style={styles.popper} {...attributes}>
                    <DropDownBox>{children}</DropDownBox>
                  </div>
                </OutsideClickHandler>
              ) : (
                <div className="z-20" ref={setPopperElement} style={styles.popper} {...attributes}>
                  <DropDownBox>{children}</DropDownBox>
                </div>
              ),
              portalElement,
            )
          ) : targetElement ? (
            targetElement
          ) : (
            <div className="z-20" ref={setPopperElement} style={styles.popper} {...attributes}>
              <DropDownBox>{children}</DropDownBox>
            </div>
          )}
        </div>
      )}
    </Wrapper>
  );
};
