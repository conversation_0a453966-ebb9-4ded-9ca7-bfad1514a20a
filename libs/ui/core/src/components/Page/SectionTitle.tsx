'use client';

import React from 'react';
import styled from '@benzinga/themetron';
import cn from 'classnames';

export interface SectionTitleProps {
  bordered?: boolean;
  borderThrough?: boolean;
  className?: string;
  id?: string;
  level?: number;
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';
  rightSlot?: React.ReactNode;
  titleText?: string;
  url?: string;
  uppercase?: boolean;
  capitalize?: boolean;
}

export const SectionTitle: React.FC<React.PropsWithChildren<SectionTitleProps>> = ({
  bordered = false,
  borderThrough = false,
  capitalize = true,
  children,
  className = '',
  id,
  level = 3,
  rightSlot,
  size,
  titleText,
  uppercase = true,
  url,
}) => {
  const additionalProps = {
    'aria-label': url && titleText ? `Click here to see more ${titleText} content` : undefined,
    'data-action': url && titleText ? `${titleText} See More Click` : undefined,
    href: url,
  };
  return (
    <SectionTitleWrapper
      $capitalize={capitalize}
      $size={size}
      $uppercase={uppercase}
      as={url ? 'a' : 'div'}
      className={cn('section-title', {
        [`${className}`]: !!className,
        'border-through': borderThrough,
        bordered: bordered,
        'contains-right-slot': rightSlot,
        [`text-${size}`]: !!size,
      })}
      id={id}
      {...additionalProps}
    >
      {level === 1 ? <h1>{children}</h1> : null}
      {level === 2 ? <h2>{children}</h2> : null}
      {level === 3 ? <h3>{children}</h3> : null}
      {level === 4 ? <h4>{children}</h4> : null}
      {level === 5 ? <h5>{children}</h5> : null}
      {level === 6 ? <h6>{children}</h6> : null}
      {level === 0 ? <span>{children}</span> : null}
      {rightSlot && <span className="right-slot">{rightSlot}</span>}
    </SectionTitleWrapper>
  );
};

// fontWeight="bold" isLG={{ fontSize: '2xl' }} mb="0" uppercase
const SectionTitleWrapper = styled.div<{ $capitalize: boolean; $uppercase: boolean; $size?: string }>`
  font-size: ${({ $size, theme }) => ($size ? undefined : theme.fontSize['2xl'])};
  font-weight: bold;
  margin-bottom: 0;
  text-transform: ${({ $capitalize, $uppercase }) => ($uppercase ? 'uppercase' : $capitalize ? 'capitalize' : '')};
  width: 100%;
  > h1,
  > h2,
  > h3,
  > h4,
  > h5,
  > h6,
  > span {
    font-size: inherit;
  }
  &.bordered {
    border-bottom: solid 1px ${({ theme }) => theme.colorPalette.neutral500};
    border-color: #e1ebfa;
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 8px;
    padding: 4px 0;
  }
  &.border-through {
    margin: 12px 0;
    padding: 4px 0;
    overflow: hidden;
    text-align: center;

    > h1,
    > h2,
    > h3,
    > h4,
    > h5,
    > h6,
    > span {
      position: relative;
      display: inline-block;

      &:before,
      &:after {
        content: '';
        position: absolute;
        top: 50%;
        border-bottom: 1px solid ${({ theme }) => theme.colorPalette.neutral500};
        width: 100vw;
        margin: 0 20px;
      }

      &:before {
        right: 100%;
      }

      &:after {
        left: 100%;
      }
    }
  }
  &.contains-right-slot {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .see-more {
    font-size: ${({ theme }) => theme.fontSize.sm};
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    text-transform: none;
    color: rgb(44, 162, 209);
  }
`;
