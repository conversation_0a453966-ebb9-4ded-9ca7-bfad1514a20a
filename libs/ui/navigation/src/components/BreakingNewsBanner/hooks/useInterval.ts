'use client';
import { useCallback, useEffect, useRef, useState } from 'react';

export type UseIntervalOptions = {
  cancelOnUnmount?: boolean;
};

const defaultOptions: UseIntervalOptions = {
  cancelOnUnmount: true,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const useInterval = <T extends (...args: any[]) => any>(
  fn: T,
  milliseconds: number,
  options: UseIntervalOptions = defaultOptions,
): [boolean, () => void] => {
  const opts = { ...defaultOptions, ...(options || {}) };
  const timeout = useRef<number>();
  const callback = useRef<T>(fn);
  const [isCleared, setIsCleared] = useState<boolean>(false);

  const clear = useCallback(() => {
    if (timeout.current) {
      setIsCleared(true);
      clearInterval(timeout.current);
    }
  }, []);

  useEffect(() => {
    if (typeof fn === 'function') {
      callback.current = fn;
    }
  }, [fn]);

  useEffect(() => {
    if (typeof milliseconds === 'number') {
      timeout.current = window.setInterval(() => {
        callback.current();
      }, milliseconds);
    }

    return clear;
  }, [clear, milliseconds]);

  useEffect(
    () => () => {
      if (opts.cancelOnUnmount) {
        clear();
      }
    },
    [clear, opts.cancelOnUnmount],
  );

  return [isCleared, clear];
};

export default useInterval;
