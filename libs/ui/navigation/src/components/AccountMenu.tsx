'use client';
import React from 'react';
import { UserOutlined, DownOutlined } from '@ant-design/icons';
import styled from '@benzinga/themetron';
import { useTranslation } from 'react-i18next';
import { Checkbox } from '@benzinga/core-ui';
import Hooks from '@benzinga/hooks';
import { useIsUserEditor } from '@benzinga/user-context';
import { getRegisterType } from '@benzinga/utils';
import classNames from 'classnames';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface AccountMenuProps {
  isLoggedIn: boolean;
  onLogout: () => void;
  deviceType: 'mobile' | 'desktop';
}

interface AccountMenuItemProps {
  item: {
    className?: string;
    label: string;
    href?: string;
  };
}

export const AccountMenu: React.FC<AccountMenuProps> = ({ deviceType, isLoggedIn, onLogout }) => {
  const { t } = useTranslation('common');
  const [isAccountMenuOpen, setIsAccountMenuOpen] = React.useState(false);
  const [isEditorModeEnabled, setIsEditorModeEnabled] = Hooks.useLocalStorage('editor_mode_enabled', false);
  const isEditor = Hooks.useHydrate(useIsUserEditor(), false);

  const handleToggleEditorMode = () => {
    setIsEditorModeEnabled(!isEditorModeEnabled);
  };

  const items = [
    {
      children: <Link href="/account">{t('Navigation.AccountMenu.my-account-text')}</Link>,
      hidden: !isLoggedIn,
      label: t('Navigation.AccountMenu.my-account-text'),
    },
    {
      hidden: !isLoggedIn,
      href: 'https://www.benzinga.com/profile/portfolio/?action=notifications',
      label: 'Notifications',
    },
    {
      hidden: !isLoggedIn,
      href: 'https://www.benzinga.com/profile/portfolio',
      label: 'Overview',
    },
    {
      hidden: !isLoggedIn,
      href: 'https://www.benzinga.com/profile/portfolio/?action=new_watchlist',
      label: '+ New Watchlist',
    },
    {
      hidden: !isLoggedIn,
      label: 'DIVIDER',
    },
    {
      children: (
        <Checkbox
          checked={isEditorModeEnabled}
          className="flex"
          id={`admin-mode_${deviceType}`}
          label="Admin Mode"
          onChange={handleToggleEditorMode}
        />
      ),
      hidden: !isLoggedIn || (isLoggedIn && !isEditor),
      label: 'Admin Mode',
    },
    {
      hidden: !isLoggedIn && !isEditor,
      label: 'DIVIDER',
    },
    {
      className: 'benzinga-edge',
      href: 'https://www.benzinga.com/profile/portfolio',
      label: 'Benzinga Edge',
    },
    {
      className: 'benzinga-plus',
      href: 'https://www.benzinga.com/research',
      label: 'Benzinga Research',
    },
    {
      className: 'benzinga-pro',
      href: 'https://pro.benzinga.com',
      label: 'Benzinga Pro',
    },
    {
      label: 'DIVIDER',
    },
  ];

  const handleToggleAccountMenu = () => {
    setIsAccountMenuOpen(isOpen => !isOpen);
  };

  const routerAsPath = usePathname();

  const login = React.useCallback(() => {
    const redirectUrl = new URL(`${window.location.origin}/login?action=login&next=${window.location.href}`);

    const utm_source = getRegisterType(routerAsPath ?? '');
    if (utm_source) {
      redirectUrl.searchParams.append('utm_source', utm_source);
    }

    window.location.href = redirectUrl.toString();
  }, [routerAsPath]);

  return (
    <MenuWrapper className="account-menu menu-wrapper">
      <button className="menu-button" onClick={handleToggleAccountMenu}>
        <UserOutlined />
        <span className="my-account-text">{t('Navigation.AccountMenu.my-account-text')}</span>
        <DownOutlined />
      </button>
      <div className={classNames('account-menu-dropdown', { open: isAccountMenuOpen })}>
        {items.map((item, index) => {
          if (item?.hidden) return null;
          if (item.label === 'DIVIDER') return <hr className="divider" key={`${item.label}-${index}`} />;
          return (
            <AccountMenuItem item={item} key={`${item.label}-${index}`}>
              {item.children || item.label}
            </AccountMenuItem>
          );
        })}
        {isLoggedIn ? (
          <div className="account-menu-item">
            <span onClick={onLogout}>{t('Navigation.AccountMenu.logout')}</span>
          </div>
        ) : (
          <div className="account-menu-item">
            <button className="auth-link-button" onClick={login}>
              {t('Navigation.TopBar.login')}
            </button>
          </div>
        )}
      </div>
    </MenuWrapper>
  );
};

const AccountMenuItem: React.FC<React.PropsWithChildren<AccountMenuItemProps>> = ({ children, item }) => {
  const className = `account-menu-item${item.className ? ` ${item.className}` : ''}`;
  return <div className={className}>{item.href ? <a href={item.href}>{children}</a> : <span>{children}</span>}</div>;
};

const MenuWrapper = styled.div`
  &.menu-wrapper {
    background-color: #033251;
    position: relative;
    width: 140px;
    white-space: nowrap;
    @media screen and (max-width: 800px) {
      background-color: transparent;
    }
    @media screen and (min-width: 800px) {
      &:hover {
        .account-menu-dropdown {
          display: block;
          box-shadow: ${({ theme }) => theme.shadow.sm};
        }
      }
    }
    .menu-button {
      align-items: center;
      color: white;
      cursor: pointer;
      display: inline-flex;
      font-size: 12px;
      height: 32px;
      padding: 0px 12px;
      .anticon-user {
        color: #1a79ff;
        margin-right: 12px;
      }
      @media screen and (max-width: 800px) {
        .anticon-user {
          color: ${({ theme }) => theme.colorPalette.blue300};
          font-size: 18px;
          margin-right: 0;
        }
        .anticon-down {
          display: none;
        }
      }
    }
    .account-menu-dropdown {
      background-color: #ffffff;
      border: 1px solid ${({ theme }) => theme.colors.border};
      display: none;
      position: absolute;
      width: 140px;
      z-index: 104;
      @media screen and (max-width: 800px) {
        right: 0px;
      }

      &.open {
        display: block;
      }

      .checkbox-container {
        label {
          display: flex;
          font-size: ${({ theme }) => theme.fontSize.sm};
        }
      }
    }
    .account-menu-item {
      color: ${({ theme }) => theme.colorPalette.black};
      cursor: pointer;
      font-size: 12px;
      font-weight: ${({ theme }) => theme.fontWeight.bold};
      padding: 8px 12px;
      a {
        color: ${({ theme }) => theme.colorPalette.black};
        display: inline-flex;
      }
      &.benzinga-plus a {
        color: ${({ theme }) => theme.colorPalette.orange500};
      }
      &.benzinga-pro a {
        color: ${({ theme }) => theme.colors.brand};
      }
      &.benzinga-edge a {
        color: #3f83f8;
      }
    }
    .divider {
      margin: 0 auto;
      width: 85%;
      border-color: ${({ theme }) => theme.colorPalette.gray200};
    }
    .my-account-text {
      margin-right: 14px;
      @media screen and (max-width: 800px) {
        display: none;
      }
    }
    @media screen and (max-width: 800px) {
      width: 3rem;
    }
  }
`;
