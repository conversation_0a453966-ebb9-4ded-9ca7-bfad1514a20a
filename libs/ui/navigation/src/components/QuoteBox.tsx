'use client';
import React from 'react';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import styled from '@benzinga/themetron';
import { formatTickerToURLFriendly } from '@benzinga/utils';

interface QuoteChangeProps {
  $change: number;
}

export interface QuoteBoxQuote {
  change: number;
  changePercent: number | string;
  lastTradePrice: number | string;
  symbol: string | string;
}

export interface QuoteBoxProps {
  quote?: QuoteBoxQuote | null;
}

export const QuoteBox: React.FC<QuoteBoxProps> = ({ quote }) => {
  const ticker = quote?.symbol ? formatTickerToURLFriendly(quote.symbol) : null;
  return (
    <QuoteBlock
      $change={quote?.change ? quote.change : 0}
      className={`quote-block ${quote?.change ? (quote.change > 0 ? 'positive' : 'negative') : ''}`}
      href={`/quote/${ticker}`}
    >
      <QuoteName className="quotebox-quote-name">{quote === null ? '' : quote?.symbol ? quote.symbol : '-'}</QuoteName>
      <QuoteData className="quotebox-quote-data">
        <QuotePrice className="quotebox-quote-price">
          {quote === null ? '' : quote?.lastTradePrice ? formatPrice(quote.lastTradePrice) : '-'}
        </QuotePrice>
        <QuoteChange
          $change={quote?.change ? quote.change : 0}
          className={`quotebox-quote-change ${quote?.change ? (quote.change > 0 ? 'positive' : 'negative') : ''}`}
        >
          {quote?.change ? (
            quote.change > 0 ? (
              <CaretUpOutlined className="quote-icon" />
            ) : (
              <CaretDownOutlined className="quote-icon" />
            )
          ) : null}
          <span>{quote === null ? '' : quote?.changePercent ? `${formatChangeValue(quote.changePercent)}%` : '-'}</span>
        </QuoteChange>
      </QuoteData>
    </QuoteBlock>
  );
};

const formatChangeValue = change => {
  return change > 0 ? change : change * -1;
};

const formatPrice = price => {
  const abs = Math.abs(price);
  if (abs < 0.001) {
    return Number(price).toFixed(5);
  } else if (abs < 0.01) {
    return Number(price).toFixed(4);
  } else if (abs < 0.1) {
    return Number(price).toFixed(3);
  } else {
    return Number(price).toFixed(2);
  }
};

const QuoteData = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
`;

const QuoteName = styled('h5')`
  color: ${({ theme }) => theme.colorPalette.gray200};
  font-weight: ${({ theme }) => theme.fontWeight.semibold};
  font-size: ${({ theme }) => theme.fontSize.md};
  line-height: ${({ theme }) => theme.lineHeight[5]};
`;

const QuotePrice = styled('span')`
  color: ${({ theme }) => theme.colorPalette.white};
  font-size: ${({ theme }) => theme.fontSize.sm};
  line-height: ${({ theme }) => theme.lineHeight[4]};
`;

const QuoteChange = styled(QuotePrice)<QuoteChangeProps>`
  color: ${({ $change, theme }) =>
    !$change ? theme.colorPalette.gray400 : $change > 0 ? theme.colors.success : theme.colors.danger};
  font-weight: ${({ theme }) => theme.fontWeight.bold};
  display: flex;
  align-items: center;

  .quote-icon {
    margin: 0 4px;
    font-size: ${({ theme }) => theme.fontSize.sm};
  }
`;

const QuoteBlock = styled('a')<QuoteChangeProps>`
  display: block;
  width: 100%;
  max-width: 145px;
  min-width: 132px;
  padding: 8px;
  background: ${({ $change, theme }) =>
    $change > 0
      ? `linear-gradient(90deg, ${theme.colors.success}00 0%, ${theme.colors.success}0d 100%)`
      : `linear-gradient(90deg, ${theme.colors.danger}00 0%, ${theme.colors.danger}0d 100%)`};

  .dark {
    color: ${({ theme }) => theme.colorPalette.gray400} !important;
  }
  .light {
    color: ${({ theme }) => theme.colorPalette.gray600} !important;
  }
`;
