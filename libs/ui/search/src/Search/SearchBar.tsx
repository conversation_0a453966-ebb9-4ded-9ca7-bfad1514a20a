import { Button, Icon } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';
import { SearchOutlined } from '@ant-design/icons';
import { faTimes } from '@fortawesome/pro-regular-svg-icons/faTimes';
import { Ticker } from '@benzinga/session';

export interface SearchBarProps {
  autoFocus?: boolean;
  handleClickCancel?: () => void;
  handleSearch?: () => void;
  handleRemove?: (removed: Ticker) => void;
  showSearchIcon?: boolean;
  isFullScreenSearch?: boolean;
  onBlur?: () => void;
  onFocus?: () => void;
  onInput: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  placeholder?: string;
  inputRef?: React.MutableRefObject<HTMLInputElement | null>;
  searchQuery?: string;
  selected?: Ticker[] | Ticker | null;
  showSearchButton?: boolean;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  autoFocus,
  handleClickCancel,
  handleRemove,
  handleSearch,
  inputRef,
  isFullScreenSearch,
  onBlur,
  onFocus,
  onInput,
  onKeyDown,
  placeholder,
  searchQuery,
  selected,
  showSearchButton = false,
  showSearchIcon = true,
}) => {
  return (
    <SearchInputContainer className="search-input-container">
      <SearchInputWrapper
        className="search-input-wrapper"
        showSearchButton={showSearchButton}
        showSearchIcon={showSearchIcon}
        style={{ flexDirection: Array.isArray(selected) && selected.length && handleRemove ? 'column' : 'row' }}
      >
        {showSearchIcon && (typeof selected == 'undefined' || (Array.isArray(selected) && selected.length < 1)) && (
          <div className="search-icon" role="presentation">
            <SearchOutlined />
          </div>
        )}
        {selected && !Array.isArray(selected) && handleRemove ? (
          <SelectedOption key={(selected as Ticker)?.symbol} onRemove={handleRemove} selected={selected as Ticker} />
        ) : Array.isArray(selected) && selected.length && handleRemove ? (
          <div className="w-full" style={{ display: 'flex', flexWrap: 'wrap' }}>
            {(selected as Ticker[]).map(option => (
              <SelectedOption key={option.symbol} onRemove={handleRemove} selected={option} />
            ))}
          </div>
        ) : null}
        <input
          aria-label="Search"
          autoFocus={autoFocus}
          className="search-input"
          onBlur={onBlur}
          onFocus={onFocus}
          onInput={onInput}
          onKeyDown={onKeyDown}
          placeholder={placeholder}
          ref={inputRef}
          role="searchbox"
          tabIndex={0}
          type="search"
          value={searchQuery}
        />
        {isFullScreenSearch && handleClickCancel && (
          <button className="cancel-button" onClick={handleClickCancel}>
            <Icon icon={faTimes} />
          </button>
        )}
        {showSearchButton && (
          <Button className="search-button" onClick={handleSearch} variant="primary">
            <SearchOutlined />
          </Button>
        )}
      </SearchInputWrapper>
    </SearchInputContainer>
  );
};

const SearchInputContainer = styled.div``;

const SearchInputWrapper = styled.div<{ showSearchIcon?: boolean; showSearchButton?: boolean }>`
  display: flex;
  position: relative;
  width: 100%;
  padding-left: ${({ showSearchIcon }) => (showSearchIcon ? 'unset' : '10px')};
  /* border-radius: ${({ showSearchButton, theme }) => (showSearchButton ? 'unset' : theme.borderRadius.default)}; */

  .search-icon {
    align-items: center;
    color: #378aff;
    display: flex;
    height: 44px;
    justify-content: center;
    width: 44px;
  }

  .cancel-button {
    font-size: 1.2rem;
    padding: 0 10px;
    color: #378aff;
  }

  .search-button {
    border-radius: 0;
    width: 50px;
  }

  input {
    background-color: transparent;
    border-width: 0;
    outline: none;
    width: 100%;
    font-weight: bold;
    font-size: 16px;
  }
`;

export default SearchBar;

export const SelectedOption = ({ onRemove, selected }: { onRemove: (value: Ticker) => void; selected: Ticker }) => (
  <SelectedOptionWrapper>
    <span>{selected?.symbol}</span>
    <div onClick={() => onRemove(selected)}>
      <Icon icon={faTimes} />
    </div>
  </SelectedOptionWrapper>
);

const SelectedOptionWrapper = styled.div`
  font-size: 0.875rem;
  line-height: 1.25rem;
  padding-left: 0.5rem;
  padding-right: 0.25rem;
  background-color: ${({ theme }) => theme.colors.backgroundInactive};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: 0.25rem;
  align-items: center;
  cursor: pointer;
  height: 1.75rem;
  display: inline-flex;

  > span:first-of-type {
    color: ${({ theme }) => theme.colors.foregroundInactive};
    font-weight: 700;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  > div {
    display: flex;
    color: rgba(107, 114, 128, var(--tw-text-opacity));
    font-size: 0.75rem;
    line-height: 1rem;
    border-radius: 9999px;
    justify-content: center;
    align-items: center;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: 5px;

    &:hover {
      background-color: ${({ theme }) => theme.colors.backgroundActive};
    }
  }

  button {
    span:first-child {
      color: ${({ theme }) => theme.colorPalette.gray500};
      margin-right: 5px;
    }
  }

  .error-message-text {
    position: absolute;
    color: ${({ theme }) => theme.colorPalette.red500};
    background-color: ${({ theme }) => theme.colorPalette.red200};
    border: 1px solid ${({ theme }) => theme.colorPalette.red200};
    font-size: 0.75rem;
    line-height: 1rem;
    border-radius: 0.375rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    margin-top: 0.5rem;
    left: 0;
    top: 100%;
    min-width: 13rem;
    opacity: 0;
    pointer-events: none;

    &:hover {
      opacity: 1;
    }
  }
`;
