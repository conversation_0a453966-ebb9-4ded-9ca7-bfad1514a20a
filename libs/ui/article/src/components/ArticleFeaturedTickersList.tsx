import React, { useRef } from 'react';
import styled from '@benzinga/themetron';
import { useTranslation } from 'react-i18next';
import i18n from '@benzinga/translate';
import { OverviewRating, StockInfoHeader, TechnicalsFinancialsAnalysis } from '@benzinga/quotes-ui';
import { AddToWatchlist } from '@benzinga/watchlist-ui';
import { StockInfoChart, RatingsProps } from '@benzinga/quotes-ui';
import { Button, Icon } from '@benzinga/core-ui';
import { faArrowRight } from '@fortawesome/pro-light-svg-icons/faArrowRight';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons/faChevronDown';
import { ArticleFeaturedTicker } from '../entities';
import Hooks from '@benzinga/hooks';
import { RankingDetail } from '@benzinga/quotes-manager';

interface ArticleFeaturedTickersListProps {
  technicals: RatingsProps;
  financials: RatingsProps;
  tickers: ArticleFeaturedTicker[];
  rankingData?: RankingDetail | null;
}

export const ArticleFeaturedTickersList: React.FC<ArticleFeaturedTickersListProps> = ({
  financials,
  rankingData,
  technicals,
  tickers,
}) => {
  const { t } = useTranslation('quote', { i18n });
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const containerScrollInfo = Hooks.useScrollInfo(scrollContainerRef, 150, { passive: true });
  const showFade = containerScrollInfo.bottom !== 0 && containerScrollInfo.percentageScrolled !== 100;

  const ratings = {
    down: technicals.down + financials.down,
    up: technicals.up + financials.up,
  };

  const scrollToBottom = () => {
    const container = scrollContainerRef.current;
    if (container) {
      container.scrollTo({
        behavior: 'smooth',
        top: container.scrollHeight,
      });
    }
  };

  // const technicalAnalysisRating = Math.floor((technicals.up / (technicals.down + technicals.up)) * 100);
  // const financialAnalysisRating = Math.floor((financials.up / (financials.down + financials.up)) * 100);
  // const ratingsExist = !!technicalAnalysisRating && !!financialAnalysisRating;

  return (
    <Container className="article-header-ticker-list h-full">
      <div className="flex flex-col w-full h-full">
        <div className="article-header-main-ticker" key={tickers[0].symbol}>
          <div className="mb-1">
            <StockInfoChart
              candles={tickers[0].candles || []}
              changePercent={tickers[0].changePercent}
              logoUrl={tickers[0].logoUrl}
              name={tickers[0].name}
              price={tickers[0].price}
              rankingData={rankingData}
              symbol={tickers[0].symbol}
              type={tickers[0].type}
            />
          </div>
          {/* {tickers[0].type === 'STOCK' && ratingsExist && (
            <>
              <OverviewRating ratings={ratings} />
              <TechnicalsFinancialsAnalysis financials={financials} technicals={technicals} />
            </>
          )} */}
          {/* <div className="buttons-container flex gap-2 h-8 mt-2 w-full">
            <AddToWatchlist
              buttonText={t('Buttons.watchlist')}
              buttonVariant="flat-blue"
              showIcon={true}
              symbol={tickers[0].symbol}
            />
            <Button
              as="a"
              className="overview-button flex h-8 w-full"
              href={`/quote/${tickers[0].symbol}`}
              rel="noopener noreferrer"
              target="_blank"
            >
              Overview
              <Icon className="text-blue-500" icon={faArrowRight} />
            </Button>
          </div> */}
        </div>
        <div className="block text-center rounded-b-[8px] bg-[#223E6C] mb-3 py-2 uppercase ">
          <a className="block font-semibold text-white" href={`/quote/${tickers[0].symbol}`}>
            Overview
          </a>
        </div>
        {tickers.length > 1 && (
          <div className="relative w-full h-full">
            <div
              className="scrollable-container flex flex-col gap-3 w-full max-h-[220px] overflow-y-auto mt-auto"
              ref={scrollContainerRef}
            >
              {tickers.map((ticker, index) => {
                if (index === 0) {
                  return null;
                }
                return (
                  <StockInfoHeader
                    changePercent={ticker.changePercent}
                    key={ticker.symbol}
                    logoUrl={ticker.logoUrl}
                    name={ticker.name}
                    price={ticker.price}
                    symbol={ticker.symbol}
                    type={ticker.type}
                  />
                );
              })}
            </div>
            {showFade && (
              <div className="fade-bottom">
                <Icon className="scroll-arrow" icon={faChevronDown} onClick={scrollToBottom} />
              </div>
            )}
          </div>
        )}
      </div>
    </Container>
  );
};

const Container = styled.div`
  &.article-header-ticker-list {
    width: 100%;
    max-width: 450px;

    .scrollable-container {
      scrollbar-width: none;
      -ms-overflow-style: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .fade-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 60px;
      background: linear-gradient(to bottom, transparent, #0e1d32 90%);
      pointer-events: none;
      display: flex;
      align-items: flex-end;
      justify-content: flex-end;
      padding: 0 1rem 0.5rem 0;

      .scroll-arrow {
        color: #5b7292;
        background: #192b45;
        border-radius: 50%;
        padding: 0.25rem;
        cursor: pointer;
        pointer-events: auto;
        transition: all 0.2s ease;

        &:hover {
          background: #21334d;
          color: white;
        }
      }
    }

    .article-header-main-ticker {
      background-color: #0e1d32;
      border: 1px solid #2c3d55;
      border-radius: 0.5rem;
      padding: 1rem;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      @media screen and (max-width: 500px) {
        padding: 0.8rem;
      }
    }

    .stock-info-header:not(.article-header-main-ticker *) {
      background-color: #0e1d32;
      border: 1px solid #2c3d55;
      border-radius: 0.5rem;
      padding: 0.8rem 1rem;
    }

    .stock-info-header {
      @media screen and (max-width: 500px) {
        font-size: 0.9rem;
        padding: unset;
      }
    }

    .overview-rating-title {
      color: ${({ theme }) => theme.colorPalette.gray400};
      font-size: ${({ theme }) => theme.fontSize.sm};
    }

    .overview-rating-value {
      font-size: ${({ theme }) => theme.fontSize.lg};
      text-transform: uppercase;
    }

    .technicals-financials-analysis {
      padding: 0;

      .info-tooltip-title {
        color: #5b7292;
        font-size: ${({ theme }) => theme.fontSize.base};
      }

      .range-indicator-value {
        color: ${({ theme }) => theme.colorPalette.white};
      }

      .ratings-section {
        display: flex;
        flex-direction: column;
        width: 100%;
      }

      .divider-wrapper {
        display: none;
      }

      .range-indicator {
        width: 100%;
      }
    }

    .buttons-container {
      > div {
        width: 50%;
      }
    }

    .add-to-watchlist-button {
      flex-direction: row-reverse;
      justify-content: space-between;
      background-color: #192b45;
      border: none;
      line-height: unset;
      width: 100%;
      height: 2rem;

      &:hover {
        background-color: #21334d;
      }

      .watchlist-dropdown-icon {
        margin-right: 0;
      }

      svg {
        color: ${({ theme }) => theme.colorPalette.blue500};
      }
    }

    .overview-button {
      background-color: #192b45;
      border: none;
      color: ${({ theme }) => theme.colorPalette.white};
      justify-content: space-between;
      width: 50%;

      &:hover {
        background-color: #21334d;
      }
    }
  }
`;

export default ArticleFeaturedTickersList;
