'use client';
import React, { useEffect } from 'react';
import classNames from 'classnames';
import styled from '@benzinga/themetron';

import {
  ArticleBlock,
  ArticleData,
  authorTypeGenerator,
  calculateReadTime,
  CampaignStrategy,
  checkIfShouldLoadCalendar,
  getCanonicalUrl,
  getPrimaryTickers,
  getPrimisIdByTaxonomies,
  isSponsoredArticle,
  setDFPTargeting,
} from '@benzinga/article-manager';

import { AccountCreationCTA, ShareButtons } from '@benzinga/ui';
import Hooks, { NoFirstRender, useCustomRouter } from '@benzinga/hooks';
import { SessionContext } from '@benzinga/session-context';
import { appEnvironment, appName } from '@benzinga/utils';
import { MobileShareButtons } from './MobileShareButtons';

import { Popup } from '@benzinga/money';

import { AuthContainer } from '@benzinga/auth-ui';
import { ButtonVariant, ErrorBoundary } from '@benzinga/core-ui';
import { AnalyticsContext, VisiblePageTracker } from '@benzinga/analytics';

import { useIsUserPaywalled } from '@benzinga/user-context';

import { ArticleMainPicture } from './ArticleMainPicture';
//import { Campaign } from './Campaign';
import { NewCampaign } from './NewCampaign';
import { ArticlePopup } from './ArticlePopup';
import { ArticleIFrame } from './ArticleIFrame';
import { ArticleBlocks } from './ArticleBlocks';
import { AuthorContent } from './AuthorContent';
import { AdvertiserDisclosure } from './AdvertiserDisclosure';
import { ArticleHeadlineContent } from './ArticleHeadlineContent';
import { ArticleBodyContent } from './ArticleBody';
import { PostedIn } from './PostedIn';

import { ArticlePageProps } from '../entities';
import { Mode } from './Ads';
import { raptiveAdManager } from '@benzinga/ads-utils';
import { checkDeviceType } from '@benzinga/device-utils';
import { GetBelowArticlePartnerAdBlock } from './GetBelowArticlePartnerAdBlock';
import { getPaywallContentType, getPaywallType } from '../utils';

const BzEdgeCTA = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.BzEdgeCTA })));
// const WNSTNWidget = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.WNSTNWidget })));

const CommentsEmbedScript = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsEmbedScript })),
);
const CommentsDrawerButton = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsDrawerButton })),
);
const CommentsDrawer = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsDrawer })),
);

const KeyPoints = React.lazy(() => import('./KeyPoints'));
const ArticleCalendarWidget = React.lazy(() => import('./ArticleCalendar'));
const RelatedArticles = React.lazy(() => import('./RelatedArticles'));
const NewTaboola = React.lazy(() => import('./Ads/Taboola/NewTaboola'));

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const TaboolaPixelHeader = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.TaboolaPixelHeader };
  }),
);

export interface TaboolaSettings {
  placementMethod?: 'interval' | 'localStorage' | 'below-article';
  unitMode?: Mode;
  unitKey?: string;
}

export interface ArticleLayoutMainProps extends Omit<ArticlePageProps, 'metaProps' | 'nid'> {
  articleData: ArticleData;
  articleScrollViewMoreLink?: string;
  baseUrl?: string;
  disablePartnerAdOnScroll?: boolean;
  disableWNSTNWidget?: boolean;
  hasAdLight: boolean;
  hideTopPanel?: boolean;
  hideTaboola?: boolean;
  isAmp?: boolean;
  isEditor: boolean;
  isBenzingaContributor: boolean;
  isBot: boolean;
  generatedByAi?: boolean;
  googleNewsUrlKey: 'benzinga' | 'benzingaIndia';
  loadMoreButtonVariant?: ButtonVariant;
  isTaggedPressRelease?: boolean;
  isDraft: boolean;
  taboolaSettings?: TaboolaSettings;
  relatedArticles?: ArticleData[];
  showAccountCreationCTA?: boolean;
  showAdvertiserDisclosure?: boolean;
  showApiText?: boolean;
  showCommentButton?: boolean;
  showFontAwesomeIcons?: boolean;
  showWhatsAppIcon?: boolean;
  executeReplaceHistory?: boolean;
  executePageviewEvent?: boolean;
  showPartnerAd?: boolean;
  campaignStrategy?: CampaignStrategy;
  campaignTicker?: string;
  trackMeta?: boolean;
  trackPageView?: boolean;
  primis?: boolean;
  disableCampaign?: boolean;
  raptiveEnabled?: boolean;
  setIsPaywallActive?: (value: boolean) => void;
}

const copyright = `© ${new Date().getFullYear()} Benzinga.com. Benzinga does not provide investment advice. All rights reserved.`;

export const whitelistedAuthors = [
  'NewMediaWire',
  'PressAdvantage',
  'InvestorBrandMedia',
  'Evertise',
  'InvestorBrandNetwork',
  'Newsworthy.ai',
  'Law Firm Newswire',
  'New York Tech',
  'Send2Press',
  'Reportable News',
  '24-7 Press Release',
  'Media OutReach',
  'PRLog',
  'King Newswire',
  'eTrendy Stock',
  'Globe PR Wire',
  'Press Services',
  'Sage Potash',
  'Golden Spike Resources',
  'EdgeClear',
  'Chainwire',
  'PRWire Center',
  'Emily Lew',
  'Jeannine Mancini',
  'Kaili Killpack',
  'Claire Shefchik',
  'Davit Kirakosyan',
  'Adrian Volenik',
  'AJ Fabino',
  'LaToya Scott',
  'Margaret Jackson',
  'Jeff Vasishta',
  'Eric Mcconnell',
  'Paula Tudoran',
  'Finance Wire',
];

const MemoCampaign = React.memo(NewCampaign);

export const ArticleLayoutMain: React.FC<ArticleLayoutMainProps> = ({
  articleData,
  articleIndex,
  campaignSettings = {},
  campaignStrategy,
  campaignTicker,
  deviceType,
  disablePaywall = false,
  //disableWNSTNWidget = false,
  executePageviewEvent = false,
  executeReplaceHistory = false,
  //followUpQuestions,
  generatedByAi = false,
  googleNewsUrlKey,
  hasAdLight = false,
  hideTaboola,
  hideTopPanel = false,
  isAmp = false,
  isBot = true,
  isDraft = false,
  isTemplate = false,
  layout,
  postedInVariant = 'default',
  primis = true,
  raptiveEnabled = false,
  relatedArticles,
  showAccountCreationCTA = true,
  showAdvertiserDisclosure = false,
  showApiText = true,
  showCommentButton = false,
  showFontAwesomeIcons = false,
  showPartnerAd = false,
  showWhatsAppIcon = false,
  taboolaSettings,
  trackMeta = true,
  trackPageView = true,
  wordCount,
}) => {
  const isTagged = React.useCallback(
    tids => {
      return articleData?.tags?.some(tag => tids.includes(tag?.tid));
    },
    [articleData?.tags],
  );
  const isChanneled = React.useCallback(
    tids => {
      return articleData?.channels?.some(channel => tids.includes(channel?.tid));
    },
    [articleData?.channels],
  );
  const [isCommentsDrawerOpen, setIsCommentsDrawerOpen] = React.useState(false);

  const paywall = useIsUserPaywalled('com/read', 'unlimited-articles', getPaywallType(articleData));
  const [isPaywallActive, setIsPaywallActive] = React.useState(disablePaywall ? false : paywall?.active);
  const router = useCustomRouter();
  const session = React.useContext(SessionContext);
  const isSponsored = isSponsoredArticle(articleData);
  const isMarketMovingExclusive = isChanneled([165347]);

  useEffect(() => {
    raptiveAdManager.setReady(false);
    const disableEdgePaywall = () => {
      paywall.disable();
      setIsPaywallActive(false);
    };
    document.addEventListener('om.Campaign.show', disableEdgePaywall);
    return () => {
      document.removeEventListener('om.Campaign.show', disableEdgePaywall);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setIsPaywallActive(disablePaywall ? false : paywall?.active);
  }, [paywall.active, disablePaywall]);

  useEffect(() => {
    const isCommentsDrawerOpenByDefault = router.query.comments_open === 'true';
    if (isCommentsDrawerOpenByDefault) {
      setIsCommentsDrawerOpen(true);
    }
  }, [router?.query?.comments_open]);

  const channels = articleData.channels ? articleData.channels : [];
  const createdAt = articleData.createdAt ? new Date(articleData.createdAt).toISOString() : null;
  const contentType = articleData?.type;
  const tags = articleData.tags ? articleData.tags : [];

  const keyItems = articleData.keyItems && articleData.keyItems.map(item => item.value).filter(item => item?.length);
  const title = articleData.title;
  const renderArticleImage =
    (!!articleData.primaryImage && hasAdLight) ||
    (!!articleData.primaryImage && !!showAdvertiserDisclosure) ||
    (!!articleData.primaryImage && articleData?.type === 'pr_chainwire') ||
    (!!articleData.primaryImage && appEnvironment().isApp(appName.india));

  const primisId = getPrimisIdByTaxonomies(channels, tags);

  const onCampaignsRendered = React.useCallback(() => {
    // renderTickersBoxes('#article-body', true);
  }, []);

  const generateContentWithCampaign = React.useMemo(
    () => (blocks: ArticleBlock[]) => <ArticleBlocks blocks={blocks} />,
    [],
  );

  const articleUrl = getCanonicalUrl(articleData);
  const tickers = getPrimaryTickers(articleData?.tickers);

  const authorName =
    articleData?.author?.firstname && articleData?.author?.lastname
      ? `${articleData?.author.firstname} ${articleData?.author.lastname}`
      : articleData?.author?.name || articleData?.name;

  const onArticleVisible = articleData => {
    setDFPTargeting(articleData);
  };

  const handleArticleCommentOpenAnalyticsEvent = React.useCallback(() => {
    import('@benzinga/comments-ui').then(module => {
      module.handleCommentsAnalyticsEvent('Article', 'open-drawer', session);
    });
  }, [session]);

  const handleOpenCommentsSidebar = () => {
    if (!showCommentButton) return;
    setIsCommentsDrawerOpen(true);
    handleArticleCommentOpenAnalyticsEvent();
  };

  const handleCloseCommentsSidebar = () => {
    if (!showCommentButton) return;
    setIsCommentsDrawerOpen(false);
  };

  const handleToggleCommentsSidebar = React.useCallback(() => {
    if (!showCommentButton) return;
    setIsCommentsDrawerOpen(!isCommentsDrawerOpen);
    !isCommentsDrawerOpen && handleArticleCommentOpenAnalyticsEvent();
  }, [showCommentButton, isCommentsDrawerOpen, handleArticleCommentOpenAnalyticsEvent]);

  return (
    <>
      {!hideTopPanel && (
        <div className="flex flex-col w-full items-start lg:flex-row">
          <ErrorBoundary name="article-layout-main-author-content">
            <AuthorContent
              authorLink={articleData.author?.profileUrl}
              authorName={authorName}
              authorType={authorTypeGenerator(articleData.author?.byLine ?? '', showAdvertiserDisclosure, contentType)}
              authorUid={articleData.author?.uid}
              className="flex flex-grow"
              commentCount={articleData.commentCount}
              created={createdAt ?? ''}
              isDraft={isDraft}
              readTime={calculateReadTime(articleData.body)}
              secondaryByline={articleData.author?.byLine_secondary}
              showCommentButton={showCommentButton}
              toggleCommentsDrawer={handleToggleCommentsSidebar}
              twitter={articleData.author?.twitter}
            />
          </ErrorBoundary>

          <div className="flex items-center lg:ml-auto flex-wrap gap-2">
            <ShareButtons
              className="flex"
              showFontAwesomeIcons={showFontAwesomeIcons}
              showWhatsAppIcon={showWhatsAppIcon}
              tickers={tickers}
              title={title}
              url={articleUrl}
            />
            <NoFirstRender fallback={<div className="h-[40px] w-[128px]" />}>
              <MobileShareButtons
                deviceType={deviceType}
                googleNewsUrlKey={googleNewsUrlKey}
                showAdvertiserDisclosure={showAdvertiserDisclosure}
              />
            </NoFirstRender>
          </div>
        </div>
      )}
      {!!showAdvertiserDisclosure && <AdvertiserDisclosure isAmp={isAmp} slug={layout?.disclosure_slug} />}
      {layout && layout.popup && !isPaywallActive && <ArticlePopup popup={layout.popup} />}
      {layout?.unmonetized_interstitial && !isPaywallActive && (
        <Popup interstitial={true} popup={layout.unmonetized_interstitial} />
      )}
      {keyItems && keyItems.length > 0 && (
        <div className="key-points-wrapper">
          <KeyPoints
            data={keyItems}
            isPaywalled={
              !isBot &&
              !showAdvertiserDisclosure &&
              !whitelistedAuthors.includes(authorName) &&
              isPaywallActive &&
              !isSponsored &&
              !isMarketMovingExclusive
            }
          />
          {!isBot &&
            !showAdvertiserDisclosure &&
            !whitelistedAuthors.includes(authorName) &&
            isPaywallActive &&
            !isSponsored &&
            !isMarketMovingExclusive && (
              <div className="relative">
                <BzEdgeCTA type={isTagged([866728]) ? 'whisper-index' : 'stock-of-the-day'} />
              </div>
            )}
        </div>
      )}
      {!hideTopPanel && (
        <ErrorBoundary name="article-layout-main-picture-block">
          <React.Suspense fallback={<div />}>
            <ArticleMainPicture
              articleData={articleData}
              imageOnly={renderArticleImage}
              primisId={primisId}
              videoPlayerSettings={layout?.video_player}
            />
          </React.Suspense>
        </ErrorBoundary>
      )}
      {/* {!disableWNSTNWidget && followUpQuestions && (
        <div className="my-4 h-[220px]">
          <NoFirstRender>
            <WNSTNWidget articleID={articleData.nodeId} questions={followUpQuestions} />
          </NoFirstRender>
        </div>
      )} */}
      <ArticleContentWrapper
        className={classNames('article-content-body', { 'is-headline': articleData?.isHeadline })}
        id={`node-${articleData.nodeId}`}
      >
        <AnalyticsContext.Provider value={{ label: 'Article Above Content' }}>
          {layout && Array.isArray(layout?.content_header?.blocks) && (
            <ArticleBlocks blocks={layout.content_header.blocks} />
          )}
        </AnalyticsContext.Provider>
        {trackPageView && (
          <ErrorBoundary name="article-layout-main-visible-page-tracker">
            <VisiblePageTracker
              article={articleData}
              executeImpressionEvent={false}
              executePageviewEvent={executePageviewEvent}
              executeReplaceHistory={executeReplaceHistory}
              meta={articleData.metaProps}
              onImpress={() => onArticleVisible(articleData)}
              trackMeta={trackMeta}
            >
              <div style={{ height: 2 }} />
            </VisiblePageTracker>
          </ErrorBoundary>
        )}

        {articleData?.isHeadline ? (
          <ArticleHeadlineContent />
        ) : (
          <>
            {!isBot &&
              !showAdvertiserDisclosure &&
              !whitelistedAuthors.includes(authorName) &&
              isPaywallActive &&
              !isSponsored &&
              !isMarketMovingExclusive && (
                <AuthContainer
                  authMode="register"
                  contentType={getPaywallContentType(articleData)}
                  iterationStyle={paywall?.paywallStyle}
                  placement="article"
                  preventRedirect={true}
                  setShowPaywall={setIsPaywallActive}
                />
              )}
            <AnalyticsContext.Provider value={{ label: 'Article In Content' }}>
              <ArticleBodyContent
                isBot={isBot}
                isPaywalled={
                  !isBot &&
                  !showAdvertiserDisclosure &&
                  !whitelistedAuthors.includes(authorName) &&
                  isPaywallActive &&
                  !isMarketMovingExclusive
                }
                isSponsored={showAdvertiserDisclosure}
              >
                {isTemplate ? null : (
                  <>
                    {isBot ? (
                      <div dangerouslySetInnerHTML={{ __html: articleData.body }} />
                    ) : articleData.shouldDisplayedInFrame ? (
                      <React.Suspense fallback={<div />}>
                        <ErrorBoundary name="article-layout-main-article-iframe">
                          <ArticleIFrame articleBody={articleData.body} />
                        </ErrorBoundary>
                      </React.Suspense>
                    ) : (
                      <ErrorBoundary name="article-layout-main-campaign">
                        <MemoCampaign
                          articleBlocks={articleData.blocks}
                          articleBody={articleData.parsedBody}
                          channels={channels}
                          contentType={articleData.type}
                          createdDate={articleData.createdAt}
                          deviceType={deviceType}
                          hasAdLight={hasAdLight}
                          inContentBlocks={layout?.in_content?.blocks}
                          isSponsored={showAdvertiserDisclosure}
                          nodeId={articleData.nodeId}
                          onRendered={onCampaignsRendered}
                          primis={renderArticleImage ? false : primis}
                          raptiveEnabled={raptiveEnabled}
                          strategy={campaignStrategy}
                          tags={tags}
                          ticker={campaignTicker}
                          wordCount={wordCount}
                          {...campaignSettings}
                        >
                          {generateContentWithCampaign}
                        </MemoCampaign>
                      </ErrorBoundary>
                    )}
                    {generatedByAi && (
                      <p className="block core-block">
                        <em>
                          This content was partially produced with the help of AI tools and was reviewed and published
                          by Benzinga editors.
                        </em>
                      </p>
                    )}

                    {/* eslint-disable-next-line react/jsx-no-target-blank */}
                    {showApiText && (
                      <a
                        className="text-sm my-4"
                        href="https://www.benzinga.com/apis?utm_source=benzinga.com&amp;utm_campaign=article-bottom"
                        rel="noreferrer"
                        target="_blank"
                      >
                        Market News and Data brought to you by Benzinga APIs
                      </a>
                    )}
                    <p className="copyright">{copyright}</p>
                  </>
                )}
              </ArticleBodyContent>
            </AnalyticsContext.Provider>
          </>
        )}

        {showCommentButton && (
          <ErrorBoundary name="article-layout-main-article-comments-section">
            <CommentsEmbedScript
              id={articleData.nodeId as number}
              isInjected={isCommentsDrawerOpen}
              type="Article"
              url={`https://www.benzinga.com/${articleData.canonicalPath}`}
            />
          </ErrorBoundary>
        )}

        {showCommentButton && (
          <ErrorBoundary name="article-layout-main-article-comments-count">
            <CommentsDrawerButton count={articleData.commentCount} onClick={handleOpenCommentsSidebar} />
          </ErrorBoundary>
        )}

        <ErrorBoundary name="article-layout-main-article-comments-drawer">
          <CommentsDrawer isOpen={isCommentsDrawerOpen} onClose={handleCloseCommentsSidebar} />
        </ErrorBoundary>

        {/* This should be moved to be in the article for longer articles */}
        {checkIfShouldLoadCalendar(articleData) && <ArticleCalendarWidget article={articleData} />}

        <React.Suspense fallback={<div />}>
          <div className="mb-4">
            <ShareButtons
              className="flex"
              showFontAwesomeIcons={showFontAwesomeIcons}
              showWhatsAppIcon={showWhatsAppIcon}
              tickers={tickers}
              title={title}
              url={articleUrl}
            />
          </div>
        </React.Suspense>

        {trackPageView && (
          <ErrorBoundary name="article-layout-main-visible-page-tracker">
            <VisiblePageTracker
              article={articleData}
              executeImpressionEvent={false}
              executePageviewEvent={false}
              meta={articleData.metaProps}
              trackMeta={trackMeta}
            >
              <PostedIn terms={articleData.terms ?? []} variant={postedInVariant} />
            </VisiblePageTracker>
          </ErrorBoundary>
        )}

        {raptiveEnabled && (
          <React.Suspense fallback={<div className="h-[90px] w-[300px] mb-2" />}>
            <RaptiveAdPlaceholder
              className="flex items-center justify-center mb-4 min-h-[50px]"
              onlyDesktop={true}
              type="bottom"
            />
          </React.Suspense>
        )}

        {showAccountCreationCTA && <AccountCreationCTA utm="article-cta" />}

        <AnalyticsContext.Provider value={{ label: 'Article Below Content' }}>
          {layout && Array.isArray(layout?.content_footer?.blocks) && (
            <ArticleBlocks blocks={layout.content_footer.blocks} />
          )}
        </AnalyticsContext.Provider>

        {!articleData?.isHeadline && relatedArticles && <RelatedArticles nodes={relatedArticles} />}

        <ErrorBoundary name="article-layout-main-below-article-partner-ad-block">
          {showPartnerAd && !hideTaboola && (
            <GetBelowArticlePartnerAdBlock
              articleIndex={articleIndex}
              id={articleData.nodeId ?? ''}
              taboolaSettings={taboolaSettings}
              url={articleUrl}
            />
          )}
        </ErrorBoundary>

        <hr />
      </ArticleContentWrapper>
      {isSponsored && <TaboolaPixelHeader pageUrl={`${appEnvironment().config().url}/${articleData.canonicalPath}`} />}
    </>
  );
};

export const ArticleContentWrapper = styled.div<{ $useNewTemplate?: boolean }>`
  max-width: ${({ $useNewTemplate }) => ($useNewTemplate ? 'unset' : '720px')};
  margin-left: auto;
  width: 100%;

  ${({ $useNewTemplate }) =>
    !$useNewTemplate &&
    `
    @media (min-width: 1100px) {
      margin-left: auto;
      margin-right: auto;
    }
  `}

  &.is-headline {
    max-width: unset;
  }

  &.article-content-body {
    iframe {
      max-width: 100%;
    }

    em {
      font-style: italic;
    }

    @media (max-width: 1023px) {
      img {
        height: auto !important;
      }
    }
  }

  .article-author-wrap {
    @media (max-width: 500px) {
      font-size: 0.8rem;
    }
  }

  .calendar-wrapper {
    margin: 1rem 0;
  }

  #in-article-ic-placeholder {
    margin-bottom: 0.6rem;
  }

  p {
    margin-bottom: 1rem;

    @media (max-width: 425px) {
      word-break: break-word;
    }
  }

  h3 {
    font-size: 1.4rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
  }

  .article-taxonomy {
    display: flex;
    strong {
      display: flex;
      align-items: center;
    }
    .posted-in-list a {
      background-color: ${({ theme }) => theme.colorPalette.blue100};
      color: ${({ theme }) => theme.colorPalette.blue500};
      border: 1px solid ${({ theme }) => theme.colorPalette.blue500};
      border-radius: 0.25rem;
    }
  }

  .copyright {
    font-size: 13px;
    font-style: italic;
  }

  .offerings-carousel-wrapper {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .youtube-block {
    margin: 2rem 0;
    display: block;
  }

  .comments-count-button {
    margin-bottom: 1rem;
    button {
      padding: 0.75rem 1.5rem;
    }
  }

  @media (min-width: 1100px) {
    margin-left: auto;
    margin-right: auto;
  }
`;

export const ArticleLayoutWrapper = styled.div`
  .layout-header {
    padding: 0;
  }
  .layout-container {
    max-width: 1080px !important;
  }
  .layout-title {
    font-family: sans-serif;

    &__link {
      color: #333;
    }
  }

  h2.wp-block-heading {
    font-weight: 600;
  }

  .show-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8rem;

    .show-box-text {
      height: 8rem;
      font-size: ${({ theme }) => theme.fontSize['2xl']};
    }
  }

  .read-more-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 0 1.5rem 0;
    font-size: 18px;
    text-transform: uppercase;

    a {
      box-shadow: none;
    }

    .chevron-right-icon {
      margin-left: 0.5rem;
    }
  }

  .key-points-wrapper {
    margin-bottom: 1rem;
    margin-top: 0.5rem;
  }

  .article-submitted {
    display: inline-block;
    font-size: 14px;
    margin-bottom: 0.3rem;
  }

  .author-name {
    &:hover {
      text-decoration: underline;
    }
  }

  .follow-author {
    border-radius: 0px;
    font-size: 10px;
    font-weight: 700;
  }

  .video-player-wrapper {
    position: relative;
    margin-bottom: 1rem;

    &:before {
      display: block;
      content: '';
      width: 100%;
      padding-top: 43%;

      @media (max-width: 800px) {
        padding-top: calc(56.25% + 97px);
      }

      @media (max-width: 335px) {
        padding-top: calc(56.25% + 88px);
      }
    }

    &.livestream {
      &:before {
        padding-top: calc((105 / 187) * 100%);
      }
    }

    &__inner {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      height: 100%;
      overflow: hidden;
    }

    #primis_container_div {
      z-index: 1 !important;
    }
  }
`;
