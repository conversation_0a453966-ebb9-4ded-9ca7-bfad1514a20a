'use client';
import React from 'react';
import { useCallback } from 'react';
import { Dropdown } from '@benzinga/core-ui';
import { SelectMenu, StepNumberInput } from '@benzinga/core-ui';
import { AllocationTypeIcon } from '../../components/FinancialAssetAllocation/AllocationTypeIcon';
import { FinancialAssetAllocationType } from '../FinancialAssetAllocation/types';
import { typeLabels } from '../FinancialAssetAllocation/data';
import { Toggle } from './Toggle';
import styled from '@benzinga/themetron';

export type { FinancialAssetAllocationType };
export interface FinancialAssetAllocationSelectProps {
  allocation?: number;
  onAllocationChange?: (value: number) => void;
  onTypeChange?: (value: FinancialAssetAllocationType) => void;
  type?: FinancialAssetAllocationType;
}

export const FinancialAssetAllocationSelect: React.FC<FinancialAssetAllocationSelectProps> = ({
  allocation,
  onAllocationChange,
  onTypeChange,
  type,
}) => {
  const handleTypeChange = useCallback(
    (value: FinancialAssetAllocationType) => {
      if (typeof onTypeChange === 'function') onTypeChange(value);
    },
    [onTypeChange],
  );

  const handleAllocationChange = useCallback(
    (value: number) => {
      if (typeof onAllocationChange === 'function') onAllocationChange(value);
    },
    [onAllocationChange],
  );

  const types = Object.keys(typeLabels).map(value => {
    return { key: value, name: value };
  });

  return (
    <Container className="financial-asset-allocation-select-container">
      <Dropdown target={<Toggle allocation={allocation} type={type} />}>
        <div className="dropdown-content-container">
          <Header title="Type" />
          <div className="financial-asset-allocation-select-options-wrapper">
            <SelectMenu
              onChange={handleTypeChange}
              open={true}
              optionElement={option => (
                <div className="option-allocation-type-container">
                  <AllocationTypeIcon type={option.key} />
                  <span>{typeLabels[option.name]}</span>
                </div>
              )}
              options={types}
              selected={type}
            />
          </div>

          <Header title="Allocation" />
          <div className="allocation-amount-container">
            <StepNumberInput max={5} min={1} onChange={handleAllocationChange} value={allocation} />
            <div>
              <div>Small</div>
              <div>Large</div>
            </div>
          </div>
        </div>
      </Dropdown>
    </Container>
  );
};

FinancialAssetAllocationSelect.defaultProps = {};

const Header = ({ required, title }: { required?: boolean; title: string }) => (
  <HeaderContainer className="header-container bg-gradient-to-r from-blue-50 to-opacity-0">
    <div className="title">{title}</div>
    {!required && <div className="optional-tag">Optional</div>}
  </HeaderContainer>
);

const HeaderContainer = styled.div`
  &.header-container {
    display: flex;
    align-items: center;
    padding-left: 1rem;
    padding-right: 1rem;
    height: 1.75rem;

    .title {
      font-weight: 700;
      font-size: 0.875rem;
      line-height: 1.25rem;
      color: ${({ theme }) => theme.colors.foregroundInactive};
    }

    .optional-tag {
      text-decoration: uppercase;
      font-size: 0.75rem;
      line-height: 1rem;
      color: ${({ theme }) => theme.colors.foregroundInactive};
      margin-left: auto;
    }
  }
`;

const Container = styled.div`
  &.financial-asset-allocation-select-container {
    .dropdown-content-container {
      display: flex;
      flex-direction: column;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
      min-width: 18rem;
      background-color: ${({ theme }) => theme.colors.background};
      color: ${({ theme }) => theme.colors.foregroundInactive};

      .financial-asset-allocation-select-options-wrapper {
        .select-menu {
          width: 100%;
        }
        .select-menu-list {
          padding-top: unset;
          padding-bottom: unset;
          box-shadow: unset;
          background-color: unset;
          border-radius: unset;
          border: unset;
          min-width: unset;
          margin-top: unset;
        }
        .select-menu-list-item {
          padding: 0.25rem 1rem;
        }
        .option-allocation-type-container {
          display: flex;
          font-weight: 700;
          font-size: 0.875rem;
          line-height: 1.25rem;
          align-items: center;
          color: ${({ theme }) => theme.colors.foregroundInactive};
          text-decoration: uppercase;
          user-select: none;

          > span {
            padding-left: 0.5rem;
            margin-left: 10px;
          }
        }
      }
    }

    .allocation-amount-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding-left: 1rem;
      padding-right: 1rem;
      margin-top: 1rem;

      > div {
        &:nth-child(1) {
          margin-bottom: 10px;
        }

        &:nth-child(2) {
          display: flex;
          align-items: center;

          > div {
            font-weight: 700;
            text-decoration: uppercase;
            font-size: 0.875rem;
            line-height: 1.25rem;
            color: ${({ theme }) => theme.colorPalette.gray700};

            &:nth-last-child(1) {
              margin-left: auto;
            }
          }
        }
      }
    }
  }
`;

Header.defaultProps = {
  required: false,
};
