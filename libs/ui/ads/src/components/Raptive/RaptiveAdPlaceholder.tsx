'use client';

import React, { startTransition } from 'react';
import classNames from 'classnames';
import styled from '@benzinga/themetron';
import Hooks, { NoFirstRender } from '@benzinga/hooks';
import { isDesktop as isDesktopViewport, isMobile as isMobileViewport } from '@benzinga/device-utils';
import {
  type RaptiveAdPlacementType,
  raptiveAdWrapperClassnames,
  raptiveAdPlaceholderHeightMap,
  raptiveAdManager,
} from '@benzinga/ads-utils';
import { useBenzingaEdge } from '@benzinga/edge';

export interface RaptiveAdPlaceholderProps {
  className?: string;
  onlyClient?: boolean;
  onlyDesktop?: boolean;
  onlyMobile?: boolean;
  type: RaptiveAdPlacementType;
  minimumHeightEnabled?: boolean;
  disableMargins?: boolean;
  noWrapper?: boolean;
  adLightHidden?: boolean;
}

// Add ?ad_test=benzinga-placeholders to test the ad placements

export const RaptiveAdPlaceholder: React.FC<RaptiveAdPlaceholderProps> = ({
  className: propsClassName,
  disableMargins = false,
  minimumHeightEnabled = true,
  noWrapper = false,
  onlyClient,
  onlyDesktop,
  onlyMobile,
  type,
}) => {
  const [isMobile, setIsMobile] = React.useState<boolean>(false);
  const [isDesktop, setIsDesktop] = React.useState<boolean>(false);

  const adIdRef = React.useRef<string>('');

  const isDisabled = !!useBenzingaEdge().disabled.raptive;

  const hasRegisteredRef = React.useRef(false);

  React.useEffect(() => {
    if (hasRegisteredRef.current) return;

    const _isMobile = isMobileViewport();
    const _isDesktop = isDesktopViewport();

    let requestedAd: string | null = null;

    if (onlyDesktop || onlyMobile) {
      if ((onlyDesktop && _isDesktop) || (onlyMobile && _isMobile)) {
        requestedAd = raptiveAdManager.registerPlaceholder(type);
      }
    } else {
      requestedAd = raptiveAdManager.registerPlaceholder(type);
    }

    if (requestedAd) {
      adIdRef.current = requestedAd;
      hasRegisteredRef.current = true;
    }

    startTransition(() => {
      setIsMobile(_isMobile);
      setIsDesktop(_isDesktop);
    });

    return () => {
      if (adIdRef.current) {
        raptiveAdManager.unregisterPlaceholder(adIdRef.current);
      }
    };
  }, [onlyDesktop, onlyMobile, type]);

  const isMobileClassName = 'flex md:hidden' + (disableMargins ? '' : ' my-2 md:my-0');
  const isDesktopClassName = 'hidden md:flex' + (disableMargins ? '' : ' md:my-2');
  const deviceBasedClassnames = onlyMobile ? isMobileClassName : onlyDesktop ? isDesktopClassName : '';
  const heightClassnames = minimumHeightEnabled ? `${raptiveAdPlaceholderHeightMap[type]}` : '';

  const wrapperClassName = classNames('raptive-ad-placement mx-auto overflow-hidden', {
    [`${deviceBasedClassnames}`]: deviceBasedClassnames,
    [`${heightClassnames}`]: heightClassnames,
    [`${propsClassName}`]: propsClassName,
  });

  const adPlacementClassName = raptiveAdWrapperClassnames[type];

  const canRenderMobile = onlyMobile && isMobile;
  const canRenderDesktop = onlyDesktop && isDesktop;
  const hasNoDeviceRestrictions = !onlyMobile && !onlyDesktop;

  const adElementClassName = classNames(
    adPlacementClassName,
    'w-full flex items-center justify-center',
    noWrapper ? wrapperClassName : '',
  );

  const adElement: React.ReactElement | null = adPlacementClassName ? (
    <div className={adElementClassName} id={adIdRef.current} />
  ) : null;

  let element: React.ReactElement | null = null;

  if (isDisabled) {
    return <div />;
  }

  const renderAdContent = () => {
    if (noWrapper) {
      return adElement;
    }
    if (onlyMobile && !isMobile)
      return (
        <StyledContainer
          className={classNames(
            'min-w-[300px] w-[300px] flex items-center justify-center',
            deviceBasedClassnames,
            heightClassnames,
            propsClassName,
          )}
        />
      );
    if (onlyDesktop && !isDesktop)
      return (
        <StyledContainer
          className={classNames(
            'min-w-[300px] w-[300px] flex items-center justify-center',
            deviceBasedClassnames,
            heightClassnames,
            propsClassName,
          )}
        />
      );
    return (
      <Container className={wrapperClassName} isDisabled={isDisabled}>
        {adElement}
      </Container>
    );
  };

  if (onlyMobile || onlyDesktop) {
    return renderAdContent();
  } else if (onlyClient) {
    if (canRenderMobile || canRenderDesktop) {
      element = renderAdContent();
    }

    return (
      <NoFirstRender
        fallback={
          <div
            className={`min-w-[300px] w-[300px] ${deviceBasedClassnames} ${heightClassnames} raptive-ad-placement`}
          />
        }
      >
        {hasNoDeviceRestrictions ? renderAdContent() : element}
      </NoFirstRender>
    );
  } else {
    return renderAdContent();
  }
};

const Container: React.FC<React.PropsWithChildren<{ className?: string; isDisabled: boolean }>> = ({
  children,
  className,
  isDisabled,
}) => {
  if (isDisabled) return null;
  return (
    <StyledContainer className={`raptive-ad-wrapper ${className} flex flex-col items-center justify-center`}>
      {children}
    </StyledContainer>
  );
};

const StyledContainer = styled.div`
  .adthrive-ad {
    margin-bottom: 0;
    margin-top: 0;
  }
`;
