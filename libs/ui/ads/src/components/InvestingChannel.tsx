'use client';
import React, { useCallback, useState } from 'react';
import * as ReactDOMClient from 'react-dom/client';
import Hooks from '@benzinga/hooks';
import { runningClientSide } from '@benzinga/utils';
import { isMobile } from '@benzinga/device-utils';
import VizSensor from 'react-visibility-sensor';
import styled from '@benzinga/themetron';
import { elementIsVisibleInViewport } from '@benzinga/frontend-utils';

interface AdditionalParams {
  load_type?: string;
}

interface Props {
  className?: string;
  id: string;
  size?: string;
  mobileSize?: string;
  height?: number | string;
  renderPlaceholder?: boolean;
  native?: NativeParam | boolean;
  sensor?: boolean;
  adSlot?: string;
  params?: AdditionalParams;
}

interface NativeParam {
  adSlot?: string;
  nativeLayoutId?: number;
}

interface OnlyScriptProps {
  className?: string;
  id: string;
  size?: string;
  sensor?: boolean;
  mobileSize?: string;
  height?: number | string;
  renderPlaceholder: boolean;
  native?: NativeParam | boolean;
  adSlot?: string;
  params?: AdditionalParams;
}

interface ICTag {
  adSlot?: string;
  site: string;
  size: string;
  id: string;
  native?: boolean;
  nativeLayoutId?: number;
}

export const UAT_KEY = '856729bc-429d-4b8d-8d68-ced83505ab1e';
const DEFAULT_IC_ZONE = 'equities';

export const getZone = (): string => {
  if (!runningClientSide()) {
    return '';
  }

  const path = window.location.pathname;
  const params = path.split('/');

  let zone = DEFAULT_IC_ZONE;

  if (Array.isArray(params) && params.length) {
    params.splice(0, 1);

    const mainSection = params[0];
    const subSection = params[1];

    switch (mainSection) {
      case 'etfs':
        zone = 'etf';
        break;
      case 'markets':
        if (subSection === 'forex') {
          zone = 'forex';
        } else if (subSection === 'commodities') {
          zone = 'commodities';
        } else if (subSection === 'options' || subSection === 'binary-options') {
          zone = 'options';
        } else if (subSection === 'bonds') {
          zone = 'bonds';
        } else if (subSection === 'futures') {
          zone = 'futures';
        } else if (subSection === 'cannabis') {
          zone = 'cannabis';
        } else if (subSection === 'psychedelics') {
          zone = 'psychedelics';
        }
        break;
      case 'news':
        if (subSection === 'dividends') {
          zone = 'dividends';
        }
        break;
      case 'personal-finance':
        zone = 'personalfinance';
        break;
      case 'stock':
        zone = 'quotepages';
        break;
      case 'trading-ideas':
        if (subSection === 'technicals') {
          zone = 'technicalanalysis';
        }
        break;
      default:
        zone = DEFAULT_IC_ZONE;
        break;
    }
  }

  return zone;
};

export const initIC = (): void => {
  if (!runningClientSide()) {
    return;
  }

  const InvestingChannelQueue = window['InvestingChannelQueue'] || [];
  const InvestingChannelTags = window['InvestingChannelTags'] || [];

  const ic_page = window['InvestingChannel']?.UAT?.Run(UAT_KEY, {});

  window['AddInvestingChannelTag'] = AddInvestingChannelTag;
  window['InvestingChannelReady'] = true;
  window.dispatchEvent(new Event('ic-ready'));

  setTimeout(() => {
    InvestingChannelTags.forEach((tag: ICTag) => {
      InvestingChannelQueue.push(() => {
        // site/zone, size, div-id
        if (tag.native) {
          const nativeTag = ic_page.defineNativeTag(tag.site, tag.size, tag.id, tag.nativeLayoutId);
          nativeTag.setKval({ adslot: tag.adSlot });
        } else {
          ic_page.defineTag(tag.site, tag.size, tag.id);
        }
      });
    });

    InvestingChannelQueue.push(() => {
      ic_page.renderTags();
    });
  }, 0);

  window.dispatchEvent(new Event('ic-rendered-tags'));
  window['InvestingChannelCompleted'] = true;
};

export const loadICScript = (): Promise<boolean> => {
  return new Promise(resolve => {
    if (runningClientSide()) {
      const scriptId = 'investing-channel-script';

      const scriptElement = document.getElementById(scriptId);

      if (!scriptElement) {
        const scriptElement = document.createElement('script');
        scriptElement.src = 'https://u5.investingchannel.com/static/uat.js';
        scriptElement.id = scriptId;
        scriptElement.addEventListener('load', () => {
          initIC();
          scriptElement.setAttribute('loaded', 'true');
          resolve(true);
        });

        document.body.appendChild(scriptElement);
      } else if (!scriptElement.getAttribute('loaded')) {
        scriptElement.addEventListener('load', () => {
          resolve(true);
        });
      } else {
        resolve(true);
      }
    }
  });
};

export const RenderTag = (
  elementId: string,
  size: string,
  zone: string,
  params?: AdditionalParams,
  adSlot?: string,
): void => {
  if (!runningClientSide()) {
    return;
  }

  const InvestingChannelQueue = window['InvestingChannelQueue'] || [];
  const ic_page = window['InvestingChannel']?.UAT?.GetPage();
  if (ic_page) {
    InvestingChannelQueue.push(() => {
      const tag = ic_page.defineTag(zone, size, elementId, params ?? {});

      if (adSlot) {
        tag.setKval({ adslot: adSlot });
      }

      if (tag) {
        tag.render();
      }
    });
  }
};

export const RenderNativeTag = (
  elementId: string,
  size: string,
  zone: string,
  layoutId = 29,
  adSlot = 'IC_D_3x7_1',
): void => {
  if (!runningClientSide()) {
    return;
  }

  const InvestingChannelQueue = window['InvestingChannelQueue'] || [];
  const ic_page = window['InvestingChannel']?.UAT?.GetPage();
  if (ic_page) {
    let tag;
    InvestingChannelQueue.push(() => {
      tag = ic_page.defineNativeTag(zone, size, elementId, layoutId);
      if (tag) {
        tag.setKval({ adslot: adSlot });
        tag.render();
      }
    });
  }
};

export const AddInvestingChannelTag = (
  elementId: string,
  size: string,
  zone?: string,
  native?: NativeParam | boolean,
  adSlot?: string,
  params?: AdditionalParams,
): ICTag[] => {
  if (!runningClientSide()) {
    return [];
  }

  if (!Array.isArray(window['InvestingChannelTags'])) window['InvestingChannelTags'] = [];

  const siteZone = zone || `benzinga/${getZone()}`;

  if (!window['InvestingChannelCompleted'] || !window['InvestingChannel']?.UAT) {
    let tag = {
      id: elementId,
      site: siteZone,
      size: size,
    };

    if (typeof native === 'object') {
      tag = {
        ...tag,
        ...native,
      };
    }

    window['InvestingChannelTags'].push(tag);
  } else {
    setTimeout(() => {
      if (typeof native === 'object') {
        RenderNativeTag(elementId, size, siteZone, native.nativeLayoutId, native.adSlot);
      } else {
        RenderTag(elementId, size, siteZone, params, adSlot);
      }
    }, 0);
  }

  return window['InvestingChannelTags'];
};

export const DEFAULT_MOBILE_SIZE = '300x50,320x50,320x100,300x250,fluid';
export const DEFAULT_DESKTOP_SIZE = '3x7,300x250,fluid';

export const InvestingChannel: React.FC<Props | OnlyScriptProps> = ({
  adSlot,
  className,
  height,
  id,
  mobileSize,
  native,
  params,
  renderPlaceholder,
  sensor,
  size,
}) => {
  const [isRendered, setIsRendered] = useState(false);
  const [placeholderMinHeight, setPlaceholderMinHeight] = useState<number | string>(height ?? 0);
  const placeholderRef = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    if (placeholderRef.current) {
      if (!elementIsVisibleInViewport(placeholderRef.current, true)) {
        setPlaceholderMinHeight(0);
      }
    }
  }, [placeholderRef]);

  const getSizeByElementId = (id: string) => {
    if (id) {
      const element = document.querySelector('#' + id) as HTMLElement;

      if (element) {
        if (isMobile()) {
          if (element.dataset.mobileSize === 'null') {
            return null;
          } else {
            return element.dataset.mobileSize || DEFAULT_MOBILE_SIZE;
          }
        } else {
          if (element.dataset.size === 'null') {
            return null;
          } else {
            return element.dataset.size || DEFAULT_DESKTOP_SIZE;
          }
        }
      }
    }

    return null;
  };

  const getMobileSize = useCallback((): string | null => {
    if (mobileSize === null) {
      return null;
    }

    const size = mobileSize || DEFAULT_MOBILE_SIZE;

    if (typeof size === 'string') {
      const split = size.split('x');

      if (window.innerWidth > Number(split?.[0])) {
        return size;
      }
    }

    return DEFAULT_MOBILE_SIZE;
  }, [mobileSize]);

  const renderIC = useCallback(
    (isVisible: boolean) => {
      if (!isRendered && isVisible) {
        loadICScript().then(() => {
          let tagSize: string | null | undefined = null;

          if (id && renderPlaceholder === false) {
            tagSize = getSizeByElementId(id);
          }

          if (!tagSize) {
            if (isMobile()) {
              tagSize = getMobileSize();
            } else {
              if (size === null) {
                tagSize = null;
              } else {
                tagSize = size || DEFAULT_DESKTOP_SIZE;
              }
            }
          }

          if (tagSize) {
            AddInvestingChannelTag(id, tagSize, undefined, native, adSlot, params);
          }

          setIsRendered(true);
        });
      }
    },
    [native, getMobileSize, id, isRendered, renderPlaceholder, size, params, adSlot],
  );

  Hooks.useEffectDidMount(() => {
    if (renderPlaceholder === false && id && sensor !== false) {
      if (getSizeByElementId(id)) {
        const element = document.querySelector('#' + id) as HTMLElement;
        if (element) {
          const root = ReactDOMClient.createRoot(element);
          root.render(<VizSensor offset={{ top: 100 }} onChange={(isVisible: boolean) => renderIC(isVisible)} />);
        }
      }
    } else if (sensor === false) {
      setTimeout(() => {
        renderIC(true);
      });
    }
  });

  if (renderPlaceholder === false) {
    return null;
  }

  const investChannelPlaceholder = () => {
    return (
      <InvestingChannelWrapper>
        <div
          className={className}
          data-mobile-size={mobileSize}
          data-size={size}
          id={id}
          ref={placeholderRef}
          style={placeholderMinHeight ? { minHeight: `${placeholderMinHeight}px` } : {}}
        />
      </InvestingChannelWrapper>
    );
  };

  if ((size !== null || (mobileSize !== null && isMobile())) && sensor !== false) {
    return (
      <VizSensor offset={{ top: 100 }} onChange={(isVisible: boolean) => renderIC(isVisible)}>
        {investChannelPlaceholder()}
      </VizSensor>
    );
  }

  return <>{investChannelPlaceholder()}</>;
};

export const BottomMargin = '1rem';

const InvestingChannelWrapper = styled.div`
  justify-content: center;
  display: flex;
  margin: ${BottomMargin} auto;

  div {
    min-width: 1px;
    min-height: 1px;
  }

  span {
    display: block;
    min-height: 1px;
    min-width: 1px;
  }
`;
