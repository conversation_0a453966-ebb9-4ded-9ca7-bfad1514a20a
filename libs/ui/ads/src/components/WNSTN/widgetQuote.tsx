import React from 'react';

import { WidgetType, WNSTNWidget } from './widget';
import { getCRYPTOQuestions, getQuoteQuestions, getRandomQuestions } from './utils';
import { DEFAULT_QUESTIONS } from './widget';
import { NoFirstRender } from '@benzinga/hooks';

const FloatingWNSTNWidget = React.lazy(() =>
  import('./floatingWidget').then(module => ({ default: module.FloatingWNSTNWidget })),
);

interface Props {
  symbol?: string;
  isCrypto?: boolean;
}

export const WNSTNWidgetQuote: React.FC<Props> = ({ isCrypto, symbol }) => {
  const randomQuestions = React.useMemo(() => {
    if (!symbol) {
      return DEFAULT_QUESTIONS;
    }

    const questions = isCrypto ? getCRYPTOQuestions(symbol) : getQuoteQuestions(symbol);

    return getRandomQuestions(questions, 5);
  }, [symbol, isCrypto]);

  return (
    <NoFirstRender>
      <FloatingWNSTNWidget questions={randomQuestions} widgetType={WidgetType.Asset} />
      {/* <WNSTNWidget questions={randomQuestions} small title={'Got Questions? Ask'} widgetType={WidgetType.Asset} /> */}
    </NoFirstRender>
  );
};
