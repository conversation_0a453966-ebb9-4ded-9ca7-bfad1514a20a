'use client';
import React, { useEffect, useRef, useCallback } from 'react';
import { runningClientSide } from '@benzinga/utils';
import { WidgetType } from './widget';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useUser } from '@benzinga/user-context';
import { WidgetModal } from './widgetModal';
import styles from './floatingWidget.module.scss';

interface FloatingWNSTNWidgetProps {
  widgetType?: WidgetType;
  questions?: string[] | null;
  articleID?: number;
  symbol?: string;
  isCrypto?: boolean;
}

export const FloatingWNSTNWidget: React.FC<FloatingWNSTNWidgetProps> = ({
  articleID,
  widgetType = WidgetType.FollowUp,
}) => {
  const elementRef = useRef<HTMLButtonElement>(null);
  const impressed = useRef<boolean>(false);
  const session = React.useContext(SessionContext);
  const user = useUser();

  useEffect(() => {
    if (!impressed.current && runningClientSide()) {
      impressed.current = true;
      const eventName = widgetType === WidgetType.Asset ? 'asset_wnstn_widget_view' : 'follow_up_wnstn_widget_view';
      session.getManager(TrackingManager).trackWnstnEvent(eventName, {
        page_link: window.location.href,
      });
    }
  }, [session, widgetType]);

  const modalRef = useRef<HTMLElement | null>(null);

  const loadWidget = useCallback(() => {
    if (!runningClientSide()) return;

    const accessType = user?.accessType === 'subscribed' || user?.accessType === 'trialing' ? 'pro' : 'www';
    window['chatInitParams'] = {
      article_index: articleID ? String(articleID) : undefined,
      proxy: `https://wnstn-api.benzinga.com/${accessType}`,
      userId: String(user?.id || ''),
    };

    const widgetContainer = document.querySelector('.my-chat-container-container__widget');
    if (widgetContainer) {
      widgetContainer.innerHTML = '';

      const script = document.createElement('script');
      script.id = 'wnstn-widget-script';
      script.src = 'https://widget.wnstn.ai/wnstnai/createWidget.js';
      script.defer = true;

      const existingScript = document.getElementById('wnstn-widget-script');
      if (existingScript) {
        existingScript.remove();
      }

      widgetContainer.appendChild(script);
    }
  }, [articleID, user?.accessType, user?.id]);

  const toggleModal = useCallback(() => {
    if (!runningClientSide()) return;

    if (!modalRef.current) {
      modalRef.current = document.getElementById('wnstn-widget-modal');
    }

    if (modalRef.current) {
      const isCurrentlyVisible = modalRef.current.style.display === 'flex';

      if (!isCurrentlyVisible) {
        try {
          const trackingManager = session?.getManager(TrackingManager);
          if (trackingManager) {
            trackingManager.trackWnstnEvent(
              widgetType === WidgetType.Asset ? 'asset_wnstn_widget_click' : 'follow_up_wnstn_widget_click',
              {
                node_id: articleID ? String(articleID) : undefined,
                page_link: window.location.href,
              },
            );
          }
        } catch (error) {
          console.error('Error tracking modal open:', error);
        }

        loadWidget();

        modalRef.current.style.display = 'flex';
      } else {
        modalRef.current.style.display = 'none';
      }
    }
  }, [articleID, loadWidget, session, widgetType]);

  const toggleCollapsed = useCallback(() => {
    if (!user?.id) {
      const redirectUrl = new URL(`${window.location.origin}/login?action=login&next=${window.location.href}`);
      window.location.href = redirectUrl.toString();
      return;
    }
    toggleModal();
  }, [toggleModal, user?.id]);

  const handleModalClose = useCallback(() => {
    if (modalRef.current) {
      modalRef.current.style.display = 'none';
    } else {
      const modal = document.getElementById('wnstn-widget-modal');
      if (modal) {
        modal.style.display = 'none';
      }
    }
  }, []);

  return (
    <>
      <WidgetModal onClose={handleModalClose} />
      <button
        aria-label="Open AI Assistant"
        className={styles.collapsedButton}
        onClick={toggleCollapsed}
        ref={elementRef}
      >
        <span className={styles.aiText}>AI</span>
      </button>
    </>
  );
};

export default FloatingWNSTNWidget;
