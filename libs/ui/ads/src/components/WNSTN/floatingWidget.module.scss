@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.collapsedButton {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 99999;
  width: 60px;
  height: 60px;
  border-radius: 50% 50% 0 50%;
  background: linear-gradient(135deg, #3F83F8 0%, #2563EB 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  outline: none;
  overflow: hidden;
  transition: transform 0.3s ease;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: fadeInScale 0.5s ease-out;

  &:hover {
    transform: scale(1.05);
    box-shadow:
      0 6px 16px rgba(0, 0, 0, 0.2),
      0 12px 32px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.15);
  }

  @media screen and (max-width: 756px) {
    right: 10px;
    bottom: 70px;
  }
}

.aiText {
  font-size: 20px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
