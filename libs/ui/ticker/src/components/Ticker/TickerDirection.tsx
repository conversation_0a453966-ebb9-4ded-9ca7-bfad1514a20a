import React from 'react';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import styled from '@benzinga/themetron';

export interface TickerDirectionProps {
  direction: 'up' | 'down';
}

export const TickerDirection: React.FC<TickerDirectionProps> = ({ direction }) => {
  return (
    <TickerDirectionWrapper className="direction" direction={direction}>
      {direction === 'up' ? <CaretUpOutlined /> : <CaretDownOutlined />}
    </TickerDirectionWrapper>
  );
};

export const TickerDirectionWrapper = styled.div<TickerDirectionProps>`
  color: ${({ direction, theme }) => (direction === 'up' ? theme.colorPalette.green500 : theme.colorPalette.red500)};
  padding-bottom: 0.5rem;
`;
