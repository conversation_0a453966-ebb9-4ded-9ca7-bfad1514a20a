import { useMemo } from 'react';
import { usePathname } from 'next/navigation';

export const getColor = (value: number): string => {
  if (value === 0) return 'text-[#b8b8b8]';
  if (value <= 33) return 'text-red-500';
  if (value <= 66) return 'text-yellow-500';
  return 'text-green-500';
};

type PaywallText = [string, string, string, string];

export const getPaywallText = (adType = 'tickerhover'): PaywallText => {
  const currentDate = new Date();
  const stableRandomSeed = currentDate.getDate() % 2;

  return generatePaywallText(stableRandomSeed, adType);
};

export const usePaywallText = (adType = 'tickerhover'): PaywallText => {
  // const promoTextVersion = useHydrate(Math.floor(Math.random() * 2), 0);
  const pathname = usePathname();

  return useMemo(() => {
    const pathHash = pathname?.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) || 0;
    const versionId = pathHash % 2;

    return generatePaywallText(versionId, adType);
  }, [adType, pathname]);
};

function generatePaywallText(versionId: number, adType: string): PaywallText {
  // const promoTextVersion = Math.floor(Math.random() * 2);

  let paywallLabel = 'Stock Score Locked: Want to See it?';
  const paywallDesc = 'Benzinga Rankings give you vital metrics on any stock – anytime.';
  let paywallButtonLabel = 'Reveal Full Score';
  let ad = 'rankingv1';

  if (versionId === 1) {
    paywallLabel = 'Stock Score Locked: Edge Members Only';
    paywallButtonLabel = 'Unlock Rankings';
    ad = 'rankingv2';
  }

  const buyNowLink = `https://www.benzinga.com/premium/ideas/benzinga-edge?adType=${adType}&campaign=ranking&ad=${ad}`;
  return [paywallLabel, paywallDesc, paywallButtonLabel, buyNowLink];
}
