import React from 'react';
import styled from '@benzinga/themetron';

import { Tooltip, Icon, Button } from '@benzinga/core-ui';
import { faInfoCircle } from '@fortawesome/pro-regular-svg-icons';
import { RankingDetail } from '@benzinga/quotes-manager';
import { RankingPositive } from './RankingPositive';
import { RankingNegative } from './RankingNegative';
import { EdgeRankingQuoteLayout } from './EdgeRankingQuoteLayout';
import { EdgeRankingCryptoQuoteLayout } from './EdgeRankingCryptoQuoteLayout';
import { getColor, usePaywallText } from './utils';

interface EdgeRankingProps extends RankingDetail {
  adType: string;
  className?: string;
  variant: string;
  layout?: string;
  symbol: string;
  showRanking?: boolean;
}

export const EdgeRanking: React.FC<EdgeRankingProps> = ({
  adType,
  className = '',
  exists,
  growth,
  layout = 'default',
  longTermTrend = 'Y',
  mediumTermTrend = 'Y',
  momentum,
  quality,
  shortTermTrend = 'Y',
  showRanking = false,
  symbol,
  value,
  variant = 'light',
}) => {
  const hasAllData = growth === 0 && quality === 0 && value === 0 ? 'only-momentum' : '';
  const textColor = variant === 'dark' ? 'text-white' : 'text-[#192940]';
  const backgroundColorStyle = variant === 'dark' ? 'bg-[#192940cc]' : 'bg-[#ffffffcc]';
  const [paywallLabel, paywallDesc, paywallButtonLabel, buyNowLink] = usePaywallText(adType);

  return layout === 'quote' ? (
    <EdgeRankingQuoteLayout
      adType={adType}
      className={className}
      exists={exists}
      growth={growth}
      longTermTrend={longTermTrend}
      mediumTermTrend={mediumTermTrend}
      momentum={momentum}
      quality={quality}
      shortTermTrend={shortTermTrend}
      showRanking={showRanking}
      symbol={symbol}
      value={value}
    />
  ) : layout === 'crypto_etf' ? (
    <EdgeRankingCryptoQuoteLayout
      adType={adType}
      className={className}
      exists={exists}
      growth={growth}
      longTermTrend={longTermTrend}
      mediumTermTrend={mediumTermTrend}
      momentum={momentum}
      quality={quality}
      shortTermTrend={shortTermTrend}
      showRanking={showRanking}
      symbol={symbol}
      value={value}
    />
  ) : (
    <EdgeRankingContainer className={`layout-${variant} relative`}>
      {!showRanking && (
        <div className={`${backgroundColorStyle} flex flex-col items-center justify-center px-6 edge-banner`}>
          <div className="paywall-data">
            <h3 className={`${textColor}`}>{paywallLabel}</h3>
            <p className={`${textColor}`}>{paywallDesc}</p>
            <Button as={'a'} href={buyNowLink} variant="flat-blue">
              {paywallButtonLabel}
            </Button>
          </div>
        </div>
      )}
      <div className={`${!showRanking ? 'paywalled' : ''} `}>
        <div className="flex flex-row gap-2 pb-3 ranking-header">
          <h6 className={`${textColor} uppercase pb-0`}>Edge Rankings</h6>
          <Tooltip
            content={
              'Benzinga Edge stock rankings give you four critical scores to help you identify the strongest and weakest stocks to buy and sell.'
            }
            width={250}
          >
            <Icon
              className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
              icon={faInfoCircle}
            />
          </Tooltip>
        </div>
        <div className={`flex justify-between mt-1 w-full data-${hasAllData}`}>
          <EdgeRankingItem label="Momentum" tooltipText={TOOLTIP_TEXTS['Momentum']} value={momentum} />
          {!hasAllData && (
            <>
              <EdgeRankingItem label="Growth" tooltipText={TOOLTIP_TEXTS['Growth']} value={growth} />
              <EdgeRankingItem label="Quality" tooltipText={TOOLTIP_TEXTS['Quality']} value={quality} />
              <EdgeRankingItem label="Value" tooltipText={TOOLTIP_TEXTS['Value']} value={value} />
            </>
          )}
        </div>
        <div className="flex flex-row justify-between w-full mt-2 gap-2 price-trend">
          <div className="flex flex-col w-full">
            <h6 className={`${textColor}`}>Price Trend</h6>
            <div className="flex gap-2 w-full justify-between">
              <EdgeRankingPriceTrend
                label="Short"
                textColor={textColor}
                tooltipText={TOOLTIP_TEXTS['Short']}
                value={shortTermTrend}
              />
              <EdgeRankingPriceTrend
                label="Medium"
                textColor={textColor}
                tooltipText={TOOLTIP_TEXTS['Medium']}
                value={mediumTermTrend}
              />
              <EdgeRankingPriceTrend
                label="Long"
                textColor={textColor}
                tooltipText={TOOLTIP_TEXTS['Long']}
                value={longTermTrend}
              />
            </div>
          </div>
        </div>
      </div>
    </EdgeRankingContainer>
  );
};

const EdgeRankingItem: React.FC<{ label: string; value: number; tooltipText: string }> = ({
  label,
  tooltipText,
  value,
}) => {
  return (
    <div className="flex flex-col items-center edge-ranking-item">
      <span className="item-label flex items-center gap-2">
        {label}
        <Tooltip content={tooltipText} width={250}>
          <Icon
            className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
            icon={faInfoCircle}
          />
        </Tooltip>
      </span>
      <span className={`${value ? getColor(value) : 'text-[#b8b8b8]'} item-value`}>
        {!value ? '-' : value.toFixed(2)}
      </span>
    </div>
  );
};

const EdgeRankingPriceTrend: React.FC<{ label: string; value: string; textColor: string; tooltipText: string }> = ({
  label,
  textColor,
  tooltipText,
  value,
}) => {
  return (
    <div className="position-item flex items-center justify-between flex-1">
      <span className={`flex items-center gap-1 ${textColor} position-label`}>
        {label}
        <Tooltip content={tooltipText} width={250}>
          <Icon
            className="info-circle-icon text-sm flex items-center justify-center text-[#99AECC]"
            icon={faInfoCircle}
          />
        </Tooltip>
      </span>
      <div className={`${value === 'Y' ? 'positive' : 'negative'} position-icon`}>
        {value === 'Y' ? <RankingPositive /> : <RankingNegative />}
      </div>
    </div>
  );
};

export const TOOLTIP_TEXTS: Record<string, string> = {
  Growth:
    "Growth measures a stock's combined historical expansion in earnings and revenue across multiple time periods, with emphasis on both long-term trends and recent performance.",
  Long: 'Shows if a stock is in an upward or downward trend over the past year.',
  Medium: 'Shows if a stock is in an upward or downward trend over the last couple of quarters.',
  Momentum:
    "Momentum measures a stock's relative strength based on its price movement patterns and volatility over multiple timeframes, ranked as a percentile against other stocks.",
  Quality:
    "Quality is a composite ranking that evaluates a company's operational efficiency and financial health by analyzing historical profitability metrics and fundamental strength indicators on a percentile basis relative to peers.",
  Short: 'Shows if a stock is in an upward or downward trend over the last couple of months.',
  Value:
    "Value is a percentile-ranked composite metric that evaluates a stock’s relative worth by comparing its market price to fundamental measures of the company's assets, earnings, sales, and operating performance.",
};

const EdgeRankingContainer = styled.div`
  .paywalled {
    filter: blur(5px);
    user-select: none;
    pointer-events: none;
  }
  font-family: Manrope, Manrope-fallback, sans-serif;
  .edge-ranking-item {
    .item-label {
      font-size: 14px;
      color: rgba(153, 174, 204, 1);
    }
    .item-value {
      font-size: 20px;
      font-weight: 700;
    }
  }
  .price-trend {
    h6 {
      font-size: 14px;
      font-weight: normal;
      color: rgba(153, 174, 204, 1);
    }
  }

  .position-item {
    border: 1px solid #283d59;
    border-radius: 4px;
    padding: 0.5rem;
    .position-label {
      font-size: 16px;
    }
    .position-icon {
      border-radius: 4px;
      padding: 7px;
      &.positive {
        background: rgba(48, 191, 96, 0.25);
      }
      &.negative {
        background: rgba(255, 64, 80, 0.25);
      }
    }
  }
  .data-only-momentum {
    .edge-ranking-item {
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      align-items: center;
      background: rgba(25, 41, 64, 1);
      padding: 0.5rem;
      border-radius: 4px;
      .item-label {
        color: white;
        font-size: 16px;
        font-weight: 600;
      }
      .item-value {
        font-size: 24px;
      }
    }
  }

  .edge-banner {
    position: absolute;
    z-index: 5;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    top: -1rem;
    left: -1rem;
    right: -1rem;
    bottom: -1.5rem;
    text-align: center;
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 8px;
      white-space: wrap;
    }
    p {
      font-size: 1rem;
      font-weight: 400;
      margin-bottom: 16px;
      margin-top: 0;
      white-space: wrap;
    }
    a {
      width: auto;
    }
  }

  &.layout-light {
    .data-only-momentum {
      .edge-ranking-item {
        border: 2px solid rgba(225, 235, 250, 1);
        background: white;
        padding: 0.5rem;
        .item-label {
          color: rgba(57, 81, 115, 1);
        }
      }
    }
    .ranking-header {
      padding-bottom: 2px;
    }
    .price-trend {
      margin-top: 1rem;
    }
    h6 {
      color: rgba(57, 81, 115, 1);
    }
    .edge-ranking-item {
      .item-label {
        font-size: 14px;
        color: rgba(57, 81, 115, 1);
      }
      .item-value {
        font-size: 16px;
      }
    }

    .position-item {
      border: 2px solid rgba(225, 235, 250, 1);
      border-radius: 4px;
      padding: 0.2rem;
      .position-label {
        font-size: 12px;
        color: rgba(91, 114, 146, 1);
        font-weight: 500;
      }
    }

    .edge-banner {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 10px;
      max-width: 300px;
      .paywall-data {
        max-width: 100%;
      }
    }
  }
`;
