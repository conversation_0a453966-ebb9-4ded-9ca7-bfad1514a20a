import React from 'react';
import classNames from 'classnames';

export interface TickerTileProps {
  dark: boolean;
  logo: string;
  name: string;
  symbol: string;
  value: number;
}

export const TickerTile: React.FC<TickerTileProps> = ({ dark, logo, name, symbol, value }) => {
  return (
    <div className={classNames({ dark: dark })}>
      <div className="dark:border-gray-700 border-2 border-gray-200 flex h-24 items-center justify-center p-2 rounded text-center w-32">
        <div className="flex-col inline-flex items-center">
          {logo && (
            <div className="-ml-px -mt-px -mb-px bg-gray-200 flex-row h-6 inline-flex items-center justify-center rounded w-6">
              <img alt={symbol} className="w-auto h-3.5" src={logo} />
            </div>
          )}

          <div className="my-1">
            <span className="font-bold text-xs dark:text-gray-100">{symbol}</span>
            {typeof value === 'number' && (
              <span
                className={classNames('m-1 font-bold text-xs', {
                  'text-green-400': value > 0,
                  'text-red-400': value < 0,
                })}
              >
                {value > 0 ? `+${value}%` : `${value}%`}
              </span>
            )}
          </div>

          <div>
            <div className="text-xs dark:text-gray-400 truncate-2-lines">{name}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

TickerTile.defaultProps = {
  dark: false,
};
