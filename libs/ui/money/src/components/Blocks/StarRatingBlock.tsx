import React from 'react';
import { StarRating } from '@benzinga/core-ui';
export interface StarRatingBlockProps {
  id?: string;
  attrs: {
    data: {
      heading: string;
      level: string;
      rating: number;
    };
  };
}

export const StarRatingBlock: React.FC<StarRatingBlockProps> = ({ attrs, id }) => {
  return (
    <StarRating
      heading={attrs.data.heading}
      headingClassname="core-block"
      id={id}
      level={attrs.data.level}
      rating={attrs.data.rating}
    />
  );
};
