import React from 'react';
import styled, { StyledComponents } from '@benzinga/themetron';
import { showCornerStone } from '@benzinga/navigation-ui';
import { MoneyTemplateProps } from '../../entities';
import { FloatingPopup } from '../Campaign/FloatingPopup';
import { Popup } from '../Popup';
import { CampaignPopup } from '../Campaign/CampaignPopup';
import { MoneyPostLayout } from '../MoneyPostLayout';
import { MoneyHeader } from '../MoneyHeader';
import classNames from 'classnames';
import { useBenzingaEdge } from '@benzinga/edge';
import { appEnvironment, appName } from '@benzinga/utils';

export const MoneyPostTemplate: React.FC<MoneyTemplateProps> = ({
  layoutAboveArticle,
  layoutBelowArticle,
  layoutHeader,
  layoutMainClassName,
  post,
  showMoneyHeader = true,
  sidebarSettings,
}) => {
  const layout = showCornerStone(post ?? null) ? 'wide' : 'narrow';
  const hasAdLight = useBenzingaEdge().adLightEnabled;
  const isMoneyApp = appEnvironment().isApp(appName.money);

  return (
    <MoneyPostTemplateWrapper className={classNames('font-manrope', { [`${layout}`]: !!layout })}>
      {layoutHeader || (showMoneyHeader && post && <MoneyHeader post={post} />)}
      <MoneyPostLayout
        layoutAboveArticle={layoutAboveArticle}
        layoutBelowArticle={layoutBelowArticle}
        layoutMainClassName={layoutMainClassName}
        post={post ?? null}
        sidebarSettings={sidebarSettings}
      />
      {post?.campaigns && <FloatingPopup targets={post.campaigns} />}
      {post?.campaigns && (isMoneyApp || !hasAdLight) && <CampaignPopup targets={post.campaigns} />}
      {post?.unmonetized_interstitial && <Popup interstitial={true} popup={post.unmonetized_interstitial} />}
      {post?.styles && <GlobalStyles styles={post.styles} />}
    </MoneyPostTemplateWrapper>
  );
};

export default MoneyPostTemplate;

const GlobalStyles = StyledComponents.createGlobalStyle<{ styles?: string }>`
    ${props => props.styles};
`;

const MoneyPostTemplateWrapper = styled.div`
  &.narrow {
    .header-container,
    .layout-container {
      max-width: 1100px;
    }
  }
  .main-content-container {
    padding-left: 0;
    padding-right: 0;
  }
  .layout-footer {
    .layout-container {
      max-width: 1100px;
    }
  }
  .money-page-main {
    /* display: flex; */
    position: relative;
    width: 100%;

    @media (max-width: 1119px) {
      padding: 0 0.75rem;
    }
    .money-post-wrapper {
      width: 100%;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &.section-title {
      margin-top: 2rem;
      margin-bottom: 1rem;
    }
  }

  .cornerstone-navigation {
    display: block;
    width: 100%;
    border: 1px solid #ceddf2;
    border-radius: 4px;
    padding: 0;
  }
`;
