'use client';
import React, { useCallback, useEffect, useState, Suspense } from 'react';
import { Button, ErrorsBox, Icon } from '@benzinga/core-ui';
import { Authentication, AuthenticationManager } from '@benzinga/session';
import { AuthBoxWrapper, AuthInput, AuthPasswordInput } from '../AuthComponents';
import { SessionContext } from '@benzinga/session-context';
import { SafeType } from '@benzinga/safe-await';
import { useTranslation } from 'react-i18next';
import { TrackingManager } from '@benzinga/tracking-manager';
import styled from '@benzinga/themetron';
import i18n from '@benzinga/translate';
import { Trans } from 'react-i18next';
import { faEnvelope } from '@fortawesome/pro-regular-svg-icons';
import { faLock } from '@fortawesome/pro-regular-svg-icons';
import { faArrowRight } from '@fortawesome/pro-light-svg-icons/faArrowRight';
import { GoogleReCaptchaProvider } from 'react-google-recaptcha-v3';
import { GOOGLE_SITE_KEY } from '../../config';
import { ReCaptchaComponent } from '../AuthModal/ReCaptcha';
import SocialButton from '../AuthIterations/SocialButton';
import { AuthMode } from '@benzinga/session';

export interface LoginProps {
  onLogin?: (auth: SafeType<Authentication>, authFor: string) => void;
  onSocialClicked?: (socialAuth: string, forceUrl?: string) => void;
  onError?: (errors: string | string[] | null) => void;
  setAuthMode?: (authMode: AuthMode) => void;
}

export const Login = ({ onError, onLogin, onSocialClicked, setAuthMode }: LoginProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [reCaptchaNonce, setReCaptchaNonce] = useState<number>(0);
  const [error, setError] = useState('');
  const [errors, setErrors] = useState<string[] | null>(null);
  const session = React.useContext(SessionContext);
  const { t } = useTranslation(['auth', 'common'], { i18n });

  const login = useCallback(async () => {
    setError('');
    setErrors(null);
    setIsLoggingIn(true);

    if (email.trim() === '' && password.trim() === '') {
      setErrors([
        t('Inputs.Error.is-required', { field: 'Email', ns: 'common' }),
        t('Inputs.Error.is-required', { field: t('Inputs.password', { ns: 'common' }), ns: 'common' }),
      ]);
      setIsLoggingIn(false);
      return;
    } else if (password.trim() === '') {
      setError(t('Inputs.Error.is-required', { field: t('Inputs.password', { ns: 'common' }) }));
      setIsLoggingIn(false);
      return;
    } else if (email.trim() === '') {
      setIsLoggingIn(false);
      setError(t('Inputs.Error.is-required', { field: 'Email', ns: 'common' }));
      return;
    }

    const auth: SafeType<Authentication> = await session
      .getManager(AuthenticationManager)
      .login(email, password, captchaToken ? captchaToken : undefined);
    if (auth.err) {
      if (auth.err.message) {
        setError(auth.err.message);
      } else if (auth.err.data instanceof Response) {
        const data: Response = auth.err.data;
        const errors = await data.json();
        delete errors.confirm_password;
        setErrors(errors);
      } else {
        setError('Error occured');
      }
      setIsLoggingIn(false);
    } else {
      setIsLoggingIn(false);
      session.getManager(TrackingManager).trackAuthEvent('login', { auth_type: 'email' });
      onLogin && onLogin(auth, 'login');
    }
  }, [captchaToken, email, onLogin, password, session, t]);

  const handleEmailChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEmail(e.target?.value);
    },
    [setEmail],
  );

  const handlePasswordChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setPassword(e.target?.value);
    },
    [setPassword],
  );

  const [passwordHidden, setPasswordHidden] = useState(true);
  const togglePasswordHidden = useCallback(() => {
    setPasswordHidden(!passwordHidden);
  }, [passwordHidden, setPasswordHidden]);

  const handleReCaptchaVerify = useCallback((token: string | null) => {
    if (token) {
      setCaptchaToken(token);
    }
  }, []);

  const updateNonce = useCallback(() => {
    setReCaptchaNonce(r => r + 1);
  }, [setReCaptchaNonce]);

  useEffect(() => {
    updateNonce();
    if (onError && (error || errors)) {
      onError(errors || error);
    }
  }, [error, errors, onError, updateNonce]);

  return (
    <>
      <div className="flex gap-2 mt-8 mb-4">
        <SocialButton onSocialClicked={onSocialClicked} socialType="google" />
        <SocialButton onSocialClicked={onSocialClicked} socialType="apple" />
        <SocialButton onSocialClicked={onSocialClicked} socialType="microsoft" />
      </div>
      <div className="flex-1 text-center mb-8 relative">
        <div className="w-full h-[1px] bg-blue-400 absolute top-1/2 left-0"></div>
        <span className="text-blue-400 relative bg-blue-950 px-4">{t('Auth.AuthModal.continue-row')}</span>
      </div>
      <AuthBoxWrapper className="login-auth-box-wrapper">
        <LoginInputContainer>
          <div className="input-container mb-2">
            <AuthInput
              autoComplete="email"
              id="email"
              label={<Icon className="label-icon" icon={faEnvelope} />}
              name="email"
              onChange={handleEmailChange}
              placeholder="<EMAIL>"
              type="email"
              value={email}
            />
            <AuthPasswordInput
              autoComplete="current-password"
              hidden={passwordHidden}
              id="current-password"
              label={<Icon className="label-icon" icon={faLock} />}
              name="password"
              onChange={handlePasswordChange}
              onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                if (e.key === 'Enter') {
                  login();
                }
              }}
              placeholder="***********"
              toggleHidden={togglePasswordHidden}
              value={password}
            />
          </div>
          <div className="forgot-text">
            <a className="action-text" href="https://www.benzinga.com/user/password" rel="noreferrer" target="_blank">
              <Trans>{t('Auth.AuthModal.login.forgot-text')}</Trans>
            </a>
          </div>
          <div className="action-container">
            <BtnMain isLoading={isLoggingIn} onClick={() => login()}>
              {t('Auth.Login.button')}
            </BtnMain>
          </div>
        </LoginInputContainer>
        {((errors && errors.length > 0) || error) && <ErrorsBox error={error} errors={errors} includeKeys={true} />}

        <div className="mt-4 text-center flex items-center justify-between p-4 font-lg border border-blue-100">
          <div>{t('Auth.AuthModal.login.toggle-box.text1')} </div>
          <div
            className="action-text select-none flex gap-2 items-center cursor-pointer"
            onClick={() => {
              setAuthMode && setAuthMode('register');
            }}
          >
            {t('Auth.AuthModal.login.toggle-box.text2')}
            <Icon icon={faArrowRight} />
          </div>
        </div>

        <div className="h-0 invisible test">
          <Suspense fallback={<div />}>
            <GoogleReCaptchaProvider
              container={{ element: 'google-recaptcha-reg', parameters: { badge: 'inline', theme: 'light' } }}
              reCaptchaKey={GOOGLE_SITE_KEY}
            >
              <ReCaptchaComponent nonce={reCaptchaNonce} onToken={handleReCaptchaVerify} />
            </GoogleReCaptchaProvider>
          </Suspense>
        </div>
        <div className="flex justify-center w-full" id="google-recaptcha-reg"></div>
      </AuthBoxWrapper>
    </>
  );
};

const LoginInputContainer = styled.form`
  .action-text {
    text-transform: none;
    color: #3f83f8 !important;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
  }

  .label-icon {
    font-size: 24px;
    color: #99aecc;
    margin-bottom: 8px;
    svg {
      height: 24px;
      width: 24px;
    }
  }

  .input-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
`;

const BtnMain = styled(Button)`
  color: white;
  background-color: #3f83f8;
  padding: 16px 32px;

  &:hover {
    background-color: #3268c4;
    color: white;
  }
`;
