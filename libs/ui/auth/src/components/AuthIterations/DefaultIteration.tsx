import { FC, useContext, useEffect, useState } from 'react';
import { MainLogo } from '@benzinga/logos-ui';
import Link from 'next/link';
import { Login } from '../Login';
import { Register } from '../Register';
import { SMSVerify } from '../SMSVerify';
import { Complete } from '../Complete';
import { Upsell } from '../Upsell';
import { ReportSignUp } from '../Upsell/ReportSignUp';
import { useTranslation } from 'react-i18next';
import { AuthIterationType } from './types';
import { Authentication } from '@benzinga/session';
import { SafeType } from '@benzinga/safe-await';
import { TrackingManager } from '@benzinga/tracking-manager';
import { IdentityContext } from '@benzinga/identity';
import i18n, { LocaleType } from '@benzinga/translate';
import { SessionContext } from '@benzinga/session-context';
import { edgePaywallCopy } from './utils';

const utmSource = 'default';

export const DefaultIteration: FC<AuthIterationType> = ({
  authMode,
  contentType,
  email,
  onLogin,
  onRegister,
  onSocialClicked,
  phoneNumber: phone,
  placement,
  setAuthMode,
}) => {
  const { config: identityConfig } = useContext(IdentityContext);
  const session = useContext(SessionContext);
  const { t } = useTranslation(['auth', 'common'], { i18n });
  const [phoneNumber, setPhoneNumber] = useState(phone);
  const [authentication, setAuthentication] = useState<SafeType<Authentication> | null>(null);
  const [socialType, setSocialType] = useState<string>('');
  const [offerEmail, setOfferEmail] = useState(email);
  const showUpsell = true;
  const paywallCopy = contentType ? edgePaywallCopy[contentType] : null;

  useEffect(() => {
    session.getManager(TrackingManager).trackPaywallEvent('view', {
      paywall_id: paywallCopy ? `${utmSource}+${paywallCopy}-copy` : utmSource,
      paywall_type: 'default',
      placement: placement,
    });
  }, [paywallCopy, placement, session]);

  const onUpsellSocialClick = (socialType: string) => {
    setSocialType(socialType);
    setAuthMode && setAuthMode('upsell');
  };

  const handleRegisterPostProcess = () => {
    if (authentication) onRegister && onRegister(authentication, 'register');
  };

  const handleSocialPostProcess = () => {
    onSocialClicked && onSocialClicked(socialType);
  };

  const handleReportExit = () => {
    setAuthMode && setAuthMode('upsell');
  };

  const handleUpsellExit = () => {
    if (socialType) handleSocialPostProcess();
    else {
      setAuthMode && setAuthMode('complete');
      handleRegisterPostProcess();
    }
  };

  return (
    <div
      className={`flex flex-col max-w-[520px] size-full md:size-auto border border-black no-scrollbar overflow-scroll flex flex-col md:flex-row md:gap-12 fixed z-[1000003] bg-blue-950 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 shadow-xl ${authMode === 'report-offer' ? 'border-bzblue-600' : 'p-6 md:p-8 md:pb-8'}`}
    >
      <div className="text-white">
        {authMode !== 'upsell' && authMode !== 'report-offer' && (
          <Link href="/">
            <MainLogo logoVariant={identityConfig.logoVariant} variant="light" />
          </Link>
        )}
        {authMode === 'login' || authMode === 'register' ? (
          <>
            {paywallCopy ? (
              <>
                <h3 className="text-3xl text-white mb-4">{paywallCopy.headline}</h3>
                <p className="text-white max-w-[400px]">{paywallCopy.subhead}</p>
              </>
            ) : (
              <>
                <h3 className="text-4xl text-white mb-4">{t('Auth.AuthModal.login.get-unlimited-access')}</h3>
                <p className="text-white">
                  <span dangerouslySetInnerHTML={{ __html: t('Auth.AuthModal.login.come-see', { ns: 'auth' }) }} />{' '}
                </p>
                <p className="max-w-[400px]">
                  <Link className="underline" href="/report/TSLA" rel="noopener noreferrer" target="_blank">
                    {t('Auth.AuthModal.Benefits.in-depth-reports', { ns: 'auth' })}
                  </Link>
                  ,{' '}
                  <Link className="underline" href="/analyst-stock-ratings" rel="noopener noreferrer" target="_blank">
                    {t('Auth.AuthModal.Benefits.analyst-ratings', { ns: 'auth' })}
                  </Link>
                  ,{' '}
                  <Link className="underline" href="/earnings" rel="noopener noreferrer" target="_blank">
                    {t('Auth.AuthModal.Benefits.earnings', { ns: 'auth' })}
                  </Link>
                  , {t('Auth.AuthModal.Benefits.trade-ideas-more')}{' '}
                  {t('Auth.AuthModal.Benefits.or-return-to', { ns: 'auth' })}{' '}
                  <Link className="underline" href="/" rel="noopener noreferrer" target="_blank">
                    Benzinga.com
                  </Link>
                </p>
              </>
            )}
            <div className="max-w-[500px]">
              {authMode === 'login' && (
                <div className="select-none">
                  <Login onLogin={onLogin} onSocialClicked={onSocialClicked} setAuthMode={setAuthMode} />
                </div>
              )}
              {authMode === 'register' && (
                <div className="select-none">
                  <Register
                    initialEmail={email}
                    onRegister={onRegister}
                    onSocialClicked={showUpsell ? onUpsellSocialClick : onSocialClicked}
                    setAuthentication={setAuthentication}
                    setAuthMode={setAuthMode}
                    setOfferEmail={setOfferEmail}
                    setPhoneNumber={setPhoneNumber}
                    showUpsell={showUpsell}
                  />
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="max-w-[500px]">
            {authMode === 'sms-verify' && (
              <SMSVerify
                authentication={authentication}
                onVerified={onRegister}
                phoneNumber={phoneNumber}
                setAuthMode={setAuthMode}
              />
            )}
            {authMode === 'complete' && <Complete />}
            {authMode === 'report-offer' && <ReportSignUp authEmail={offerEmail} onClose={handleReportExit} />}
            {authMode === 'upsell' && <Upsell onClose={handleUpsellExit} />}
          </div>
        )}
      </div>
    </div>
  );
};
