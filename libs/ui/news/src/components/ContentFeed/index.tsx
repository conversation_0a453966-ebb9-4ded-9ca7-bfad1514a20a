'use client';
import React, { ReactElement, useCallback, useEffect, useState, startTransition, useRef } from 'react';
import styled from '@benzinga/themetron';
import classNames from 'classnames';
import { SectionTitle, Button, Icon } from '@benzinga/core-ui';
import { isDesktop, isMobile } from '@benzinga/device-utils';
import { SafePromise } from '@benzinga/safe-await';
import DefaultNewsElement from '../DefaultNewsElement';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';
import { filterDuplicateArticles, StoryObject } from '@benzinga/advanced-news-manager';
import { SimpleNewsQueryAndOptions } from '@benzinga/internal-news-manager';
import { BasicNewsManager, News, formatToSimpleNewsQueryParams } from '@benzinga/basic-news-manager';
import { faCode } from '@fortawesome/pro-solid-svg-icons/faCode';
import { LoadMoreElement } from './LoadMoreElement';
import { LiveBarIndicator } from './LiveBarIndicator';
import { PostCardProps } from '../PostCard';
import VizSensor from 'react-visibility-sensor';
import {
  isContentHasReachImpressionLimit,
  ContentManager,
  storeGlobalImpression,
  isGlobalImpressionStored,
} from '@benzinga/content-manager';
import { NewsListItemElementSkeleton } from '../NewsListItemElementSkeleton';
import { useImpressionTracker } from '@benzinga/analytics';

export interface Item extends StoryObject {
  isHighlighted?: boolean;
  sponsored?: boolean;
}

export type LoadMoreDevice = 'all' | 'desktop' | 'mobile';

const isDeviceFitToUseLoadMore = (loadMoreDevice: string) => {
  if (loadMoreDevice === 'mobile') {
    return isMobile();
  } else if (loadMoreDevice === 'desktop') {
    return isDesktop();
  }
  return true;
};

export interface ReachContentParams {
  placementIndex: number;
  repeatIndex: number;
  preload: boolean;
}

export interface AdPlacement {
  index: number;
  placement_id: string;
}

export interface ContentFeedProps {
  adUnit?: React.ReactNode;
  className?: string;
  contentId?: string;
  feedHeaderElement?: (title: string) => ReactElement;
  excludeIds?: number[];
  isInfinite?: boolean;
  isLoadingMore?: boolean;
  limit?: number;
  link?: string;
  loadMore?: boolean;
  loadMoreStyle?: 'text' | 'button';
  loadMoreDevice?: LoadMoreDevice;
  loadMoreElement?: (loading: boolean, loadMoreItems: () => void) => ReactElement;
  loadMoreInterface?: boolean;
  loadMoreRequest?: (queryParams) => SafePromise<StoryObject[]>;
  onMoreLoaded?: (page: number, results: StoryObject[] | News[]) => News[];
  newsItemElement?: (node: Item, index: number) => ReactElement | null;
  newItemsText?: string;
  nodes?: StoryObject[] | News[];
  noResultsElement?: React.ReactNode;
  onLoadMore?: () => void;
  poolLatest?: boolean;
  poolLatestInterval?: number;
  postCardProps?: PostCardProps;
  query?: SimpleNewsQueryAndOptions;
  showSponsoredContent?: boolean;
  sponsoredNodeInitialPlacement?: number;
  sponsoredNodeNthPlacement?: number;
  sponsoredNodes?: StoryObject[];
  title?: string;
  getWidget?: boolean;
  realtime?: boolean;
  maxTimesLoaded?: number;
  reachContent?: ReachContentParams;
  adPlacements?: AdPlacement[];
}

export const ContentFeed: React.FC<ContentFeedProps> = ({
  adPlacements = [],
  adUnit,
  className,
  contentId,
  excludeIds,
  feedHeaderElement,
  getWidget = false,
  isInfinite = false,
  limit = 4,
  loadMore = false,
  loadMoreDevice = 'all',
  loadMoreElement,
  loadMoreInterface = false,
  loadMoreRequest,
  loadMoreStyle = 'text',
  maxTimesLoaded = 50,
  newItemsText,
  newsItemElement,
  nodes = [],
  noResultsElement,
  onLoadMore,
  onMoreLoaded,
  poolLatest = false,
  poolLatestInterval = 60000,
  postCardProps,
  query,
  reachContent,
  realtime = false,
  showSponsoredContent,
  sponsoredNodeInitialPlacement,
  sponsoredNodeNthPlacement,
  sponsoredNodes: initialSponsoredNodes,
  title,
}) => {
  const session = React.useContext(SessionContext);
  const [didLoad, setDidLoad] = useState<boolean>(!!nodes?.length);
  const [currentPage, setCurrentPage] = useState<number>(-1);
  const [items, setItems] = useState<Item[]>(nodes as Item[]);
  const [newItems, setNewItems] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [timesLoaded, setTimesLoaded] = useState<number>(0);
  const [deadEndOfLoadMore, setDeadEndOfLoadMore] = useState<boolean>(false);
  const [isLoadMoreAvailable, setIsLoadMoreAvailable] = useState<boolean>(false);
  const [didFail, setDidFail] = useState<boolean>(false);
  const [requestMethodCalledHistory, setRequestMethodCalledHistory] = useState<number[]>([]);
  const [sponsoredNodes, setSponsoredNodes] = useState<StoryObject[]>(initialSponsoredNodes || []);
  const basicNewsManager = session.getManager(BasicNewsManager);
  const contentManager = session.getManager(ContentManager);
  const containerRef = useRef<HTMLDivElement | null>(null);
  useImpressionTracker({ containerRef: containerRef });

  const initialItemsCount = nodes.length ?? 0;

  // useEffect(() => {
  //   setIsLoading(isLoadingMore);
  // }, [isLoadingMore]);

  // useEffect(() => {
  //   setInitialItemsCount(Array.isArray(nodes) ? nodes.length : 0);
  // }, [nodes]);

  useEffect(() => {
    if (!showSponsoredContent) return;
    if (Array.isArray(initialSponsoredNodes)) {
      setSponsoredNodes(initialSponsoredNodes);
    }
  }, [initialSponsoredNodes, showSponsoredContent]);

  useEffect(() => {
    if (!showSponsoredContent) return;
    setTimeout(() => {
      if (!initialSponsoredNodes && reachContent) {
        contentManager
          .getDebouncedSponsoredContentArticles()
          .then(response => {
            if (response?.ok) {
              setSponsoredNodes(response?.ok as Item[]);
            }
          })
          .catch(e => {
            console.error('Sponsored Article Error:', e);
          });
      }
    }, 500);
  }, [initialSponsoredNodes, reachContent, contentManager, showSponsoredContent]);

  useEffect(() => {
    // there is a bug here if set when nodes have no length
    if (nodes?.length && nodes?.length > 0) {
      setItems(nodes as Item[]);
    }
  }, [nodes]);

  const handleCheckWhenRequestMethodWasLastCalled = React.useCallback(() => {
    const twoSecondsAgo = new Date().getTime() - 2000;
    const numberOfRequestMadeInLastTwoSeconds = requestMethodCalledHistory.filter(
      timestamp => timestamp > twoSecondsAgo,
    ).length;
    if (numberOfRequestMadeInLastTwoSeconds >= 5) {
      startTransition(() => {
        setDidFail(true);
        setIsLoadMoreAvailable(true);
        setRequestMethodCalledHistory([]);
      });
    }
  }, [requestMethodCalledHistory]);

  const handleTrackRequestMethodCalled = useCallback(() => {
    const now = new Date().getTime();
    setRequestMethodCalledHistory([...requestMethodCalledHistory, now]);
  }, [requestMethodCalledHistory]);

  const loadMoreItems = useCallback(
    async (analyticsEvents = true) => {
      if (!isLoadMoreAvailable) return;
      try {
        if (isLoading === true) return;

        // If we want to use Load More only as UI interface set loadMoreInterface = true and onLoadMore event;
        if (loadMoreInterface) {
          if (onLoadMore) {
            onLoadMore();
          }
          return;
        }

        if (!query) return;

        setIsLoading(true);

        const getLastNodeId = (items: Item[]) => {
          if (Array.isArray(items) && items.length) {
            const targetNode = [...items].reverse().find(item => !item?.sponsored);

            if (targetNode) {
              return targetNode.id;
            }
          }

          return undefined;
        };

        const sponsoredPosts = items.filter(item => item?.sponsored);

        const queryParams = {
          ...query,
          last: getLastNodeId(items),
          limit: limit,
          offset: (currentPage + 1) * limit + (initialItemsCount - sponsoredPosts.length),
        };

        const defaultNewsRequest = async (params = {}): SafePromise<StoryObject[]> => {
          const queryParams = formatToSimpleNewsQueryParams(params);
          const response = await basicNewsManager.simplyQueryNews(queryParams.query, queryParams.options);
          return response;
        };

        const requestMethod = loadMoreRequest || defaultNewsRequest;

        if (requestMethod) {
          const response = await requestMethod(queryParams);
          handleTrackRequestMethodCalled();
          handleCheckWhenRequestMethodWasLastCalled();

          if (response.err) {
            setDidFail(true);
          } else if (Array.isArray(response.ok)) {
            const results = response.ok as Item[];
            const _currentPage = currentPage + 1;
            setCurrentPage(_currentPage);
            const loadMoreResults = onMoreLoaded ? onMoreLoaded(_currentPage, results) : results;
            setItems([...items, ...loadMoreResults] as Item[]);

            if (!results.length) {
              setDeadEndOfLoadMore(true);
            }
          } else if (!response.ok) {
            setDidFail(true);
          }
        }

        setTimesLoaded(previousCount => previousCount + 1);
        setDidLoad(true);
        setIsLoading(false);

        if (analyticsEvents) {
          session.getManager(TrackingManager).trackTableEvent('load_more', 'content_feed', 'news', {});
        }
      } catch (e) {
        setDidFail(true);
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      isLoading,
      loadMoreInterface,
      query,
      items,
      limit,
      currentPage,
      initialItemsCount,
      loadMoreRequest,
      onLoadMore,
      handleTrackRequestMethodCalled,
      handleCheckWhenRequestMethodWasLastCalled,
      onMoreLoaded,
      title,
      contentId,
      isLoadMoreAvailable,
    ],
  );

  useEffect(() => {
    if (!didLoad) loadMoreItems(false);
  }, [didLoad, loadMoreItems]);

  const showNewItems = useCallback(
    async (incomingNewItems = newItems) => {
      incomingNewItems.forEach(item => {
        item.isHighlighted = true;
      });
      if (newItems.length >= 5) {
        setItems(incomingNewItems);
      } else {
        // setItems(current => {
        //   const currentItems = current.map(item => {
        //     if (item.isHighlighted)
        //       return {
        //         ...item,
        //         isHighlighted: false,
        //       };
        //     return { ...item };
        //   });
        //   const result = [...incomingNewItems, ...currentItems];
        //   return result;
        setItems(current => [...incomingNewItems, ...current]);
      }
      setNewItems([]);
    },
    [newItems],
  );

  const loadLatestItems = useCallback(async () => {
    if (!isLoadMoreAvailable) return;
    if (items.length) {
      const queryParams = formatToSimpleNewsQueryParams({
        ...query,
        last: items?.[0]?.nodeId ?? undefined,
        limit: limit,
      });

      const response = await basicNewsManager.simplyQueryNews(queryParams.query, queryParams.options);
      // Map on existing items

      const newItemsResult = response?.ok as Item[];
      const firstResultItem = newItemsResult?.[0];
      if (newItemsResult && firstResultItem) {
        if (
          firstResultItem.id !== items[0].id &&
          firstResultItem.id !== newItems[0]?.id &&
          !excludeIds?.includes(firstResultItem.id)
        ) {
          const itemsIds = items.map(item => item.nodeId);
          const idsToExclude = excludeIds?.length ? itemsIds.concat(excludeIds) : itemsIds;

          const filteredItems = filterDuplicateArticles(newItemsResult as Item[], idsToExclude);

          if (realtime) {
            showNewItems(filteredItems);
          } else {
            setNewItems(filteredItems);
          }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [excludeIds, items, limit, newItems, query, realtime, showNewItems, isLoadMoreAvailable]);

  useEffect(() => {
    let interval: NodeJS.Timer | null = null;
    if (poolLatest && didLoad) {
      interval = setInterval(() => loadLatestItems(), poolLatestInterval);
    }
    return () => {
      interval && clearInterval(interval);
    };
  }, [didLoad, loadLatestItems, newItems, poolLatest, poolLatestInterval]);

  useEffect(() => {
    startTransition(() => {
      if (timesLoaded >= maxTimesLoaded) {
        setIsLoadMoreAvailable(false);
      } else {
        setIsLoadMoreAvailable(loadMore && !deadEndOfLoadMore && isDeviceFitToUseLoadMore(loadMoreDevice));
      }
    });
  }, [loadMore, deadEndOfLoadMore, loadMoreDevice, maxTimesLoaded, timesLoaded]);

  const renderNewsElement = (node: Item, index: number) => {
    if (!node) return null;
    if (newsItemElement) return newsItemElement(node, index);
    return <DefaultNewsElement node={node as StoryObject} postCardProps={postCardProps} />;
  };

  const renderFeedHeader = () => {
    if (feedHeaderElement && title) {
      return feedHeaderElement(title);
    } else if (title) {
      return (
        <NewsFeedHeader className={classNames('mb-4', { realtime })}>
          {realtime && <LiveBarIndicator />}
          <SectionTitle bordered level={2}>
            {title}
          </SectionTitle>
          <div className="my-4" />
        </NewsFeedHeader>
      );
    } else {
      return null;
    }
  };

  const filteredItems = filterDuplicateArticles(items as StoryObject[], excludeIds);
  const filteredItemsIds = filteredItems.map(node => node.id);

  const renderSponsoredItem = (index: number) => {
    if (!Array.isArray(sponsoredNodes) || sponsoredNodes?.length < 1) {
      return reachContent?.preload ? <NewsListItemElementSkeleton isSponsored={true} showTicker={false} /> : null;
    }

    const sponsoredNode = (sponsoredNodes || [])[index % (sponsoredNodes || []).length];
    const { id: sponsoredNodeId, meta } = sponsoredNode;

    if (filteredItemsIds.includes(sponsoredNodeId)) {
      return null;
    }

    const registerImpression = isVisible => {
      const impressionSlug = `${sponsoredNodeId}-${index}`;
      if (isVisible && !isGlobalImpressionStored(impressionSlug)) {
        storeGlobalImpression(impressionSlug);
        session.getManager(TrackingManager).trackCampaignEvent('view', {
          partner_id: (meta?.Reach?.Disclosure?.tid || 0).toString(),
          unit_type: `sponsored-item-${sponsoredNodeId}`,
        });
      }
    };

    if (isContentHasReachImpressionLimit(meta)) {
      return (
        <VizSensor offset={{ top: 100 }} onChange={isVisible => registerImpression(isVisible)}>
          {renderNewsElement(sponsoredNode, index)}
        </VizSensor>
      );
    }

    return renderNewsElement(sponsoredNode, index);
  };

  return (
    <NewsFeedWrapper className={`content-feed ${className ?? ''}`}>
      {renderFeedHeader()}
      {getWidget && <GetWidgetButton />}

      {!!newItems.length && (
        <NewItemsWrapper className="new-items-button">
          <Button onClick={() => showNewItems()}>
            {newItems?.length}
            {newItems?.length >= limit ? '+' : ''} New {newItemsText || newItems?.length > 1 ? 'Stories' : 'Story'}
          </Button>
        </NewItemsWrapper>
      )}
      <div className="content-feed-container">
        <div className="content-feed-list" ref={containerRef}>
          {filteredItems.map((node, index) => {
            const initialPlacement = reachContent?.placementIndex ?? sponsoredNodeInitialPlacement ?? 3;
            const NTH_PLACEMENT = reachContent?.repeatIndex ?? sponsoredNodeNthPlacement ?? 4;

            const shouldShowSponsored =
              showSponsoredContent &&
              (Array.isArray(sponsoredNodes) || reachContent?.preload) &&
              (index === initialPlacement ||
                (index > initialPlacement && (index - initialPlacement) % NTH_PLACEMENT === 0));

            if (shouldShowSponsored) {
              return (
                <React.Fragment key={`content-feed-list-item-${node.id}`}>
                  {renderSponsoredItem(Math.floor((index - initialPlacement) / NTH_PLACEMENT))}
                  <div data-impression={node.id}>{renderNewsElement(node as Item, index)}</div>
                </React.Fragment>
              );
            }

            const adPlacement = (adPlacements || []).find(placement => placement.index === index);
            const placementId = adPlacement?.placement_id;
            if (placementId) {
              return (
                <React.Fragment key={`content-feed-list-item-${node.id}`}>
                  {/* <RaptiveAdPlaceholder minimumHeightEnabled={false} noWrapper={true} type="content-small" /> */}
                  <div
                    className={`nativo-ad-placement ${placementId}`}
                    data-impression={node.id}
                    id={placementId}
                    key={`quote-news-item-nativo-${placementId}`}
                  />
                  {renderNewsElement(node as Item, index)}
                </React.Fragment>
              );
            }

            return (
              <React.Fragment key={`content-feed-list-item-${node.id}`}>
                {renderNewsElement(node as Item, index)}
              </React.Fragment>
            );
          })}
          {noResultsElement && Number(filteredItems.length) === 0 && (
            <div className="content-feed-no-results">{noResultsElement}</div>
          )}
        </div>
        {isLoadMoreAvailable && (
          <LoadMoreElement
            didFail={didFail}
            didLoad={didLoad}
            isInfinite={timesLoaded >= 2 ? false : isInfinite}
            isLoading={isLoading}
            loadMoreElement={loadMoreElement}
            loadMoreStyle={loadMoreStyle}
            onLoadMore={loadMoreItems}
          />
        )}
        {adUnit && <div className="mt-8">{adUnit}</div>}
      </div>
    </NewsFeedWrapper>
  );
};

export default ContentFeed;

const GetWidgetButton = () => {
  return (
    <CopyLinkButton href="/widgets/newsfeed" target="_blank">
      <div className="get-widget-button">
        <div className="button-section">
          <div className="button-text">Get Widget</div>
          <Icon className="code-icon" icon={faCode} />
        </div>
      </div>
    </CopyLinkButton>
  );
};

const CopyLinkButton = styled.a`
  float: right;
  height: 30px;
  position: absolute;
  top: 0px;
  width: 100%;

  .get-widget-button {
    height: 30px;
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
  }

  .code-icon {
    color: #ffffff;
  }

  .button-section {
    display: flex;
    align-items: center;
    gap: 5px;
    border-radius: 5px;
    background-color: #7f7f7f;
    padding: 2px 4px;
  }

  .button-text {
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
  }
`;

const NewItemsWrapper = styled.div`
  text-align: center;
  margin-bottom: 0.5rem;
`;

const NewsFeedHeader = styled.div``;

const NewsFeedWrapper = styled.div`
  width: 100%;
  position: relative;
  .content-feed-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  .newsfeed-card {
    color: ${({ theme }) => theme.colorPalette.black};
    .post-card {
      //margin-bottom: 12px;
    }

    &.highlight {
      &.fade {
        .post-card-wrapper {
          animation: fade 5s forwards;
        }

        @keyframes fade {
          0% {
            background-color: #fdf8db;
          }
          100% {
            background-color: transparent;
          }
        }
      }
    }

    .post-teaser {
      color: #7c7c7c;
      font-weight: 400;
      font-size: 12px;
    }
  }

  .news-block {
    border-bottom: solid 1px ${({ theme }) => theme.colorPalette.neutral300};
    line-height: ${({ theme }) => theme.lineHeight.normal};
    padding: 12px 0;
    .news-block-headline {
      color: ${({ theme }) => theme.colorPalette.black};
    }
    .article-title {
      font-size: ${({ theme }) => theme.fontSize.lg};
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
    }
  }
`;
