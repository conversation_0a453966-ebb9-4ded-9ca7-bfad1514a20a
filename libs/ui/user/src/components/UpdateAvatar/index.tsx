'use client';
import React, { Fragment, useRef, useState } from 'react';
import { FileDrop } from 'react-file-drop';
import { ChatIdentity } from '@benzinga/chat-manager';
import { UserManager } from '@benzinga/user-manager';
import styled, { useTheme } from '@benzinga/themetron';
import { message, Avatar } from 'antd';
import { Spinner } from '@benzinga/core-ui';

import { SessionContext } from '@benzinga/session-context';
import { CheckCircleTwoTone } from '@ant-design/icons';

export interface UpdateAvatarProps {
  identity: ChatIdentity | undefined;
  setAvatar?: (avatar: string) => void;
  username?: string | undefined;
}

interface State {
  loading: boolean;
  avatar: string | undefined;
  fileUrl: string | undefined;
}

export const UpdateAvatar: React.FC<UpdateAvatarProps> = props => {
  const session = React.useContext(SessionContext);
  const { colors } = useTheme();

  const [state, setState] = useState<State>({
    avatar: undefined,
    fileUrl: undefined,
    loading: false,
  });

  const { identity, username } = props;
  const inputBtn = useRef<HTMLInputElement>(null);

  if (!identity) return null;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const validate = (file: any) => {
    switch (file?.type) {
      case 'image/jpg':
      case 'image/jpeg':
      case 'image/gif':
      case 'image/png':
        break;
      default:
        message.error(`File type ${file?.type} is not supported!`, 3);
        return false;
    }

    if (file?.size >= 1024000) {
      message.error('File size limit exceed 1MB', 3);
      return false;
    }

    return true;
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onFileChange = (event: any) => {
    if (validate(event.target.files[0])) {
      const avatarUrl = URL.createObjectURL(event.target.files[0]);
      setState(preState => ({ ...preState, fileUrl: avatarUrl, loading: true }));
      onFileUpload(event.target.files[0]);
      props.setAvatar && props.setAvatar(avatarUrl);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onFileDrop = (files: any) => {
    if (validate(files)) {
      const avatarUrl = URL.createObjectURL(files);
      setState(preState => ({ ...preState, fileUrl: avatarUrl, loading: true }));
      onFileUpload(files);
      props.setAvatar && props.setAvatar(avatarUrl);
    }
  };

  const onFileUpload = async (avatar: File) => {
    if (avatar && props.identity && props.identity.identityUuid) {
      const response = await session
        .getManager(UserManager)
        .updateAvatar({ file: avatar, uuid: props.identity?.identityUuid });
      if (response.err) {
        setState(preState => ({ ...preState, loading: false }));
      } else {
        setState(preState => ({ ...preState, avatar: response.ok?.avatar, loading: false }));
        message.success({
          content: 'Avatar uploaded Successfully',
          duration: 3,
          icon: <CheckCircleTwoTone />,
          style: {
            color: colors.foreground,
          },
        });
      }
    }
  };

  return (
    <Fragment>
      <FileDrop onDrop={(files, _event) => files && onFileDrop(files[0])}>
        <AvatarFlexDiv onClick={() => inputBtn?.current?.click()}>
          <AvatarDiv>
            <Avatar
              shape="circle"
              src={
                state.loading ? (
                  <Spinner />
                ) : (
                  state.fileUrl ||
                  identity.avatar ||
                  (username
                    ? `https://ui-avatars.com/api/?name=${username}&background=0075cd&color=fff&size=128`
                    : null)
                )
              }
            >
              {identity?.nickname?.slice(0, 1)}
            </Avatar>
          </AvatarDiv>
          <DropSpan>
            <span>
              Click or drag and drop image to change your avatar. Accepted file formats:{' '}
              <BlueFormat>JPG, PNG, GIF</BlueFormat>
            </span>
            <ErrorContainer>Maximum file size should be 1MB </ErrorContainer>
          </DropSpan>
        </AvatarFlexDiv>
      </FileDrop>
      <HideDiv>
        <input onChange={onFileChange} ref={inputBtn} type="file" />
      </HideDiv>
    </Fragment>
  );
};

const HideDiv = styled.div`
  display: none;
`;

const DropSpan = styled.span`
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-evenly;
  margin-left: 10px;
  cursor: pointer;
  user-select: none;
`;

const AvatarDiv = styled.span`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  object-fit: fill;
  padding: 0;
  cursor: pointer;
  height: 80px;
  width: 80px;
  border: 1px solid transparent;
  text-align: center;
  transition: all 0.1s ease;
  overflow: hidden;
  border-radius: 9999px;

  &:hover {
    border: 1px solid ${props => props.theme.colorPalette.blue500};
  }
`;

const AvatarFlexDiv = styled.div`
  display: flex;
  height: auto;
  width: auto;
  align-items: center;
`;

const ErrorContainer = styled.span({
  color: '#E5594E',
  marginBottom: '5px',
});

const BlueFormat = styled.span`
  color: ${props => props.theme.colorPalette.blue500};
`;

export default UpdateAvatar;
