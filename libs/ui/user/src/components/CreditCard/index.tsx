'use client';
import React from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import { message, Popconfirm } from 'antd';
import { transparentize } from 'polished';
import styled from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { type Card, ShopManager } from '@benzinga/shop-manager';
import { CreditCardIcon } from './CreditCardIcon';
import { capitalize } from '@benzinga/utils';

// We need to load in the .less file for antd to work properly

export interface UserCreditCardProps {
  card: Card;
  addSelectedCard?: (card: Card | null) => void;
  selectedCard?: Card | null;
  debugMode?: boolean;
  reloadSession?: () => void;
  onSuccessfulDelete?: (args: boolean) => void;
  buttonMode?: 'buttons' | 'popconfirm';
  locked?: boolean;
}

export const CreditCard: React.FC<UserCreditCardProps> = props => {
  const { addSelectedCard, buttonMode = 'popconfirm', card, debugMode = false, locked, selectedCard } = props;

  const [loadingMsg, setLoadingMsg] = React.useState<(() => void) | null>(null);

  const session = React.useContext(SessionContext);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleDeleteCardClick = async (e: any) => {
    e?.stopPropagation();
    const {
      addSelectedCard,
      card: { uuid },
      reloadSession,
      selectedCard,
    } = props;
    if (addSelectedCard !== undefined && selectedCard?.uuid === uuid) {
      addSelectedCard(null);
    }
    setLoadingMsg(message.loading('Deleting card...', 10));
    const response = await session.getManager(ShopManager).deleteCreditCard(uuid);
    loadingMsg && loadingMsg();
    if (response.err) {
      message.error(response.err.message);
    } else {
      message.success('Successfully deleted card!');
      reloadSession ? reloadSession() : window.location.reload();
      if (props?.onSuccessfulDelete) {
        props.onSuccessfulDelete(true);
      }
    }
  };

  const hideMessage = React.useCallback(() => {
    if (loadingMsg) {
      loadingMsg();
      setLoadingMsg(null);
    }
  }, [loadingMsg]);

  React.useEffect(() => {
    return () => {
      hideMessage();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const defaultCardTag = card && card.isDefault ? <DefaultCard>default</DefaultCard> : null;

  return (
    <CreditCardWrapper onClick={() => addSelectedCard !== undefined && addSelectedCard(card)}>
      <CreditCardWrapperInner isCardSelected={selectedCard?.uuid === card.uuid}>
        <CreditCardIconWrapper>
          <CreditCardIcon type={card.brand} />
        </CreditCardIconWrapper>

        <Column style={{ flexGrow: 1 }}>
          <Title>
            <CreditCardName>{capitalize(card.name)}</CreditCardName>

            {defaultCardTag}

            <Row style={{ flexGrow: 1 }} />

            <CreditCardActions>
              <CreditCardAction>
                {buttonMode === 'popconfirm' && !locked ? (
                  <Popconfirm
                    cancelText="No"
                    okText="Yes"
                    onCancel={e => {
                      e?.stopPropagation();
                    }}
                    onConfirm={handleDeleteCardClick}
                    title="Are you sure you want to delete this card?"
                  >
                    <DeleteOutlined onClick={e => e.stopPropagation()} />
                  </Popconfirm>
                ) : !locked ? (
                  <DeleteOutlined onClick={handleDeleteCardClick} />
                ) : null}
              </CreditCardAction>
            </CreditCardActions>
          </Title>

          <Row>
            <CreditCardInfo>
              <InfoLabel>Expires: </InfoLabel>
              {card && card.expMonth}/{card && card.expYear}
            </CreditCardInfo>
          </Row>

          {debugMode && (
            <Row>
              <CreditCardInfo>
                <InfoLabel>uuid</InfoLabel>
                {card && card.uuid}
              </CreditCardInfo>
            </Row>
          )}
        </Column>
      </CreditCardWrapperInner>
    </CreditCardWrapper>
  );
};

export default CreditCard;

const Row = styled('div')`
  display: flex;
  flex-direction: row;
  min-width: 0%;
`;

const Column = styled('div')`
  display: flex;
  flex-direction: column;
`;

const CreditCardWrapper = styled(Row)`
  display: flex;
  width: 100%;
`;

const CreditCardWrapperInner = styled(Row)<{ isCardSelected: boolean }>`
  &:hover {
    background-color: ${({ theme }) => transparentize(1 / 3, theme.colors.border)};
    box-shadow:
      0 3px 6px rgba(0, 0, 0, 0.16),
      0 3px 6px rgba(0, 0, 0, 0.23);
  }

  background-color: ${({ theme }) => transparentize(2 / 3, theme.colors.border)};
  border: 1px solid ${({ isCardSelected, theme }) => (isCardSelected ? '#008fd9' : theme.colors.border)};
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
  flex-grow: 1;
  flex-shrink: 0;
  margin-bottom: 1em;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
`;

const CreditCardName = styled(Row)`
  flex-shrink: 0;
  font-size: 1.25em;
  line-height: 2em;
`;

const CreditCardActions = styled(Row)`
  padding: 0.5em;
`;

const CreditCardAction = styled(Row)`
  cursor: pointer;
  margin: 0.25em 0.5em;
`;

const CreditCardInfo = styled(Row)`
  flex-shrink: 0;
  font-size: 0.875em;
  margin-bottom: 0.5em;
  margin-right: 2em;
`;

const InfoLabel = styled(Row)`
  color: ${({ theme }) => theme.colors.foregroundInactive};
  margin-right: 5px;
  text-transform: 'uppercase';
`;

const CreditCardIconWrapper = styled(Row)`
  &:hover {
    color: ${({ theme }) => theme.colors.foregroundActive};
  }
  align-items: center;
  justify-content: center;
  padding: 0 1em;
`;

const DefaultCard = styled(Row)`
  align-self: center;
  background-color: ${({ theme }) => transparentize(0.8, theme.colors.accent)};
  border: 0.5px solid ${({ theme }) => theme.colors.accent};
  flex-shrink: 0;
  font-size: 0.625em;
  line-height: 0.75em;
  margin: 0 1em;
  padding: 0.5em;
  text-transform: uppercase;
`;

const Title = styled(Row)`
  flex-shrink: 0;
`;
