'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import classnames from 'classnames';
import { fusionSectionContentPadding } from './FusionSectionStyles';
import FusionSectionScroll from './FusionSectionScroll';
import FusionSectionContent from './FusionSectionContent';

type BaseProps = {
  mobileScroll?: false;
  columns: {
    xs?: number;
    sm?: number;
    md: number;
    lg: number;
    // xl;
  };
};

type ScrollProps = {
  mobileScroll: true;
  columns: {
    md: number;
    lg: number;
    // xl;
  };
};

type Props = { children: React.ReactNode; gap?: number; className?: string } & (BaseProps | ScrollProps);
const FusionSectionGrid = (props: Props) => {
  const grid = (
    <Grid
      className={classnames({ 'mobile-scroll': props.mobileScroll }, props.className)}
      columns={props.columns}
      gap={props.gap}
      paddingHorizontal={false}
      paddingVertical={false}
    >
      {props.children}
    </Grid>
  );

  return props.mobileScroll ? <FusionSectionScroll>{grid}</FusionSectionScroll> : grid;
};

FusionSectionGrid.defaultProps = {
  gap: 16,
};

const Grid = styled(FusionSectionContent)<BaseProps & { gap?: number }>`
  display: grid;
  grid-gap: ${({ gap }) => `${gap}px`};
  grid-template-columns: repeat(${({ columns }) => columns.lg ?? 4}, 1fr);
  padding-top: 8px;
  padding-bottom: 16px;
  ${fusionSectionContentPadding};

  @media (max-width: 1279px) {
    grid-template-columns: repeat(${({ columns }) => columns.md ?? 3}, 1fr);
  }

  @media (max-width: 959px) {
    &.mobile-scroll {
      grid-auto-flow: column;
      grid-auto-columns: 288px;
      grid-template-columns: unset;
      min-width: max-content;
    }
    &:not(.mobile-scroll) {
      grid-template-columns: repeat(${({ columns }) => columns.sm ?? 2}, 1fr);
    }
  }

  &:not(.mobile-scroll) {
    @media (max-width: 567px) {
      grid-template-columns: repeat(${({ columns }) => columns.xs ?? 1}, 1fr);
    }
  }
`;

export default FusionSectionGrid;
