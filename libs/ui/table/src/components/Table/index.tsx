'use client';
import React, { startTransition } from 'react';
import classNames from 'classnames';
import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import { ErrorBoundary } from '@benzinga/core-ui';
import { StyledTableRow, TableRow } from './TableRow';
import { TableHead } from './TableHead';
import { SortBy, getScrollbarWidth, sortRowData } from '../../libs/utils';
import { ColumnDef } from '../../interface';
import useScrollShadow from '../../libs/useScrollShadow';
import useResizeShadow from '../../libs/useResizeShadow';

export interface TableProps {
  className?: string;
  columnsDef: ColumnDef[];
  height?: number;
  isGated?: boolean;
  rowData: any[];
  title?: string;
  seeMoreLink?: string;
  seeMoreText?: string;
  hiddenColumns?: string[];
  hiddenColumnsWhenGated?: Set<string>;
  adPlacement?: React.ReactElement;
  hasStockTableStyling?: boolean;
  noDataText?: string;
}

export const Table: React.FC<TableProps> = ({
  adPlacement,
  className,
  columnsDef,
  hasStockTableStyling,
  height = 500,
  hiddenColumns,
  hiddenColumnsWhenGated,
  isGated,
  noDataText,
  rowData,
  seeMoreLink,
  seeMoreText,
  title,
}) => {
  const [sortBy, setSortBy] = React.useState<SortBy>({});

  const ref = React.useRef<HTMLDivElement | null>(null);
  const tableWrapperRef = React.useRef<HTMLDivElement>(null);
  const shadowRef = React.useRef<HTMLDivElement>(null);

  const { width } = Hooks.useRefElementSize(ref);
  const isGreaterThan700 = width > 700;

  const scrollbarWidth = getScrollbarWidth();

  useScrollShadow(tableWrapperRef, shadowRef);
  useResizeShadow(tableWrapperRef, shadowRef);

  const filteredColumnsDef = (columnsDef ?? []).filter(column => {
    const shouldBeHidden = Array.isArray(hiddenColumns) && hiddenColumns.includes(column.field);
    return !shouldBeHidden;
  });

  const handleSortChange = React.useCallback(
    (newSortByField: string, columnDef: ColumnDef) => {
      let direction: 'desc' | 'asc' = 'asc';
      startTransition(() => {
        if (sortBy.field !== newSortByField) {
          direction = 'asc';
          setSortBy({ columnDef, direction, field: newSortByField });
        } else if (sortBy?.direction === 'asc') {
          direction = 'desc';
          setSortBy({ columnDef, direction, field: newSortByField });
        } else if (sortBy?.direction === 'desc') {
          setSortBy({});
        } else {
          setSortBy({ columnDef, direction, field: newSortByField });
        }
      });
    },
    [sortBy],
  );

  const sortedRowData = React.useMemo(() => sortRowData(rowData ?? [], sortBy), [rowData, sortBy]);

  const isLastColumnSortable = !filteredColumnsDef[filteredColumnsDef.length - 1]?.sortEnabled || false;

  return (
    <Container
      $alignLastRowsRight={isLastColumnSortable}
      $hasStockTableStyling={hasStockTableStyling}
      $height={height}
      className={classNames('benzinga-core-table-container', { [`${className}`]: !!className })}
      ref={ref}
    >
      {title && (
        <div className="header-container">
          <h2 className="header-title">{title}</h2>
          <StyledLine />
          {seeMoreLink && (
            <a className="see-more-link" href={seeMoreLink} rel="noreferrer" target="_blank">
              See More
              {seeMoreText && <span className="sr-only">{seeMoreText}</span>}
            </a>
          )}
        </div>
      )}
      <div className="relative">
        <div className="benzinga-core-table-wrapper overflow-x-auto" ref={tableWrapperRef}>
          <StyledTable
            $alignLastRowsRight={isLastColumnSortable}
            $hasStockTableStyling={hasStockTableStyling}
            className={classNames('benzinga-core-table relative', {
              'min-w-[600px]': !isGreaterThan700,
              'min-w-[unset]': isGreaterThan700,
            })}
          >
            <TableHead
              $alignLastRowsRight={isLastColumnSortable}
              $hasStockTableStyling={hasStockTableStyling}
              columnsDef={filteredColumnsDef}
              hiddenColumnsWhenGated={hiddenColumnsWhenGated}
              isGated={isGated}
              onSortChange={handleSortChange}
              sortBy={sortBy}
            />
            <StyledTBody
              $isEmpty={!sortedRowData?.length}
              className="benzinga-core-table-tbody whitespace-nowrap w-full h-full overflow-y-auto"
            >
              {Array.isArray(sortedRowData) && sortedRowData.length ? (
                sortedRowData.map((item, index) => (
                  <React.Fragment key={`benzinga-core-table-row-${item.name}-${index}`}>
                    <TableRow
                      $hasStockTableStyling={hasStockTableStyling}
                      columnsDef={filteredColumnsDef}
                      data={item}
                      hiddenColumnsWhenGated={hiddenColumnsWhenGated}
                      index={index}
                      isGated={isGated && index > 2}
                      isPayWall={isGated}
                    />
                    {adPlacement && sortedRowData.length > 3 && index === 2 && (
                      <ErrorBoundary name="stock-movers-table-ad-placement">
                        <StyledTableRow $hasStockTableStyling={hasStockTableStyling}>
                          <td colSpan={filteredColumnsDef.length}>
                            <div>{adPlacement}</div>
                          </td>
                        </StyledTableRow>
                      </ErrorBoundary>
                    )}
                  </React.Fragment>
                ))
              ) : (
                <tr>
                  <td colSpan={filteredColumnsDef.length + 1}>
                    <div className="mt-2 text-center font-semibold">{noDataText || 'No data available to display'}</div>
                  </td>
                </tr>
              )}
            </StyledTBody>
          </StyledTable>
        </div>
        <StyledShadow $right={scrollbarWidth} ref={shadowRef} />
      </div>
    </Container>
  );
};

const Container = styled.div<{ $hasStockTableStyling?: boolean; $height?: number; $alignLastRowsRight?: boolean }>`
  &.benzinga-core-table-container {
    width: 100%;

    .header-container {
      display: flex;
      align-items: center;
      margin-bottom: 0.75rem;

      .header-title {
        z-index: 2;
        font-weight: 600;
        font-size: ${({ theme }) => theme.fontSize.base};
        line-height: 1;
        padding-right: 0.5rem;
        word-wrap: break-word;
        white-space: normal;
      }

      .see-more-link {
        font-weight: ${({ theme }) => theme.fontWeight.bold};
        font-size: ${({ theme }) => theme.fontSize.sm};
        color: ${({ theme }) => theme.colorPalette.blue500};
        margin-left: 0.625rem;
      }
    }

    .benzinga-core-table-wrapper {
      max-height: ${({ $height }) => ($height ? `${$height}px` : 'unset')};
      border-left: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};
      border-right: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};
      border-bottom: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};
    }

    table {
      th,
      td {
        padding: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '0.2rem 0.5rem' : '0.35rem 0.5rem')};
      }
      td {
        &:last-of-type {
          text-align: ${({ $alignLastRowsRight }) => ($alignLastRowsRight ? 'right' : 'unset')};
        }
      }
    }
  }
`;

export const StyledTable = styled.table<{ $alignLastRowsRight?: boolean; $hasStockTableStyling?: boolean }>`
  width: 100%;
  text-align: left;
  font-size: 12px;
  font-family: sans-serif;
  font-weight: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? 700 : `normal`)};

  tr {
    font-size: inherit;
  }

  th,
  td {
    padding: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '0.2rem 0.5rem' : '0.35rem 0.5rem')};
  }
  td {
    &:last-of-type {
      text-align: ${({ $alignLastRowsRight }) => ($alignLastRowsRight ? 'right' : 'unset')};
    }
  }
`;

export const StyledTBody = styled.tbody<{ $isEmpty?: boolean }>`
  &.benzinga-core-table-tbody,
  &.benzinga-core-virtualized-table-tbody {
    font-size: 0.75rem;
    height: ${({ $isEmpty }) => ($isEmpty ? '300px' : 'unset')};
    tr {
      @media (max-width: 800px) {
        height: 40px;
      }
    }
  }
`;

export const StyledLine = styled.div`
  flex-grow: 1;
  position: relative;
  height: 1px;

  &:before {
    content: '';
    width: 100%;
    height: 1px;
    display: block;
    background-color: #d6d6d6;
    position: absolute;
    top: 50%;
  }
`;

export const StyledShadow = styled.div<{ $right?: number }>`
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease-in-out,
    visibility 0.3s ease-in-out;
  position: absolute;
  top: 0;
  right: ${({ $right }) => ($right ? `${$right}px` : 0)};
  bottom: 0;
  width: 20px;
  box-shadow: inset -14px 0 20px -12px rgba(0, 0, 0, 0.2);
  z-index: 1;
  pointer-events: none;
`;
