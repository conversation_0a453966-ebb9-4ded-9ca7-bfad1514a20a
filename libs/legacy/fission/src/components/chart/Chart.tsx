import { <PERSON><PERSON><PERSON>, create<PERSON><PERSON>, <PERSON><PERSON>hart<PERSON>pi, ISeriesApi, MouseEventParams, SeriesType } from 'lightweight-charts';
import { isFunction } from 'ramda-adjunct';
import React, { Component, createRef, RefObject } from 'react';
import { EventKey, StockSymbol } from '../../entities';
import { ChartContainer, ChartInstance } from './styles';

import { ChartData, ChartRange, ChartType, ChartView } from './entities';

import { ThemeNames } from '@benzinga/themetron';
import { throttle } from 'lodash';
import { equals, isEmpty, isNil, length } from 'ramda';
import Controls from './Controls';
import EmptyChart from './EmptyChart';
import Tooltip, { TooltipOptions } from './Tooltip';
import {
  baseConfig,
  CHART_CONSTANTS,
  getSiftedChartValues,
  getUpdatedSeriesConfig,
  getUpdatedThemeConfig,
  modifyDataByCandlestickChart,
  modifyDataByLineChart,
} from './utils';

import { Observable, Subscription } from 'rxjs';
import { delay } from 'rxjs/operators';

export const THROTTLE_TIME = 60;

interface ResizeParameters {
  height: number;
  width: number;
}

type ChartSize = ResizeParameters;

interface PriceOpen {
  open: number;
}

type Price = (number & PriceOpen & BarPrice) | undefined;

interface State {
  chartSize: ChartSize;
  isFirstLoad: boolean;
  isTooltipShown: boolean;
  tooltipOptions?: TooltipOptions;
}

interface Props {
  changeRange: (range: ChartRange) => void;
  changeType: (type: ChartType) => void;
  chartData: ChartData[];
  chartRange: ChartRange;
  chartType: ChartType;
  error: boolean;
  height?: number | string;
  isLoaded: boolean;
  symbol: StockSymbol;
  theme: ThemeNames;
}

class Chart extends Component<Props, State> {
  static chartHeight = 200;

  state = {
    chartSize: {
      height: 0,
      width: 0,
    },

    isFirstLoad: true,
    isTooltipShown: false,

    tooltipOptions: {
      price: 0,
      x: 0,
      y: 0,
    },
  };

  private CHART: RefObject<HTMLDivElement> | null = createRef();
  private CHART_INSTANCE: IChartApi | null = null;
  private CHART_SERIES: ISeriesApi<SeriesType> | null = null;
  private mountSubscription: Subscription | null = null;

  componentDidMount() {
    if (this.CHART && this.CHART.current) {
      const DELAY_FOR_SUCCESSFUL_RENDERING = 100;

      this.mountSubscription = new Observable().pipe(delay(DELAY_FOR_SUCCESSFUL_RENDERING)).subscribe(() => {
        this.changeChartSize(this.createChartInstance);
      });
    }

    window.addEventListener(EventKey.resize, this.onThrottledResize);
  }

  componentDidUpdate(prevProps: Props) {
    const { chartType, isLoaded } = this.props;

    if (!equals(prevProps.isLoaded, isLoaded) && this.CHART && this.CHART.current) {
      this.changeChartSize(this.createChartInstance);
    }

    if (!equals(prevProps.chartType, chartType)) {
      this.createChartInstance();
    }
  }

  componentWillUnmount() {
    window.removeEventListener(EventKey.resize, this.onThrottledResize);

    if (this.mountSubscription) {
      this.mountSubscription.unsubscribe();
      this.mountSubscription = null;
    }

    this.unsubscribeChartInstance();

    if (this.CHART) {
      this.CHART = null;
    }
  }

  unsubscribeChartInstance = () => {
    if (!this.CHART_INSTANCE) {
      return;
    }

    this.CHART_INSTANCE.unsubscribeCrosshairMove(this.onThrottledMouseMove);

    if (this.CHART_SERIES) {
      this.CHART_INSTANCE.removeSeries(this.CHART_SERIES);
      this.CHART_SERIES = null;
    }

    this.CHART_INSTANCE.remove();
    this.CHART_INSTANCE = null;
  };

  onResize = () => {
    if (!this.CHART || !this.CHART.current) {
      return;
    }
    this.changeChartSize(this.resize);
  };

  resize = (parameters: ResizeParameters) => {
    if (!this.CHART_INSTANCE) {
      return;
    }
    const { height, width } = parameters;
    this.CHART_INSTANCE.resize(height, width);
    this.fitContent();
  };

  changeChartSize = (cb?: (parameters: ResizeParameters) => void) => {
    if (!this.CHART) {
      return;
    }

    const { height: chartHeight } = this.props;
    const height = (chartHeight && Number(chartHeight)) || Chart.chartHeight;
    const width = this.CHART.current?.getBoundingClientRect().width as number;

    const sideEffect = isFunction(cb)
      ? () => {
          cb({ height, width });
        }
      : undefined;

    this.setState(
      {
        chartSize: { height, width },
      },
      sideEffect,
    );
  };

  fitContent = () => {
    this.CHART_INSTANCE!.timeScale().fitContent();
  };

  removePrevChartInstanceIfExists = () => {
    if (!this.CHART || !this.CHART.current) {
      return;
    }

    this.unsubscribeChartInstance();
  };

  createChartInstance = () => {
    if (!this.CHART) {
      return;
    }

    const { chartData, chartRange, chartType, theme } = this.props;
    const {
      chartSize: { height, width },
      isFirstLoad,
    } = this.state;

    const { priceFormat } = CHART_CONSTANTS;

    if (isEmpty(chartData) || isNil(chartData)) {
      return;
    }

    this.removePrevChartInstanceIfExists();
    this.CHART_INSTANCE = createChart(this.CHART.current as HTMLDivElement, { height, width });

    const chartView = equals(chartType, ChartType.line) ? ChartView.line : ChartView.candlestick;

    this.CHART_SERIES = equals(chartView, ChartView.line)
      ? this.CHART_INSTANCE.addAreaSeries({ priceFormat })
      : this.CHART_INSTANCE.addCandlestickSeries({ priceFormat });

    const siftedData = getSiftedChartValues(chartData, chartRange);

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore issue with the ramda's typings
    const data = equals(chartView, ChartView.line)
      ? // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        modifyDataByLineChart(siftedData) // @ts-ignore
      : modifyDataByCandlestickChart(siftedData);

    this.CHART_SERIES.setData(data);

    const themeConfig = getUpdatedThemeConfig(baseConfig, theme);
    this.CHART_INSTANCE.applyOptions(themeConfig);

    this.CHART_INSTANCE.applyOptions({
      handleScale: {
        axisPressedMouseMove: false,
        mouseWheel: false,
        pinch: false,
      },
      handleScroll: {
        horzTouchDrag: false,
        mouseWheel: false,
        pressedMouseMove: false,
        vertTouchDrag: false,
      },
    });

    const seriesConfig = getUpdatedSeriesConfig(chartData);
    this.CHART_SERIES.applyOptions(seriesConfig);

    this.fitContent();

    this.CHART_INSTANCE.subscribeCrosshairMove(this.onThrottledMouseMove);

    if (isFirstLoad) {
      this.setState({ isFirstLoad: false });
    }
  };

  subscribeOnMouseMove = (params: MouseEventParams) => {
    const { point, seriesPrices } = params;

    if (!this.CHART_SERIES) {
      return;
    }

    if (!point) {
      this.setState({ isTooltipShown: false });
      return;
    }

    const price = seriesPrices.get(this.CHART_SERIES) as Price;

    if (!price) {
      return;
    }

    const tooltipOptions = {
      price: price.open || price,
      x: point.x,
      y: point.y,
    };

    this.setState({
      isTooltipShown: true,
      tooltipOptions,
    });
  };

  // eslint-disable-next-line @typescript-eslint/member-ordering
  onThrottledMouseMove = throttle(this.subscribeOnMouseMove, THROTTLE_TIME);
  // eslint-disable-next-line @typescript-eslint/member-ordering
  onThrottledResize = throttle(this.onResize, THROTTLE_TIME);

  changeRange = (range: ChartRange) => {
    const { changeRange } = this.props;
    changeRange(range);

    if (!this.CHART_INSTANCE) {
      return;
    }
  };

  changeType = (type: ChartType) => {
    const { changeType } = this.props;
    changeType(type);
  };

  render() {
    const { isFirstLoad, isTooltipShown, tooltipOptions } = this.state;
    const { chartData, chartRange, chartType, error, isLoaded, symbol, theme } = this.props;

    const shouldShowSpinner = !length(chartData) || (!isLoaded && isFirstLoad);
    const isEmptyData = isLoaded && (isEmpty(chartData) || isNil(chartData));

    return (
      <>
        <Controls
          changeRange={this.changeRange}
          changeType={this.changeType}
          chartData={chartData}
          chartRange={chartRange}
          chartType={chartType}
          isLoaded={isLoaded}
        />

        {shouldShowSpinner && <EmptyChart emptiness={isEmptyData} error={error} />}

        {!shouldShowSpinner && (
          <ChartContainer>
            <ChartInstance ref={this.CHART} />

            {isTooltipShown && <Tooltip options={tooltipOptions} symbol={symbol} theme={theme} />}
          </ChartContainer>
        )}
      </>
    );
  }
}

export default Chart;
