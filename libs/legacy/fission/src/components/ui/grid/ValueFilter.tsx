import React, { ChangeEvent, Component, ComponentClass, createRef, RefObject } from 'react';

import { IFilterParams } from '@ag-grid-community/core';
import { equals, isEmpty, isNil, path, pathOr } from 'ramda';
import styled from '@benzinga/themetron';

enum Range {
  Ge = 'ge',
  Gte = 'gte',
  Le = 'le',
  Lte = 'lte',
  Range = 'range',
}

enum FilterBooleanLogic {
  And = 'AND',
  Or = 'OR',
}

enum Filter {
  First = 'filter1',
  Second = 'filter2',
}

enum GridFilterFields {
  Range = 'range',
  RangeStart = 'rangeStart',
  RangeStop = 'rangeStop',
  Text = 'text',
}

interface GridFilter {
  [field: string]: string | Range;
  readonly range: Range;
  readonly rangeStart: string;
  readonly rangeStop: string;
  readonly text: string;
}

interface State {
  [filter: string]: GridFilter | string;
  readonly booleanLogic: FilterBooleanLogic;
  readonly filter1: GridFilter;
  readonly filter2: GridFilter;
}

const defaultState: GridFilter = {
  [GridFilterFields.Range]: Range.Ge,
  [GridFilterFields.RangeStart]: '',
  [GridFilterFields.RangeStop]: '',
  [GridFilterFields.Text]: '',
};

type Props = Record<string, unknown> & IFilterParams;

const createValueFilter = (): ComponentClass<Props> => {
  const instances: Map<string, Component<Props, State>> = new Map();

  return class FloatFilter extends Component<Props, State> {
    INPUT: RefObject<HTMLInputElement> = createRef();

    readonly state: State = {
      booleanLogic: FilterBooleanLogic.And,
      [Filter.First]: defaultState,
      [Filter.Second]: defaultState,
    };

    constructor(props: Props) {
      super(props);

      const pathToField = ['colDef', 'field'];
      const pathToCurrentWidgetId = ['agGridReact', 'props', 'context'];

      const filterName = path(pathToField, props) as string;
      const currentID = pathOr('', pathToCurrentWidgetId, props);
      const filterNameWithPrefix = `${currentID}-${filterName}`;

      if (!instances.has(filterNameWithPrefix)) {
        instances.set(filterNameWithPrefix, this);
        return this;
      }

      const currentInstance =
        instances.has(filterNameWithPrefix) && (instances.get(filterNameWithPrefix) as FloatFilter);

      if (
        currentInstance &&
        currentInstance instanceof FloatFilter &&
        equals(path(pathToField, currentInstance.props), filterName)
      ) {
        return currentInstance;
      }
    }

    _isFilterActive(filter: string) {
      const { range, rangeStart, rangeStop, text } = this.state[filter] as GridFilter;

      return equals(range, Range.Range)
        ? !(isNil(rangeStart) || isEmpty(rangeStart)) && !(isNil(rangeStop) || isEmpty(rangeStop))
        : !(isNil(text) || isEmpty(text));
    }

    isFilterActive() {
      return this._isFilterActive(Filter.First);
    }

    getModel() {
      return { ...this.state };
    }

    setModel = (model: State) => {
      if (model) {
        this.setState(model);
      } else {
        this.setState({
          filter1: defaultState,
          filter2: defaultState,
        });
      }
    };

    afterGuiAttached() {
      this.focus();
    }

    focus() {
      if (this.INPUT.current) {
        this.INPUT.current.focus();
      }
    }

    createOnChangeFunction = (field: string) => (filter: string) => (event: ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;

      this.setState(prevState => {
        const prevFilter = prevState[filter] as GridFilter;

        if (equals(prevFilter[field], newValue)) {
          return null;
        }

        return {
          [filter]: {
            ...prevFilter,
            [field]: newValue,
          },
        };
      }, this.props.filterChangedCallback);
    };

    // eslint-disable-next-line @typescript-eslint/member-ordering
    onChange = this.createOnChangeFunction(GridFilterFields.Text);
    // eslint-disable-next-line @typescript-eslint/member-ordering
    onChangeStartRange = this.createOnChangeFunction(GridFilterFields.RangeStart);
    // eslint-disable-next-line @typescript-eslint/member-ordering
    onChangeStopRange = this.createOnChangeFunction(GridFilterFields.RangeStop);

    onChangeRange = (filter: string) => (event: ChangeEvent<HTMLSelectElement>) => {
      const newValue = event.target.value as Range;
      this.setState(prevState => {
        const prevFilter = prevState[filter] as GridFilter;

        if (equals(prevFilter.range, newValue)) {
          return null;
        }

        return {
          [filter]: {
            ...prevFilter,
            range: newValue,
          },
        };
      }, this.props.filterChangedCallback);
    };

    onChangeBooleanLogic = (event: ChangeEvent<HTMLInputElement>) => {
      this.setState(
        {
          booleanLogic: event.target.value as FilterBooleanLogic,
        },
        this.props.filterChangedCallback,
      );
    };

    renderFilter(filter: string) {
      const { range, rangeStart, rangeStop, text } = this.state[filter] as GridFilter;

      return (
        <div>
          <Select className="ag-filter-select" id="filterType" onChange={this.onChangeRange(filter)} value={range}>
            <option value={Range.Ge}>Greater than</option>
            <option value={Range.Gte}>Greater than or equals</option>
            <option value={Range.Range}>Range</option>
            <option value={Range.Lte}>Less than or equals</option>
            <option value={Range.Le}>Less than</option>
          </Select>
          {equals(range, Range.Range) ? (
            <div>
              <div>
                <TextInput
                  className="ag-filter-filter"
                  onChange={this.onChangeStartRange(filter)}
                  placeholder="Filter..."
                  value={rangeStart}
                />
              </div>
              <div>
                <TextInput
                  className="ag-filter-filter"
                  onChange={this.onChangeStopRange(filter)}
                  placeholder="Filter..."
                  value={rangeStop}
                />
              </div>
            </div>
          ) : (
            <div>
              <TextInput
                className="ag-filter-filter"
                onChange={this.onChange(filter)}
                placeholder="Filter..."
                ref={this.INPUT}
                value={text}
              />
            </div>
          )}
        </div>
      );
    }

    render() {
      return (
        <AgFilterBody>
          {this.renderFilter(Filter.First)}
          {this._isFilterActive(Filter.First) ? (
            <>
              <AgFilterCondition>
                <AndInput
                  className="and"
                  defaultChecked
                  name="booleanLogic"
                  onChange={this.onChangeBooleanLogic}
                  type="radio"
                  value="AND"
                />
                <label htmlFor="and">AND</label>
                <OrInput
                  className="or"
                  name="booleanLogic"
                  onChange={this.onChangeBooleanLogic}
                  type="radio"
                  value="OR"
                />
                <label htmlFor="or">OR</label>
              </AgFilterCondition>
              {this.renderFilter(Filter.Second)}
            </>
          ) : null}
        </AgFilterBody>
      );
    }
  };
};

const AgFilterBody = styled.div`
  margin: auto;
  padding: 2px 0 8px;
  width: 94%;
`;

const AgFilterCondition = styled.div`
  display: flex;
  justify-content: center;
  height: 24px;
  padding: 5px;
`;

const AndInput = styled.input`
  margin-right: 2px;
`;

const OrInput = styled.input`
  margin: 0 2px 0 10px;
`;

const TextInput = styled.input`
  width: 100%;
  padding-left: 6px;
`;

const Select = styled.select`
  width: 100%;
  height: 22px;
  padding-left: 2px;
`;

export default createValueFilter;
