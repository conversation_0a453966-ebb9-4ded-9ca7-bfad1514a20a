import React, { Component, createRef } from 'react';
import { DateTime } from 'luxon';
import { map, match, equals } from 'ramda';

import { AuthenticationManager, PersistStorageKey } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import * as LightweightCharts from 'lightweight-charts';
import ChartSocket, { Instrument } from './ChartSocket';
import * as protos from './lastdb';
import { TimeFormat, getTimeDisplayFormat } from '@benzinga/date-utils';
import { safeCancelable, SafePromiseCancelableReturn, SafeError } from '@benzinga/safe-await';
import { WidgetContext } from '@benzinga/widget-tools';

export declare type OnStateChange = (state: LoadState) => void;

export interface LoadState {
  message: string | null;
}

export interface ChartProps {
  from: string;
  height: number;
  onLoadStateChange?: OnStateChange;
  resolution: string;
  showVolume?: boolean;
  symbol: string;
  theme?: string;
  timeFormat: TimeFormat;
  timeOffset: number;
  type: 'line' | 'bar';
  volumeEnabled: boolean;
  width: number;
  widgetId: string | undefined;
  isWidgetActive: boolean;
}

const chartColor = {
  bzBlack: '#000000',
  bzGreyDark: '#373f49',
  bzGreyDarker: '#28313C',
  bzGreyLight: '#C5CEDA',
  bzOrange: '#E3A542',
  bzWhite: '#ffffff',
  darkDown: '#E5594E',
  darkUp: '#30BFA3',
  down: '#C75050',
  up: '#50C773',
};

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface State {}

/**
 * Uses lightweight charts to implement a fairly simple real-time capable chart.
 * - No implementation of the Lightweight api is exposed.
 * - Few as possible dependencies on pro code, including themes. Treat this close to a 3rd party lib.
 *
 * https://github.com/tradingview/lightweight-charts/blob/master/docs/README.md
 */
class ProLightweightChartInternal extends Component<ChartProps, State> {
  static contextType = SessionContext;

  chart!: LightweightCharts.IChartApi;
  priceSeries?:
    | {
        bar?: undefined;
        line: ReturnType<LightweightCharts.IChartApi['addAreaSeries']>;
      }
    | {
        bar: ReturnType<LightweightCharts.IChartApi['addCandlestickSeries']>;
        line?: undefined;
      };
  volumeSeries?: ReturnType<LightweightCharts.IChartApi['addHistogramSeries']>;
  chartSocket!: ChartSocket; // This is set in componentDidMount and thereafter will never be null.

  private chartRef = createRef<HTMLDivElement>();
  private resolveSymbolRef: SafePromiseCancelableReturn<Instrument> | null = null;
  private onBarsRef: SafePromiseCancelableReturn<protos.quotestore.IGetBarsResponse> | null = null;

  private series: any[] = [];

  private themes: Map<string | undefined, any> = new Map();
  private currentTheme: any;

  private currentSubscriptionGuid: string | null = null;

  constructor(props: ChartProps, context: typeof SessionContext) {
    super(props, context);

    this.themes.set('dark', {
      background: chartColor.bzGreyDarker,
      borderColor: chartColor.bzGreyLight,
      downColor: chartColor.darkDown,
      gridColor: chartColor.bzGreyDark,
      textColor: chartColor.bzGreyLight,
      upColor: chartColor.darkUp,
    });
    this.themes.set('light', {
      background: chartColor.bzWhite,
      borderColor: chartColor.bzGreyDark,
      downColor: chartColor.down,
      gridColor: chartColor.bzGreyLight,
      textColor: chartColor.bzGreyDark,
      upColor: chartColor.up,
    });
    this.themes.set('antique', {
      background: chartColor.bzBlack,
      borderColor: chartColor.bzOrange,
      downColor: chartColor.down,
      gridColor: chartColor.bzGreyDark,
      textColor: chartColor.bzOrange,
      upColor: chartColor.up,
    });
    this.themes.set('highContrast', {
      background: chartColor.bzBlack,
      borderColor: chartColor.bzGreyLight,
      downColor: chartColor.down,
      gridColor: chartColor.bzGreyDark,
      textColor: chartColor.bzWhite,
      upColor: chartColor.up,
    });
    if (this.themes.has(props.theme)) {
      this.setTheme(props.theme!);
    } else {
      this.setTheme('dark');
    }
  }
  convertTime =
    (convertTime: boolean) =>
    (time: number): LightweightCharts.Time => {
      return (time + (convertTime ? this.props.timeOffset * 60 : 0)) as LightweightCharts.Time;
    };

  setTheme(theme: string) {
    if (!this.themes.has(theme)) {
      throw `unknown theme ${theme}`;
    }
    this.currentTheme = this.themes.get(theme);
  }

  componentDidMount() {
    this.chart = LightweightCharts.createChart(this.chartRef.current!, this.buildChartConfig());

    this.chartSocket = ChartSocket.getInstance(
      {
        auth: {
          iamKey: (this.context as React.ContextType<typeof SessionContext>)
            .getManager(AuthenticationManager)
            .getBenzingaToken(),
        },
        onReady: () => {
          this.load(this.props.symbol);
        },
      },
      this.props.widgetId ?? this.props.symbol,
    );
    if (this.chartSocket) {
      this.load(this.props.symbol);
    }
  }
  componentWillUnmount() {
    if (this.chartSocket && !this.props.isWidgetActive) {
      this.chartSocket.close();
    }
  }

  componentDidUpdate(prevProps: ChartProps) {
    if (
      prevProps.from !== this.props.from ||
      prevProps.symbol !== this.props.symbol ||
      prevProps.resolution !== this.props.resolution ||
      prevProps.type !== this.props.type ||
      prevProps.volumeEnabled !== this.props.volumeEnabled
    ) {
      try {
        this.load(this.props.symbol);
      } catch (error) {
        this.updateLoadState(`Failed to load chart; please try again.`);
      }
    }
    if (prevProps.width !== this.props.width) {
      this.chart.resize(this.props.height, this.props.width);
    }
    if (prevProps.theme !== this.props.theme && this.props.theme) {
      this.setTheme(this.props.theme);
      this.chart.applyOptions(this.buildChartConfig());
    }
  }

  render() {
    return <div ref={this.chartRef} />;
  }

  private updateLoadState(message: string | null) {
    if (this.props.onLoadStateChange) {
      this.props.onLoadStateChange({ message });
    }
  }

  /*
   * Loads a new symbol and chart.
   * - Guarantee that only one sub per chart instance is active.
   */
  private load(symbol: string) {
    this.updateLoadState(`Resolving ${symbol}`);
    if (this.currentSubscriptionGuid) {
      this.chartSocket.unsubscribeBars(this.currentSubscriptionGuid);
    }
    // Clear the chart
    this.series.forEach(series => {
      this.chart.removeSeries(series);
    });
    //resizing price for scale
    this.chart.priceScale().applyOptions({
      scaleMargins: {
        bottom: 0,
        top: 0,
      },
    });

    this.series = [];

    this.resolveSymbolRef?.cancel();
    this.onBarsRef?.cancel();

    this.resolveSymbolRef = safeCancelable(
      new Promise(resolve => {
        this.chartSocket.resolveSymbol(
          symbol,
          instrument => {
            try {
              resolve({ ok: instrument });
            } catch (error) {
              resolve({ err: new SafeError(`Failed to load chart for ${symbol}: ${error}`, 'error') });
            }
          },
          error => {
            resolve({ err: new SafeError(error.message as string, 'error') });
          },
        );
      }),
    );

    this.resolveSymbolRef.promise.then(resp => {
      if (resp.canceled) return;
      if (resp.err) this.updateLoadState(resp.err.message);
      else this.load2(resp.ok);
    });
  }

  private load2(instrument: Instrument) {
    this.updateLoadState(`Loading...`);

    const symbol = `${instrument.exchange}:${instrument.symbol}`;

    const chartResolution = this.toResolution(this.props.resolution);
    const anySession = false;

    this.onBarsRef = safeCancelable(
      new Promise(resolve => {
        this.chartSocket.getBars(
          symbol,
          chartResolution,
          0,
          this.props.from,
          anySession,
          barsResult => {
            resolve({ ok: barsResult });
          },
          error => {
            resolve({ err: new SafeError(error.message ?? '', 'error') });
          },
        );
      }),
    );

    this.onBarsRef.promise.then(resp => {
      if (resp.canceled) return;
      if (resp.err) this.updateLoadState(resp.err.message);
      else {
        const barsResult = resp.ok;
        this.updateLoadState(null);

        const subGuid = `${symbol}_${chartResolution}`;
        this.currentSubscriptionGuid = subGuid;

        // Charts with lower resolution don't contain timestamps, only dates
        const convertTime = !['D', '1w', '2w', 'MO'].includes(chartResolution);
        this.addPriceSeries(barsResult, convertTime);
        this.props.volumeEnabled && this.addVolumeSeries(barsResult, convertTime);
        this.chartSocket.subscribeBars(symbol, chartResolution, this.onBarUpdate(convertTime), subGuid);
        this.chart.timeScale().fitContent();
      }
    });
  }

  private toResolution(interval: string) {
    if (interval === '1d') {
      return 'D';
    } else if (!equals(match(/^[0-9]+m$/, interval), [])) {
      return String(interval.substring(0, interval.length - 1));
    } else if (!equals(match(/^[0-9]+h$/, interval), [])) {
      return String(parseInt(interval.substring(0, interval.length - 1)) * 60);
    } else if (!equals(match(/^[0-9]+w$/, interval), [])) {
      return interval;
    } else if (!equals(match(/^[0-9]+mo$/, interval), [])) {
      return 'MO';
    } else {
      throw new Error(`Unable to parse interval ${interval}`);
    }
  }

  private onBarUpdate = (convertTime: boolean) => (guid: string, candle: protos.quotestore.ICandle) => {
    if (guid !== this.currentSubscriptionGuid) {
      return;
    }
    if (this.priceSeries?.bar) {
      this.mapBar(convertTime)(candle).length > 0 && this.priceSeries.bar.update(this.mapBar(convertTime)(candle)[0]);
    }
    if (this.priceSeries?.line) {
      this.mapLine(convertTime)(candle).length > 0 &&
        this.priceSeries.line.update(this.mapLine(convertTime)(candle)[0]);
    }
    if (this.volumeSeries) {
      this.mapVolume(convertTime)(candle).length > 0 &&
        this.volumeSeries.update(this.mapVolume(convertTime)(candle)[0]);
    }
  };

  private buildChartConfig = () => {
    const { timeFormat } = this.props;
    const c: LightweightCharts.DeepPartial<LightweightCharts.ChartOptions> = {
      crosshair: {
        mode: LightweightCharts.CrosshairMode.Normal,
      },
      grid: {
        horzLines: {
          color: this.currentTheme.gridColor,
        },
        vertLines: {
          color: this.currentTheme.gridColor,
        },
      },
      height: this.props.height,
      layout: {
        backgroundColor: this.currentTheme.background,
        textColor: this.currentTheme.textColor,
      },
      localization: {
        timeFormatter: (businessDayOrTimestamp: number) =>
          DateTime.fromSeconds(businessDayOrTimestamp)
            .setZone('Etc/UTC')
            .toFormat(`dd, MMM#yy ${getTimeDisplayFormat({ timeFormat })}`)
            .replace('#', " '"),
      },
      priceScale: {
        borderColor: this.currentTheme.borderColor,
        scaleMargins: {
          bottom: 0.04,
          top: 0.04,
        },
      },

      timeScale: {
        borderColor: this.currentTheme.borderColor,
        rightBarStaysOnScroll: true,
        secondsVisible: false,
        timeVisible: true,
      },
      width: this.props.width,
    };
    return c;
  };
  private mapBar =
    (convertTime: boolean) =>
    (candle: protos.quotestore.ICandle): LightweightCharts.BarData[] => {
      if (!candle.close || !candle.high || !candle.low || !candle.open) return [];
      return [
        {
          close: candle.close,
          high: candle.high,
          low: candle.low,
          open: candle.open,
          time: this.convertTime(convertTime)(candle.time as number),
        },
      ];
    };

  private mapLine =
    (convertTime: boolean) =>
    (candle: protos.quotestore.ICandle): LightweightCharts.LineData[] => {
      if (!candle.close) return [];
      return [
        {
          time: this.convertTime(convertTime)(candle.time as number),
          value: candle.close,
        },
      ];
    };

  private mapVolume =
    (convertTime: boolean) =>
    (candle: protos.quotestore.ICandle): LightweightCharts.HistogramData[] => {
      if (!candle.close || !candle.open || !candle.volume) return [];
      const color = `${
        candle.open <= candle.close ? `${this.currentTheme.upColor}60` : `${this.currentTheme.downColor}60`
      }`;
      return [
        {
          color: color,
          time: this.convertTime(convertTime)(candle.time as number),
          value: typeof candle.volume === 'number' ? candle.volume : 0,
        },
      ];
    };

  private addVolumeSeries(candlesResponse: protos.quotestore.IGetBarsResponse, convertTime: boolean) {
    const data = map(this.mapVolume(convertTime), candlesResponse.bars ?? []).flat();

    this.volumeSeries = this.chart.addHistogramSeries({
      lineWidth: 1,
      overlay: true,
      priceFormat: {
        type: 'volume',
      },
      // this is needed to make transparent histogram bars load, for some reason.
      // this can be tested and removed if tradingview is updated.
      priceLineColor: 'white',
      scaleMargins: {
        bottom: 0,
        top: 0.7,
      },
    });
    this.series.push(this.volumeSeries);
    this.volumeSeries.setData(data);
  }

  private addPriceSeries(candlesResponse: protos.quotestore.IGetBarsResponse, convertTime: boolean) {
    if (this.props.type === 'line') {
      const data = (candlesResponse.bars ?? []).map(this.mapLine(convertTime)).flat();
      let clr = this.currentTheme.downColor;
      if (data[data.length - 1].value > data[0].value) {
        clr = this.currentTheme.upColor;
      }

      this.priceSeries = {
        line: this.chart.addAreaSeries({
          bottomColor: `${clr}08`,
          lineColor: clr,
          lineWidth: 1,
          topColor: `${clr}80`,
        }),
      };
      this.series.push(this.priceSeries.line);
      this.priceSeries.line.setData(data);
    } else if (this.props.type === 'bar') {
      const data = map(this.mapBar(convertTime), candlesResponse.bars ?? []).flat();
      this.priceSeries = {
        bar: this.chart.addCandlestickSeries({
          borderDownColor: this.currentTheme.downColor,
          borderUpColor: this.currentTheme.upColor,
          downColor: this.currentTheme.downColor,
          upColor: this.currentTheme.upColor,
          wickDownColor: this.currentTheme.downColor,
          wickUpColor: this.currentTheme.upColor,
        }),
      };
      this.series.push(this.priceSeries.bar);
      this.priceSeries.bar.setData(data);
    }
  }
}

export const ProLightWeightChart: React.FC<Omit<ChartProps, 'widgetId' | 'isWidgetActive'>> = props => {
  const widgetContext = React.useContext(WidgetContext);
  const widgetsList = React.useMemo(() => window.sessionStorage.getItem(PersistStorageKey.widgets), []);

  const isWidgetActive = React.useMemo(() => {
    if (!widgetsList || widgetsList.length === 0) return false;
    return JSON.parse(widgetsList).list.some(w => w.widgetId === widgetContext.widgetId);
  }, [widgetContext.widgetId, widgetsList]);

  return <ProLightweightChartInternal {...props} isWidgetActive={isWidgetActive} widgetId={widgetContext.widgetId} />;
};
