.footer {
  background-color: #0c2140;
  width: 100%;
  position: relative;

  &.has-sticky-nav {
    margin-bottom: 70px;
  }

  .footer-top-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    margin: 0 auto;
    padding: 2rem 0;

    @media (min-width: 640px) {
      max-width: 640px;
    }

    @media screen and (min-width: 768px) {
      padding: 2rem 0;
      max-width: 768px;
    }

    @media (min-width: 1024px) {
      max-width: 1024px;
    }

    @media (min-width: 1220px) {
      max-width: 1220px;
    }

    .benzinga-links-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 1rem;
      gap: 24px;

      .benzinga-logo {
        flex-shrink: 0;
        margin: auto 0;
        max-width: 240px;

        @media screen and (min-width: 768px) {
          max-width: 200px;
        }
      }

      @media screen and (min-width: 768px) {
        padding: 1rem 4rem;
      }

      @media screen and (min-width: 992px) {
        flex-direction: row;
      }
    }

    .logo-and-socials-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1 1 0%;

      @media screen and (min-width: 1024px) {
        flex-direction: row;
        align-items: center;
      }
    }

    .social-sites-list {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      margin-top: 1rem;
      flex-wrap: wrap;

      @media screen and (min-width: 1024px) {
        margin: 0 auto;
      }
    }
  }

  .new-footer-top-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 0 auto;

    .benzinga-links-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 1rem;
      gap: 24px;

      .benzinga-logo {
        flex-shrink: 0;
        margin: auto 0;
        max-width: 240px;

        @media screen and (min-width: 768px) {
          max-width: 200px;
        }
      }

      @media screen and (min-width: 768px) {
        padding: 1rem 4rem;
      }

      @media screen and (min-width: 992px) {
        flex-direction: row;
      }
    }

    .new-logo-and-socials-wrapper {
      display: flex;
      flex-direction: column;
      gap: 15px;
      margin-bottom: 20px;

      @media screen and (min-width: 1024px) {
        flex-direction: column;
      }
    }

    .newsletter-description {
      font-family: Arial, sans-serif;
      font-size: 14.4px;
      color: #b3b3b3;
    }

    .new-social-sites-list {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 0.75rem;
      flex-wrap: wrap;
    }

    .app-store-wrapper {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 0.5rem;
      gap: 1.25rem;

      @media screen and (min-width: 768px) {
        margin-bottom: 0;
        justify-content: flex-start;
      }
    }
  }

  .newsletter-form {
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }

  .error-message {
    margin-top: -10px;
    text-align: left;
    color: #db3737;
  }

  .success-message {
    margin-top: -10px;
    text-align: left;
    color: #31c48d
  }

  .input-error {
    border: 2px solid #ff4d4d;
    background: #ff4d4d33;
    color: white;
  }
  .input-success {
    border: 2px solid #4caf50;
    background: #4caf5033;
    color: white;
  }

  .newsletter-form input {
    width: 100%;
    padding: 10px;
    height: 35px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 13.4px;
  }

  .newsletter-form button {
    font-size: 13.4px;
    padding: 10px 20px;
    background-color: #0052cc;
    color: white;
    border: none;
    height: 35px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .newsletter-form button:hover {
    background-color: #003d99;
  }

  .copyright-bar {
    margin-top: 3rem;
    padding-top: 1.5rem;
    padding-bottom: 1.3rem;
    border-top: 1px solid #333;
    text-align: center;
    font-size: 0.9rem;
    color: #b3b3b3;
  }

  .footer-bottom-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #225aa9;
    color: #fff;
    text-align: center;
    width: 100%;
    padding: 1rem 0;
    box-sizing: border-box;
    border-bottom: 1px solid rgb(184, 203, 230);
    border-top: 1px solid rgb(184, 203, 230);

    ul {
      display: flex;
      flex-direction: column;
      text-align: center;
      margin: 0;
      padding: 0;

      .footer-link {
        color: #fff;
        display: block;
        padding-top: 0;
        text-decoration: none;
        -webkit-text-decoration: none;
        font-size: 12px;
        margin: 5px 0;

        &:hover {
          text-decoration: underline;
        }

        a {
          color: #fff;
        }

        @media screen and (min-width: 700px) {
          margin: 0 1rem 0 0;
        }
      }

      @media screen and (min-width: 480px) {
        padding: 0 0.5rem;
      }

      @media screen and (min-width: 768px) {
        flex-direction: row;
      }
    }

    .copyright-section {
      font-size: 12px;

      @media screen and (min-width: 768px) {
        margin-top: 0;
        width: 25%;
      }
    }
  }

  .footer-bottom-common {
    @media screen and (min-width: 480px) {
      text-align: left;
      padding: 0.5rem;
    }

    @media screen and (min-width: 768px) {
      justify-content: space-between;
      min-height: 2.75rem;
    }

    @media screen and (min-width: 992px) {
      justify-content: space-between;
      flex-direction: row;
    }

    @media screen and (min-width: 1300px) {
      padding-left: 9rem;
      padding-right: 9rem;
    }
  }


  .new-footer-bottom-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
  }
}
