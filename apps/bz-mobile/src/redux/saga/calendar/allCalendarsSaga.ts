import { all, call, put, takeEvery } from 'redux-saga/effects';
import { GET_ALL_CALENDAR_LOADING, GET_ALL_CALENDAR_REQUEST, GET_ALL_CALENDAR_SUCCESS } from '../../actions/types';
import Data from '../../../data-mobile/data';
import { CommonCalendarQueryParams } from '@benzinga/calendar-commons';
import { SafeType } from '@benzinga/safe-await';
import { Earning } from '@benzinga/calendar-earnings-manager';
import { Ipo } from '@benzinga/calendar-ipos-manager';
import { Rating } from '@benzinga/calendar-ratings-manager';
import { MergersAndAcquisitions } from '@benzinga/calendar-ma-manager';
import { Economics } from '@benzinga/calendar-economics-manager';
import { Fda } from '@benzinga/calendar-fda-manager';
import { Conference } from '@benzinga/calendar-conference-calls-manager';
import { OptionsActivity } from '@benzinga/calendar-option-activity-manager';
import { Dividend } from '@benzinga/calendar-dividends-manager';

interface FetchAllCalendarDataProps {
  type: string;
  calendarRequestData: {
    calendarType: string;
    params: CommonCalendarQueryParams;
  }[];
  cbFunction: () => undefined;
}

type FetchAllCalendarDataResponse = [
  earnings: SafeType<Earning[]>,
  ratings: SafeType<Rating[]>,
  ipos: SafeType<Ipo[]>,
  economic: SafeType<Economics[]>,
  mergersAcquisitions: SafeType<MergersAndAcquisitions[]>,
  fda: SafeType<Fda[]>,
  conferenceCalls: SafeType<Conference[]>,
  unusualOptions: SafeType<OptionsActivity[]>,
  dividends: SafeType<{ dividends: Dividend[] }>,
];

export function* allCalendarRequest() {
  yield takeEvery(GET_ALL_CALENDAR_REQUEST, fetchAllCalendarData);
}

function* fetchAllCalendarData({ calendarRequestData }: FetchAllCalendarDataProps) {
  try {
    const calendarRequests = calendarRequestData.map(calendarData => {
      const calendarManager = Data.getCalendarManager(calendarData.calendarType);
      if (calendarManager) {
        return call(calendarManager.fetchCalendarData, calendarData.params);
      }
      return undefined;
    });
    // turning on loader
    yield put({
      type: GET_ALL_CALENDAR_LOADING,
      data: true,
    });

    // waiting for all APIs response
    const [
      earnings,
      ratings,
      ipos,
      economic,
      mergersAcquisitions,
      fda,
      conferenceCalls,
      unusualOptions,
      dividends,
    ]: FetchAllCalendarDataResponse = yield all(calendarRequests);

    // turning off loader
    yield put({
      type: GET_ALL_CALENDAR_LOADING,
      data: false,
    });

    // storing all data to redux
    yield put({
      type: GET_ALL_CALENDAR_SUCCESS,
      data: {
        earnings: earnings?.ok,
        ratings: ratings?.ok,
        ipos: ipos?.ok,
        economic: economic?.ok,
        mergersAcquisitions: mergersAcquisitions?.ok,
        fda: fda?.ok,
        conferenceCalls: conferenceCalls?.ok,
        unusualOptions: unusualOptions?.ok,
        dividends: dividends?.ok?.dividends,
      },
    });
  } catch (error) {
    yield put({
      type: GET_ALL_CALENDAR_LOADING,
      data: false,
    });
    console.log('Error in one of the calendarSaga API', error);
  }
}
