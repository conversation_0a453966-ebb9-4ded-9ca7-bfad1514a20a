import { StackNavigationProp } from '@react-navigation/stack';
import React, { useCallback, useEffect, useState } from 'react';
import { Linking, ScrollView, StyleSheet, Text, View } from 'react-native';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { useTheme } from '../../theme/themecontext';
import { WP, size } from '../../services';
import PremiumIdeaFeed from '../../components/Ideas/PremiumIdeaFeed';
import { PostButton } from '../../components/Buttons/PostButton';
import Data from '../../data-mobile/data';
import { useIsLoggedIn } from '../../hooks/useIsLoggedIn';
import WebView from 'react-native-webview';
import { useAppSelector } from '../../redux/hooks';
import { Navigation } from '../../components/Navigation';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import useTrackPageView from '../../hooks/useTrackPageView';

export interface ExpertsArrayProps {
  locked: boolean;
  name: string;
  product_id: string;
  package_ids: number[];
  profileUrl: string;
}

const PremiumCardScreen = ({
  navigation,
}: {
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
}) => {
  const Tab = createMaterialTopTabNavigator();
  const { colors } = useTheme();
  const styles = withColors(colors);
  const isLoggedIn = useIsLoggedIn();
  const stockIdeasFeed = Data.tradeIdeas().premiumFeed(
    {
      limit: 10,
    },
    'stock | ticker',
  );
  const optionsIdeasFeed = Data.tradeIdeas().premiumFeed(
    {
      limit: 10,
    },
    'option',
  );

  const [expertsArray, setExpertsArray] = useState<ExpertsArrayProps[]>([
    { locked: false, name: 'All', package_ids: [], product_id: 'all-premium-ideas', profileUrl: '' },
  ]);
  const { hasPremiumStatus: hasPremium } = useAppSelector(state => state.idea);
  useTrackPageView('Ideas', '_');
  useEffect(() => {
    (async function () {
      const experts = await stockIdeasFeed.getExpertsArray();
      setExpertsArray(expertsArray.concat(experts));
    })();
  }, [isLoggedIn]);

  const showAccount = useCallback(() => {
    global.Analytics.event('Navigation', 'Show Account', 'Premium Trades');
    navigation.push('AccountNav');
  }, []);

  const showSearch = useCallback(() => {
    global.Analytics.event('Navigation', 'Show Search', 'Premium Trades');
    navigation.push('Search');
  }, []);

  const showNotifications = () => {
    global.Analytics.event('Navigation', 'Show Notifications', 'Ideas Page');
    navigation.push('Notifications');
  };

  if (!hasPremium) {
    return (
      <View style={{ backgroundColor: colors.cardBackground, flex: 1 }}>
        <Navigation
          navigation={navigation}
          showAccount={showAccount}
          showSearch={showSearch}
          showNotification={showNotifications}
        />
        <ScrollView style={styles.noPremiumContainer} showsVerticalScrollIndicator={false} bounces={false}>
          <View style={[styles.imageBackgroundStyle, { backgroundColor: colors.cardBackground }]}>
            <WebView
              source={{
                uri: 'https://fast.wistia.net/embed/iframe/0jyc0hwy7s?twitter=true',
              }}
              style={[
                {
                  height: WP(45),
                  width: WP(80),
                },
              ]}
              containerStyle={[{ borderRadius: 5 }]}
              sharedCookiesEnabled={true}
              allowsFullscreenVideo={true}
              allowsInlineMediaPlayback={true}
              onError={syntheticEvent => {
                const { nativeEvent } = syntheticEvent;
                console.error('WebView error: ', nativeEvent);
              }}
              onShouldStartLoadWithRequest={request => {
                if (request.url.length) {
                  return true;
                } else {
                  return false;
                }
              }}
            />
          </View>
          <View
            style={{
              alignSelf: 'center',
              justifyContent: 'center',
              marginHorizontal: 8,
              // backgroundColor: 'white',
              marginTop: WP(8),
            }}
          >
            <Text style={styles.headerTextStyle}>
              Profit With Actionable Stock News, Trading Signals and Intelligent Chatrooms
            </Text>
          </View>
          <View style={{ alignSelf: 'center', justifyContent: 'center', marginHorizontal: 30, marginTop: WP(6) }}>
            <Text
              style={{
                textAlign: 'center',
                fontSize: 16,
                color: colors.dimmedText,
              }}
            >
              Streaming platform with all the information you need to invest smarter today
            </Text>
          </View>
          <View style={{ flexDirection: 'row', marginHorizontal: 15, alignSelf: 'center', marginTop: WP(6) }}>
            <PostButton
              style={styles.buttonStyle}
              title="Subscribe Now"
              containerStyle={{ width: '50%' }}
              onPress={() => navigation.navigate('MySubscription')}
            />
            <PostButton
              style={[
                styles.buttonStyle,
                {
                  backgroundColor: colors.card,
                },
              ]}
              title="Learn More"
              containerStyle={{ width: '50%' }}
              onPress={() => {
                Linking.openURL(
                  'https://www.benzinga.com/services?utm_source=benzinga.com&utm_medium=mobile_app&utm_campaign=ideas',
                );
              }}
            />
          </View>
        </ScrollView>
      </View>
    );
  }

  const renderTabComponent = (tab: string) => {
    useTrackPageView('Ideas', tab);
    switch (tab) {
      case 'Stock':
        return (
          <PremiumIdeaFeed
            ideasFeed={stockIdeasFeed}
            navigation={navigation}
            expertsArray={expertsArray}
            feedType="stock"
          />
        );
      case 'Options':
        return (
          <PremiumIdeaFeed
            ideasFeed={optionsIdeasFeed}
            navigation={navigation}
            expertsArray={expertsArray}
            feedType="option"
          />
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.tabsContainer}>
      <Navigation
        navigation={navigation}
        showAccount={showAccount}
        showSearch={showSearch}
        showNotification={showNotifications}
      />
      {expertsArray ? (
        <Tab.Navigator
          screenOptions={{
            swipeEnabled: false,
            tabBarLabelStyle: styles.tabBarLabelStyle,
            tabBarContentContainerStyle: { backgroundColor: colors.background },
            tabBarIndicatorStyle: {
              backgroundColor: 'transparent',
            },
            lazy: true,
            tabBarItemStyle: {
              marginVertical: 10,
            },

            tabBarLabel({ children, focused }) {
              return (
                <View
                  style={[
                    styles.tabBarLabelContainer,
                    {
                      backgroundColor: focused ? colors.buttonBlueDefault : colors.card,
                      borderWidth: focused ? 0 : 0.6,
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.tabBarLabelText,
                      {
                        color: colors.text,
                      },
                    ]}
                  >
                    {children}
                  </Text>
                </View>
              );
            },
          }}
        >
          {['Stock', 'Options'].map(tab => {
            return <Tab.Screen children={() => renderTabComponent(tab)} key={tab} name={tab} />;
          })}
        </Tab.Navigator>
      ) : (
        <View style={[styles.emptyContainer, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.emptyText, { color: colors.textInverted, textAlign: 'center' }]}>No Rows To Show</Text>
        </View>
      )}
    </View>
  );
};

export default PremiumCardScreen;

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    tabsContainer: {
      flex: 1,
    },
    noPremiumContainer: {
      backgroundColor: colors.cardBackground,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      marginTop: 2,
      paddingBottom: 25,
    },
    imageBackgroundStyle: {
      height: WP(45),
      width: WP(80),
      justifyContent: 'center',
      marginTop: WP(7),
      alignItems: 'center',
      alignSelf: 'center',
    },
    iconContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#1A79FFAF', //'#0000FF7C',
      paddingHorizontal: 16,
      paddingVertical: 4,
    },
    headerTextStyle: {
      textAlign: 'center',
      fontSize: size.h4,
      color: colors.textInverted,
      paddingHorizontal: 8,
      fontWeight: '500',
    },
    buttonStyle: {
      paddingLeft: 0,
      paddingVertical: 15,
      borderColor: 'transparent',
      marginVertical: 10,
      marginHorizontal: 4,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: colors.cardBackground,
    },
    emptyText: {
      color: colors.textInverted,
      fontSize: 20,
    },
    tabBarLabelStyle: {
      fontSize: 16,
      fontWeight: '500',
    },
    tabBarLabelContainer: {
      height: WP(9),
      width: WP(44),
      borderRadius: 4,
      borderColor: '#0076CD1F',
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
    },
    tabBarLabelText: {
      color: colors.text,
      fontSize: 16,
      fontWeight: '600',
    },
  });
};
