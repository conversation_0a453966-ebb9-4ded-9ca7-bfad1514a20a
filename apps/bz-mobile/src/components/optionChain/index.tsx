import { View, Text, StyleSheet, FlatList, Pressable, ScrollView } from 'react-native';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTheme } from '../../theme/themecontext';
import AlertModal from 'react-native-modal';
import OptionChainFilter from './optionChainFilter';
import OptionChainCardContain from './optionChainCardContain';
import { useOptionChain } from '@benzinga/hooks';
import { ExpiryDates, moneynessType, organizeOptionDataReturn } from '@benzinga/data-option-chain';

export interface ExpirationDate {
  date: string;
}
export interface Moneyness {
  type: string;
}

type OptionChainType = {
  symbol?: string;
  optionData?: organizeOptionDataReturn;
  stockPrice?: number;
  title: string;
  companyName: string | undefined;
  handleState: () => boolean;
};

const OptionChainList: React.FC<OptionChainType> = ({
  companyName,
  handleState,
  optionData,
  stockPrice,
  symbol,
  title,
}) => {
  const [expirationDate, setExpirationDate] = useState<ExpiryDates>();
  const [moneynessType, setMoneynessType] = useState<NonNullable<'Near The Money' | 'All' | undefined>>();
  const [modalVisible, setModalVisible] = useState(false);
  const [currentModalData, setCurrentModalData] = useState<'expiration' | 'moneyness'>('expiration');

  const { expiries, filteredData, initialRange, onExpiryChange, onMoneynessChange, onRangeChange, range } =
    useOptionChain({
      optionChainData: (optionData as organizeOptionDataReturn) || {},
      stockPrice: stockPrice || 0,
      symbol: symbol || '',
    });

  const meneynessOptions: moneynessType[] = useMemo(
    () => [
      { label: 'All', value: 'all' },
      { label: 'Near The Money', value: 'near-the-money' },
    ],
    [],
  );
  useEffect(() => {
    if (expiries && expiries.length > 0) {
      const firstObject = expiries[0];
      setExpirationDate(firstObject);
      onExpiryChange(firstObject.value);
      setMoneynessType(meneynessOptions[0].label);
      onMoneynessChange(meneynessOptions[0].value);
    } else {
      console.error('No expiries available');
    }
  }, []);

  const { colors, isDark } = useTheme();
  const openModal = (type: 'expiration' | 'moneyness') => {
    setCurrentModalData(type);
    setModalVisible(true);
  };

  const handleItemPress = useCallback(
    item => {
      if (currentModalData === 'expiration') {
        setExpirationDate(item);
        onExpiryChange(item.value);
      } else {
        setMoneynessType(item.label);
        onMoneynessChange(item.value);
      }
      setModalVisible(false);
    },
    [currentModalData, onExpiryChange, onMoneynessChange],
  );

  const renderModalContent = useMemo(() => {
    const data = currentModalData === 'expiration' ? expiries : meneynessOptions;
    return (
      <FlatList
        data={data}
        keyExtractor={item => item.label}
        renderItem={({ item }) => {
          const isSelected =
            currentModalData === 'expiration' ? expirationDate?.label === item.label : moneynessType === item.label;

          return (
            <Pressable onPress={() => handleItemPress(item)}>
              <Text
                style={{
                  backgroundColor: isSelected ? colors.buttonBlueDefault : colors.background,
                  padding: 9,
                  borderRadius: 5,
                  color: colors.text,
                }}
              >
                {currentModalData === 'expiration' ? item.label : item.label}
              </Text>
              <View style={{ height: 1, backgroundColor: colors.border, marginVertical: 6 }} />
            </Pressable>
          );
        }}
      />
    );
  }, [currentModalData, expiries, meneynessOptions, expirationDate, moneynessType, colors, handleItemPress]);

  const renderTrackMarkComponent = useCallback(
    index => <View key={index} style={[styles.trackMark, { backgroundColor: colors.sliderBlue }]} />,
    [colors],
  );
  return (
    <ScrollView style={styles.mainContainer} contentContainerStyle={{ paddingBottom: 100 }}>
      <View style={styles.container}>
        <Text style={[styles.optionTitle, { color: colors.text, fontWeight: 'bold' }]}>{`${title}, Option Chain`}</Text>
        <Text style={[styles.optionDescription, { color: colors.dimmedText }]}>
          {`Option chain shows key data for ${companyName}'s stock options at various strike prices and expiration dates. Traders use this information to analyze potential trades and assess market sentiment for ${companyName}.`}
        </Text>
        <View style={styles.optionMoneyTag}>
          <Text
            style={{
              backgroundColor: isDark ? colors.backgroundSecondary : 'rgba(63, 131, 248, 0.05)',
              borderLeftWidth: 3,
              borderRadius: 4,
              paddingHorizontal: 8,
              paddingVertical: 1,
              borderColor: isDark ? colors.border : colors.sliderBlue,
              color: colors.text,
            }}
          >
            In The Money
          </Text>
        </View>
      </View>
      <View style={{ height: 0.8, backgroundColor: colors.border, marginVertical: 10 }} />
      <OptionChainFilter
        expirationDate={expirationDate}
        moneynessType={moneynessType}
        setSliderValue={onRangeChange}
        openModal={openModal}
        sliderValue={range}
        renderTrackMarkComponent={renderTrackMarkComponent}
        rangeLimits={initialRange}
        setSwipe={handleState}
      />
      <OptionChainTableData colors={colors} optionChainData={filteredData} isDark={isDark} />

      <AlertModal
        isVisible={modalVisible}
        avoidKeyboard={true}
        hasBackdrop={true}
        style={{ marginVertical: 50 }}
        onBackdropPress={() => setModalVisible(false)}
      >
        <View
          style={{
            backgroundColor: colors.background,
            borderRadius: 10,
            paddingHorizontal: 14,
            paddingVertical: 10,
          }}
        >
          <Text style={{ fontSize: 16, fontWeight: 'bold', textAlign: 'center', color: colors.text }}>
            {currentModalData === 'expiration' ? 'Expiration Date' : 'Moneyness'}
          </Text>
          <View style={{ height: 2, backgroundColor: colors.border, marginVertical: 10 }} />
          {renderModalContent}
        </View>
      </AlertModal>
    </ScrollView>
  );
};

const OptionChainTableData = ({ colors, isDark, optionChainData }) => {
  return (
    <View style={styles.outerContainer}>
      <FlatList
        ListEmptyComponent={() => (
          <View style={{ padding: 20, alignItems: 'center' }}>
            <Text style={{ color: colors.text, fontSize: 16 }}>No OptionChain data available</Text>
          </View>
        )}
        data={optionChainData}
        keyExtractor={item => item.id}
        scrollEnabled={false}
        renderItem={({ item }) => (
          <View
            style={[
              styles.innerContainer,
              {
                //left blue border
                borderRadius: 3,
                borderLeftColor: item.call?.inTheMoney ? colors.sliderBlue : colors.border,
                borderLeftWidth: item.call?.inTheMoney ? 3 : 1,
                borderTopLeftRadius: 3,
                borderBottomLeftRadius: 3,
                //right blue border
                borderRightWidth: item.put?.inTheMoney ? 3 : 1,
                borderRightColor: item.put?.inTheMoney ? colors.sliderBlue : colors.border,
                borderTopRightRadius: 3,
                borderBottomRightRadius: 3,
                borderBottomColor: colors.border,
                borderTopColor: colors.border,
              },
            ]}
          >
            <View
              style={[
                styles.card,
                {
                  paddingHorizontal: 3,
                  width: '43.5%',
                  backgroundColor: item.call?.inTheMoney
                    ? isDark
                      ? colors.backgroundSecondary
                      : 'rgba(63, 131, 248, 0.05)'
                    : colors.background,
                },
              ]}
            >
              {/* item.call */}
              {item.call ? <OptionChainCardContain item={item.call} /> : null}
            </View>
            <View
              style={{
                width: '13%',
                borderRadius: 4,
              }}
            >
              {/* item.call.strike */}
              <Text
                style={{
                  fontSize: 12,
                  margin: 0.5,
                  fontWeight: '600',
                  color: colors.text,
                  textAlign: 'center',
                  borderRadius: 2,
                  padding: 3,
                  backgroundColor:
                    item.put?.inTheMoney || item.call?.inTheMoney
                      ? isDark
                        ? '#345a8f'
                        : '#e1ebfa'
                      : colors.background,
                }}
              >
                Strike
              </Text>
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: 4,
                  borderColor: colors.border,
                  borderWidth: 1,
                  backgroundColor: isDark ? colors.backgroundSecondary : '#e1ebfa',
                }}
              >
                <Text
                  style={{
                    textAlign: 'center',
                    fontSize: 14,
                    color: colors.text,
                    borderRadius: 8,
                    padding: 2,
                    fontWeight: '500',
                  }}
                >
                  {item.call.strike.toFixed(1)}
                </Text>
              </View>
            </View>
            <View
              style={{
                paddingHorizontal: 3,
                paddingVertical: 3,
                width: '43.5%',
                backgroundColor: item.put?.inTheMoney
                  ? isDark
                    ? colors.backgroundSecondary
                    : 'rgba(63, 131, 248, 0.05)'
                  : colors.background,
              }}
            >
              {/* item.put */}
              {item.put ? <OptionChainCardContain item={item.put} /> : null}
            </View>
          </View>
        )}
      />
    </View>
  );
};

export default React.memo(OptionChainList);

export const styles = StyleSheet.create({
  outerContainer: {},
  innerContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    marginBottom: 8,
  },
  card: {
    backgroundColor: '#fff',
    paddingHorizontal: 2,
    paddingVertical: 4,
  },
  mainContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
  },
  container: {},
  optionTitle: {
    fontSize: 18,
    paddingBottom: 6,
  },
  optionDescription: {
    fontSize: 14,
  },
  optionMoneyTag: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 5,
  },
  priceInfoContainer: {},
  divider: {
    height: 1,
    backgroundColor: '#ccc',
    marginVertical: 7,
  },
  row: {
    flexDirection: 'row',
  },
  bidAskContainer: {
    flex: 1,
  },
  trackMark: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },

  sectionContainer: {
    marginBottom: 8,
  },
  // titleText: {
  //   color: colors.text,
  //   fontSize: 13,
  //   fontWeight: '500',
  // },
  // boldText: {
  //   fontWeight: 'bold',
  //   fontSize: 12,
  //   color: colors.text,
  // },
  // dateText: {
  //   color: colors.dimmedText,
  //   fontSize: 12,
  // },
  percentageText: {
    paddingHorizontal: 5,
    borderRadius: 4,
    fontWeight: 'bold',
    fontSize: 12,
    alignSelf: 'flex-start',
  },
  // row: {
  //   flexDirection: 'row',
  //   justifyContent: 'space-between',
  // },
  // bidAskContainer: {
  //   flex: 1,
  //   alignItems: 'center',
  // },
  // divider: {
  //   height: 1,
  //   backgroundColor: colors.divider,
  //   marginVertical: 4,
  // },
});
