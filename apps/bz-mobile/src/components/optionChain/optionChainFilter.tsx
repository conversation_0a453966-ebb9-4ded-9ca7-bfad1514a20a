import { View, Text, TouchableOpacity } from 'react-native';
import React from 'react';
import Icon, { IconTypes } from '../Icon/Icon';
import { Slider } from '@miblanchard/react-native-slider';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native';
import { useTheme } from '../../theme/themecontext';
import { ExpiryDates } from '@benzinga/data-option-chain';

export interface OptionChainFilterProps {
  expirationDate: ExpiryDates | undefined;
  moneynessType: 'All' | 'Near The Money' | undefined;
  openModal: (type: 'expiration' | 'moneyness') => void;
  rangeLimits: [number, number];
  renderTrackMarkComponent?: (index) => React.ReactNode;
  setSliderValue: (min: number, max: number) => void;
  sliderValue: [number, number];
  setSwipe?: any;
}

const OptionChainFilter = ({
  expirationDate,
  moneynessType,
  openModal,
  rangeLimits,
  renderTrackMarkComponent,
  setSliderValue,
  setSwipe,
  sliderValue,
}: OptionChainFilterProps) => {
  const { colors, isDark } = useTheme();
  const step = 5;
  const dots: number[] = [];
  for (let i = rangeLimits[0] + 0.1; i <= rangeLimits[1] + 0.1; i += step) {
    dots.push(i);
  }
  return (
    <SafeAreaView>
      <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
        <View style={{ flex: 1, marginRight: 10 }}>
          <Text style={[styles.optionTitle, { color: isDark ? colors.text : colors.dimmedText, fontSize: 14 }]}>
            Expiration Date :
          </Text>
          <TouchableOpacity
            onPress={() => openModal('expiration')}
            style={{
              backgroundColor: isDark ? colors.backgroundSecondary : 'rgba(63, 131, 248, 0.05)',
              padding: 4,
              borderRadius: 4,
              flexDirection: 'row',
            }}
          >
            <Text
              style={{
                paddingHorizontal: 10,
                flexGrow: 1,
                color: isDark ? colors.text : colors.sliderBlue,
                fontWeight: 'bold',
              }}
            >
              {expirationDate?.label}
            </Text>
            <Icon
              type={IconTypes.MaterialIcons}
              name={'expand-more'}
              size={20}
              color={isDark ? colors.text : colors.sliderBlue}
            />
          </TouchableOpacity>
        </View>
        <View style={{ flex: 1 }}>
          <Text
            style={[
              styles.optionTitle,
              {
                color: isDark ? colors.text : colors.dimmedText,
                fontSize: 14,
              },
            ]}
          >
            Moneyness :
          </Text>
          <TouchableOpacity
            onPress={() => openModal('moneyness')}
            style={{
              backgroundColor: isDark ? colors.backgroundSecondary : 'rgba(63, 131, 248, 0.05)',
              padding: 4,
              borderRadius: 4,
              flexDirection: 'row',
            }}
          >
            <Text
              style={{
                paddingHorizontal: 10,
                flexGrow: 1,
                color: isDark ? colors.text : colors.sliderBlue,
                fontWeight: 'bold',
              }}
            >
              {moneynessType}
            </Text>
            <Icon
              type={IconTypes.MaterialIcons}
              name={'expand-more'}
              size={20}
              color={isDark ? colors.text : colors.sliderBlue}
            />
          </TouchableOpacity>
        </View>
      </View>
      {/* slider Range Filter */}
      <View style={{ height: 0.8, backgroundColor: colors.border, marginVertical: 10 }} />
      <View
        onTouchStart={() => {
          setSwipe(false);
        }}
        onTouchEnd={() => {
          setSwipe(true);
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text
            style={[
              styles.optionTitle,
              { color: isDark ? colors.text : colors.dimmedText, fontSize: 14, marginRight: 50 },
            ]}
          >
            Strike Range :
          </Text>
          {/* rangeLimits[0] */}
          <Text
            style={{
              fontSize: 13,
              color: isDark ? colors.text : colors.sliderBlue,
              fontWeight: 'bold',
              backgroundColor: isDark ? colors.backgroundSecondary : 'rgba(63, 131, 248, 0.05)',
              paddingHorizontal: 3,
              paddingVertical: 2,
              borderRadius: 5,
            }}
          >
            {sliderValue[0].toFixed(1)}
          </Text>
          <Text style={{ fontSize: 15, paddingHorizontal: 10, color: isDark ? colors.text : colors.sliderBlue }}>
            -
          </Text>
          {/* rangeLimits[1] */}
          <Text
            style={{
              fontSize: 13,
              color: isDark ? colors.text : colors.sliderBlue,
              fontWeight: 'bold',
              backgroundColor: isDark ? colors.backgroundSecondary : 'rgba(63, 131, 248, 0.05)',
              paddingHorizontal: 3,
              paddingVertical: 2,
              borderRadius: 5,
            }}
          >
            {sliderValue[1].toFixed(1)}
          </Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
          <Text style={{ fontSize: 13, color: colors.text, fontWeight: 'bold', paddingRight: 6 }}>
            {rangeLimits[0].toFixed(1) || 0}
          </Text>

          <Slider
            onSlidingStart={() => {
              setSwipe(false);
            }}
            onSlidingComplete={() => {
              setSwipe(true);
            }}
            containerStyle={{ flex: 1, height: 50 }}
            animateTransitions
            maximumTrackTintColor={colors.sliderBlue}
            minimumTrackTintColor={colors.sliderBlue}
            maximumValue={rangeLimits[1] || 500}
            minimumValue={rangeLimits[0] || 0}
            step={5}
            value={sliderValue}
            onValueChange={value => {
              setSliderValue(value[0], value[1]);
            }}
            thumbStyle={{
              width: 16,
              height: 16,
              borderRadius: 10,
            }}
            thumbTintColor={colors.sliderBlue}
            trackStyle={{
              height: 6,
              backgroundColor: colors.backgroundSecondary,
              borderRadius: 4,
              borderColor: colors.border,
            }}
            trackMarks={dots}
            renderTrackMarkComponent={renderTrackMarkComponent}
          />

          <Text style={{ fontSize: 13, color: colors.text, fontWeight: 'bold', paddingLeft: 6 }}>
            {rangeLimits?.[1].toFixed(1) || 500}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default OptionChainFilter;

export const styles = StyleSheet.create({
  optionTitle: {
    fontSize: 18,
    paddingBottom: 6,
  },
});
