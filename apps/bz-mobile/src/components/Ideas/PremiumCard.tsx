import React, { useEffect, useState } from 'react';
import { ImageBackground, LayoutAnimation, Platform, Pressable, StyleSheet, Text, UIManager, View } from 'react-native';
import { useTheme } from '../../theme/themecontext';
import { PositionTag } from './PositionTag';
import { WP, size } from '../../services';
import QuoteTile from '../Quote/QuoteTile';
import { StackNavigationProp } from '@react-navigation/stack';
import { TradeIdeasNavigationStackParamList } from '../../navigation/TradeIdeasNavigationStack';
import { AppNavigationStackParamList } from '../../app/App';
import { colors as textColors } from '../../screens/Ideas/colors';
import Icon, { IconTypes } from '../Icon/Icon';
import { PostButton } from '../Buttons/PostButton';
import AlertModal from 'react-native-modal';
import { WebView } from 'react-native-webview';
import moment from 'moment';
import { useAppSelector } from '../../redux/hooks';
import CustomPressable from '../CustomPressable';

interface PremiumCardProps {
  idea: any;
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
}

interface PremiumCardHeaderProps {
  idea: any;
  cardExpanded: boolean;
  navigation: StackNavigationProp<TradeIdeasNavigationStackParamList & AppNavigationStackParamList>;
}

const PremiumCard = ({ idea, navigation }: PremiumCardProps) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const hasPremium = useAppSelector(state => state.idea.hasPremiumStatus);
  const [cardExpanded, setCardExpanded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const expirationDate = idea.trades?.length
    ? idea.trades[0].legs.length
      ? idea.trades[0].legs[0].expiration
      : ''
    : '';
  const strike = idea.trades?.length ? (idea.trades[0]?.legs?.length ? idea.trades[0]?.legs[0]?.strike : '') : '';
  const keepCardExpanded = expirationDate.length < 1 || strike.length < 1;

  useEffect(() => {
    if (Platform.OS === 'android') {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
    if (keepCardExpanded) {
      setCardExpanded(true);
    }
  }, []);

  const expirationStrike = () => {
    return (
      <View style={{ marginBottom: cardExpanded ? 0 : 9 }}>
        <View style={[styles.textContainer]}>
          <Text style={styles.grayText}>Expiration</Text>
          <Text style={styles.grayText}>Strike</Text>
        </View>
        <View style={[styles.textContainer]}>
          <Text style={styles.boldText}>
            {expirationDate ? moment(expirationDate).format('MMM DD') : '-'}
            <Text style={[styles.grayText, { fontWeight: 'bold' }]}>
              {' '}
              {expirationDate ? moment(expirationDate).format('YYYY') : '-'}
            </Text>
          </Text>
          <Text style={styles.boldText}>{strike}</Text>
        </View>
      </View>
    );
  };

  const dividerComponent = () => {
    return (
      <View
        style={{
          marginTop: 16,
          height: 1,
          backgroundColor: colors.border,
        }}
      />
    );
  };

  const renderPercentChange = (percentChange: string) => {
    const background = Number(percentChange) < 0 ? 'rgba(255, 64, 80, 0.3)' : 'rgba(14, 159, 110, 0.3)';
    const textColor = Number(percentChange) < 0 ? 'rgba(255, 64, 80, 1)' : 'rgba(14, 159, 110, 1)';
    return (
      <View
        style={[
          styles.percentChangeContainer,
          {
            backgroundColor: background,
            paddingHorizontal: percentChange ? 5 : 0,
          },
        ]}
      >
        <Text
          style={[
            styles.percentChangeText,
            {
              color: textColor,
            },
          ]}
        >
          {percentChange}
          {percentChange && '%'}
        </Text>
      </View>
    );
  };

  const stopLossComponent = () => {
    return (
      <>
        <View style={[styles.textContainer]}>
          <Text style={styles.grayText}>Stop Loss</Text>
          <Text style={styles.grayText}>Closing price</Text>
        </View>
        <View style={[styles.textContainer]}>
          <View style={{ flexDirection: 'row', alignSelf: 'center' }}>
            <Text style={[styles.boldText, { fontSize: size.medium }]}>
              ${idea.trades.length && idea.trades[0].stop_loss ? idea.trades[0].stop_loss : '-'}
            </Text>
            {renderPercentChange('')}
          </View>
          <View style={{ alignSelf: 'center' }}>
            <Icon type={IconTypes.FontAwesome} name="long-arrow-right" color={textColors.grey1} size={18} />
          </View>
          <View style={{ flexDirection: 'row', alignSelf: 'center' }}>
            <Text style={[styles.boldText, { fontSize: size.medium }]}>
              ${(idea.trades.length && idea.trades[0]?.closing_price) ?? '-'}
            </Text>
            {renderPercentChange((idea.trades.length && idea.trades[0]?.profit_loss_perc) ?? '')}
          </View>
        </View>
      </>
    );
  };

  const _footer = () => {
    return hasPremium && idea.blog_id ? (
      <CustomPressable
        style={[styles.footerContainer]}
        onPress={() => {
          setIsModalVisible(true);
        }}
      >
        <Text style={[styles.boldText]}>READ REPORT</Text>
      </CustomPressable>
    ) : null;
  };

  const renderNoPremiumCard = () => {
    const { isDark } = useTheme();
    return (
      <View style={styles.noPremiumCardContainer}>
        <ImageBackground
          source={
            isDark ? require('../../assets/images/blur_idea.png') : require('../../assets/images/blur_idea_light.png')
          }
          style={{ height: 148, width: '100%', justifyContent: 'center', alignItems: 'center' }}
          resizeMode="cover"
        >
          <View style={{ padding: 6, backgroundColor: '#373F4966', borderRadius: 15, marginTop: 8 }}>
            <Icon type={IconTypes.Feather} name="lock" size={16} color={colors.text} />
          </View>
          <Text style={{ fontSize: 16, fontWeight: '700', color: colors.text, marginVertical: 10 }}>
            This idea is for subscribers only
          </Text>
          <PostButton
            title="Subscribe Now"
            style={{
              backgroundColor: '#0076CD29',
              borderWidth: 0,
              borderRadius: 15,
              paddingHorizontal: 22,
              paddingVertical: 6,
            }}
            textStyle={{ fontWeight: '700', fontSize: 14, color: colors.text }}
            containerStyle={{ paddingLeft: 0 }}
            onPress={() => {
              navigation.navigate('MySubscription');
            }}
          />
        </ImageBackground>
      </View>
    );
  };

  const hideModal = () => {
    setIsModalVisible(false);
  };

  return hasPremium ? (
    <View style={[styles.container]}>
      <Pressable
        // style={({ pressed }) => [{ opacity: pressed ? 0.5 : 1 }]}
        onPress={() => {
          LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          !keepCardExpanded && setCardExpanded(!cardExpanded);
        }}
        disabled={keepCardExpanded}
      >
        {<PremiumCardHeader idea={idea} cardExpanded={cardExpanded} navigation={navigation} />}
        {!keepCardExpanded && expirationStrike()}
        {cardExpanded && (
          <>
            {dividerComponent()}
            {idea.entry_price.length > 0 ? (
              <Text style={[styles.boldText, { marginTop: 10, fontSize: size.medium }]}>
                {isNaN(idea.entry_price.slice(0, 1))
                  ? `${idea.entry_price.slice(0, 1) + idea.entry_price.slice(1)} `
                  : `$${idea.entry_price} `}
                Entry Price
              </Text>
            ) : (
              <Text style={[styles.boldText, { marginTop: 10, fontSize: size.medium }]}>
                {idea.current_price} Current Price
              </Text>
            )}
            {dividerComponent()}
            {idea.trades?.length && (idea.trades[0]?.stop_loss || idea.trades[0]?.closing_price)
              ? stopLossComponent()
              : null}
            {idea.trades?.length && (idea.trades[0]?.stop_loss || idea.trades[0]?.closing_price)
              ? dividerComponent()
              : null}

            <View style={[styles.textContainer, { marginBottom: 5 }]}>
              <Text style={styles.grayText}>Opened & Closed</Text>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                }}
              >
                <Text style={[styles.boldText, { marginRight: 8 }]}>
                  {idea.open ? moment(idea.open).format('MMM DD') : '-'}
                  {idea.close ? moment(idea.close).format('MMM DD') : ''}
                </Text>
                <Icon type={IconTypes.FontAwesome} name="long-arrow-right" color={textColors.grey1} size={18} />
                <Text style={[styles.boldText, { marginLeft: 8 }]}>
                  {idea.close ? moment(idea.close).format('MMM DD') : ''}
                </Text>
                <Text style={[styles.grayText, { marginLeft: 8, fontWeight: 'bold', alignSelf: 'center' }]}>
                  {idea.close ? moment(idea.close).format('YYYY') : moment(idea.open ?? new Date()).format('YYYY')}
                </Text>
              </View>
            </View>
            {_footer()}
          </>
        )}
      </Pressable>
      <AlertModal
        isVisible={isModalVisible}
        hasBackdrop={true}
        onBackdropPress={hideModal}
        style={{ margin: 0 }}
        onBackButtonPress={hideModal}
        animationIn="slideInUp"
        animationInTiming={300}
        animationOut="slideOutDown"
        animationOutTiming={300}
      >
        <View style={styles.modalChildrenContainer}>
          <CustomPressable style={styles.closeModalIconContainer} onPress={hideModal}>
            <Icon type={IconTypes.AntDesign} name="closecircleo" size={25} color={'white'} />
          </CustomPressable>
          <WebView
            source={{ uri: `https://www.benzinga.com/research/${idea.package.product_id}/blog/post/${idea.blog_id}` }}
            sharedCookiesEnabled={true}
          />
        </View>
      </AlertModal>
    </View>
  ) : (
    <>
      {renderNoPremiumCard()}
      <View style={styles.noPremiumFooterContainer}>{_footer()}</View>
    </>
  );
};

export const PremiumCardHeader = ({ cardExpanded, idea, navigation }: PremiumCardHeaderProps) => {
  const { colors } = useTheme();
  const styles = withColors(colors);
  const positionTag = (idea.trades?.length && idea.trades[0]?.transaction_type?.split(' ', 1)[0]?.toLowerCase()) ?? '';
  const position = positionTag ? (positionTag === 'long' || positionTag === 'bull' ? 'LONG' : 'SHORT') : '';

  const onQuotePress = (symbol: string) => {
    console.log('PRESSED QUOTE: ', symbol);

    global.Analytics.event('Navigation', 'Show Quote', `Post(${idea.id})`);

    const _symbol = symbol.split('/USD')[0];
    if (symbol.includes('/USD')) {
      navigation.navigate('CryptoQuote', {
        symbol: _symbol,
      });
    } else {
      navigation.navigate('Quote', {
        symbol: _symbol,
      });
    }
  };

  const _renderTags = () => {
    return (
      <View style={styles.openContainer}>
        <View style={styles.premiumContainer}>
          <Text style={styles.premiumTextStyle}>PREMIUM</Text>
        </View>
        <Text style={styles.openTextStyle}>{idea.status}</Text>
      </View>
    );
  };

  return (
    <View style={{ flexDirection: 'row', flex: 1 }}>
      {position ? <PositionTag position={position} containerStyle={{ marginRight: 8 }} /> : <View />}
      <QuoteTile
        onPress={onQuotePress}
        style={[styles.quoteTitleStyle, { marginLeft: 0 }]}
        symbol={idea.tickers ? idea.tickers[0] : idea.symbol}
        hideMarketStatus={true}
        showPercentChange={false}
        subscriptedNameStyle={{ color: colors.text }}
      />
      <View style={styles.companyNameContainer}>
        {(idea.companyName || idea.name) && cardExpanded && (
          <Text style={[styles.companyName]} numberOfLines={1}>
            {idea.companyName && idea.companyName}
            {idea.name && idea.name}
          </Text>
        )}
      </View>
      {_renderTags()}
    </View>
  );
};

const withColors = (colors: Record<string, string>) => {
  return StyleSheet.create({
    container: {
      backgroundColor: colors.newCard,
      // marginVertical: 10,
      borderRadius: 5,
      paddingHorizontal: WP('3'),
      paddingTop: 8,
      marginHorizontal: 15,
    },
    quoteTitleStyle: {
      paddingVertical: 2,
      fontSize: 16,
      marginLeft: 6,
    },
    companyName: {
      fontSize: 12,
      color: colors.dimmedText,
      alignSelf: 'flex-start',
      textAlign: 'center',
    },
    companyNameContainer: {
      flex: 1,
      justifyContent: 'center',
    },
    marginLeft: {
      marginLeft: WP('3'),
    },
    grayText: {
      color: textColors.grey1,
      fontSize: size.xxsmall,
    },
    boldText: {
      color: colors.text,
      fontWeight: '700',
      fontSize: size.small,
    },
    textContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    readReportTextStyle: {
      flexDirection: 'row-reverse',
      flex: 1,
      marginHorizontal: 8,
      alignSelf: 'center',
    },
    premiumContainer: {
      backgroundColor: '#EFA12729',
      alignSelf: 'center',
      marginHorizontal: 8,
      paddingHorizontal: 4,
      borderRadius: 3,
    },
    premiumTextStyle: {
      color: '#EFA127',
      alignSelf: 'center',
      fontWeight: '700',
      fontSize: size.tiny,
      paddingVertical: 3,
    },
    footerContainer: {
      backgroundColor: colors.grayishBlue,
      height: 40,
      marginHorizontal: -WP('3'),
      borderBottomLeftRadius: 5,
      borderBottomRightRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 10,
    },
    avatarImage: {
      height: 24,
      width: 24,
      borderRadius: 12,
      alignSelf: 'center',
      marginLeft: 15,
    },
    openTextStyle: {
      textAlign: 'center',
      borderWidth: 0.6,
      borderColor: colors.border,
      paddingHorizontal: 6,
      color: colors.text,
      fontSize: size.xxsmall,
      borderRadius: 3,
      paddingVertical: 1,
    },
    openContainer: {
      flexDirection: 'row',
      alignSelf: 'center',
      justifyContent: 'flex-end',
    },
    percentChangeContainer: {
      alignSelf: 'center',
      marginLeft: 5,
      paddingVertical: 2,
      borderRadius: 3,
    },
    percentChangeText: {
      textAlign: 'center',
      fontWeight: 'bold',
      fontSize: size.xxsmall,
    },
    noPremiumCardContainer: {
      paddingHorizontal: 0,
      paddingTop: 0,
      backgroundColor: colors.newCard,
      marginHorizontal: 15,
    },
    noPremiumFooterContainer: {
      paddingTop: 0,
      paddingHorizontal: WP('3'),
      marginHorizontal: 15,
      backgroundColor: colors.newCard,
    },
    closeModalIconContainer: {
      flexDirection: 'row-reverse',
      marginHorizontal: 15,
      marginBottom: 4,
    },
    modalChildrenContainer: {
      bottom: 0,
      position: 'absolute',
      width: '100%',
      height: '90%',
    },
  });
};
export default PremiumCard;
