.bz-theme {
  .bz-logo {
    margin: 5px;
    background-image: url("/assets/static/img/favicon-white-32x32.png");
    background-repeat: no-repeat;
    background-size: 100% auto;
    width: 32px;
    height: 32px;
  }

  .bp4-navbar {
    background-color: $primary-color;
    color: $secondary-color;
    overflow: hidden;

    a {
      color: inherit;
      text-decoration: none;
    }

    .bp4-button.bp4-minimal, .bp4-button .bp4-icon {
      color: inherit;
    }

    .bp4-button:not([class*="bp4-intent-"])[class*="bp4-icon-"]::before {
      color: $secondary-color;
    }

    .pt-spinner.bz-spinner {
      .pt-spinner-head {
        stroke: $secondary-color;
      }
      .pt-spinner-track {
        stroke: lightgray;
      }
    }

    .pt-link {
      padding: 10px;
    }
  }
}
