.grid-view{
  height: 75vh;
  width: 100%;
}

.ag-theme-balham .ag-ltr .ag-cell {
  border-right: 1px solid #CDD2D6 !important;
}

.conference{
  width: 98% !important;
}

.grid-cell-button--wrapper {
  display: flex;
  margin-top: -4px;
}

.accordion-view {
  height: 90%;
  width: 100%;
}

.calendar-cell.align-center {
  text-align: center;
}

.calendar-cell.align-center.update-history {
  left: 48px;
  bottom: 4px;
}

.overlay-size {
  width: 300px;
  height: 200px;
}

.calendar-cell {
  color: #7F8A93;
  display: inline-box;
  display: -webkit-inline-box;
  height: 22px;
  margin-left: 1px;
  margin-top: 1px;
  padding: 0 0;
  position: relative;

  .bp4-button {
    min-width: 25px;
  }

}

.bp4-button {
  padding: 0px 10px;
}

.downshift-parent-div {
  border: 1px solid black;
  height: 25px;
  margin: auto;
  width: 100%;
}

.downshift-child-div {
  height: 100%;
  margin: auto;
  position: relative;
  width: 100%;
  align-items: center;
  padding: 4px;
}

.downshift-input {
    border: none;
    flex: 1px;
    height: 16px !important;
    min-width: 100px;
    outline: none;
    padding-bottom: 0;
    padding-top: 0;
    height: inherit;
    width: 100%;
}

.downshift-ul {
  background-color: white;
  border-color: #378ED3;
  border-width: 0px 1px 0px 1px;
  border-style: solid;
  box-shadow: 0 2px 3px 0 rgba(34,36,38,.15);
  line-height: 25px;
  margin-top: -4px;
  margin-bottom: 35px;
  margin-left: -3px;
  max-height: 20rem;
  min-width: 100%;
  outline: 0;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 0;
  position: absolute;
  transition: opacity .1s ease;
  width: max-content;
}

.downshift-list {
  border-top: 1px solid #378ED3
}

.downshift-list:last-child {
  border-bottom: 1px solid #378ED3;
}

.error-tooltip-icon {
  padding-bottom: 3px;
}

.ag {
  &-cell {
    padding-left: 6px !important;
    padding-right: 6px !important;
    &-inline {
      &-editing {
        padding: 0 !important;
        height: 25px !important;
      }
    }
    &-edit {
      &-input {
        select {
          height: 100% !important;
        }
      }
    }
  }
  &-popup {
    &-editor {
      border: none !important;
    }
  }

  &-paging {
    &-button {
     &:not(:disabled) {
        opacity: 1 !important;
      }
    }
  }

  &-row {
    &-odd {
      background-color: #F1F2F4 !important;
    }
    &-hover {
      background-color: #DFDFDF !important;
    }
    &-selected {
      border-color: rgba(57,166,255, 0.1) !important;
      background-color: rgba(57,166,255, 0.3) !important;
    }
  }
}

.revision-btn {
	margin: 10px;
}

.collapse-pane {
  position: relative;
  z-index: 4;
}

.ag-theme-balham .ag-row:not(.ag-row-first) {
  border-width: none !important;
}

.grid-container {
  height: 100%;
  left: 20%;
  top: 24%;
  width: 35%;
}

.grid-size {
  width: 160%;
  height: 60%;
}

div .ag-theme-balham .ag-cell {
  border-right: 1px solid #CDD2D6;
}

.default-cell{
  text-align: left;
}

.numeric-cell{
  text-align: right;
  margin-right: 10px;
}

.bold-text {
  font-weight: bolder;
}

.earning,
.economic,
.guidance,
.rating {
  .ag {
    &-header {
      &.ag-pivot-off {
        height: 40px !important;
      }
      &-row {
        height: 40px !important;
      }
      &-cell {
        &.split-header {
          line-height: 20px;
        }
        &-label {
          .ag-header-cell-text {
            white-space: pre !important;
          }
        }
      }
    }
  }
  ::after {
    height: 23px !important;
  }
}

.grid-cell-renderer {
  margin-right: 10px;
}

.user-positions-list {
  margin: 0px;
  padding-left: 20px;
}

.ag-theme-balham .ag-ltr .ag-cell-focus {
  border: 1px solid #0091EA !important;
  outline: initial !important;
}