.drawer {
	position: fixed;
	height: 300px;
	bottom: -300px;
	left: 0;
	right: 0;
	transition: bottom 0.5s ease;
  background-color:white;

	&-open {
		bottom: 0;
	}

  &-padding {
		font-size: 0;
		line-height: 0;
		height: 30px;
		transition: height 0.2s ease;

		&-open {
			height: 300px;
		}
	}

	&-header {
    background-color: $primary-color;
    color: $secondary-color;
		cursor: pointer;
		height: 30px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
		margin-top: -30px;
    padding-left: 15px;
    padding-right: 15px;
	}

	&-content {
    padding: 0 15px 15px 15px;
		height: 100%;
	}

  .bp4-button.bp4-minimal, .bp4-button .bp4-icon {
    color: inherit;
  }

  .bp4-button:not([class*="bp4-intent-"])[class*="bp4-icon-"]::before {
    color: $secondary-color;
  }
}

.bp4-button .bp4-icon, .bp4-button .bp4-icon-standard, .bp4-button .bp4-icon-large {
	color: inherit;
}

.drawer-content {
  .bz-alert {
	  margin-bottom: 15px;
  }

  .bz-grid-table {
	  min-height: 220px;
  }
}

.drawer-view {
	display: flex;
	height: 100%;
	padding-bottom: 2px;
}

.comments {
	background-color: #fff;
	border: 1px solid #BCBEC0;
	float: right;
	height: 90%;
	width: 25%;
}

.messages {
	border-bottom: 1px solid #C6CCCD;
	height: 80%;
	margin-bottom: 7px;
	overflow: auto;
	position: relative;
	width: 100%;
}
.no-messages {
	height: 100%;
	text-align: center;
	width: 100%;
}

span.text {
  color: #7F8A93;
  display: inline-block;
  vertical-align: middle;
}

.post-block {
	float: left;
	height: 20%;
	width: 100%;
}

.post-wrapper {
	padding-right: 12px;
	padding-left: 7px;
	height: 75%;
}

.post-wrapper textarea {
	width: 100%;
	height: 100%;
	resize: none;
}

.message {
	margin-bottom: 1em;
	overflow-wrap: break-word;
}

.messages .info {
	color: #7F8A93;
	padding: 7px;
	background-color: #F1F2F4;
	border-top: 1px solid #C6CCCD;
}

.messages-wrapper {
	padding: 5px;
}
