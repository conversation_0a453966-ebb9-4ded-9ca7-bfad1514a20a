$primary-color: #378ED3;
$secondary-color: #fff;
$danger-color: #DB3737;
$select-cell-cursor: text;

html, body {
  margin: 0;
  height: 100%;
}

#init, #site {
  height: 100%;
}

.container {
  padding: 15px 15px 0 15px;
  height: 70%;
}

.bz-calendar {
  height: 100%;

  .bz-grid-table {
    min-height: 400px;
  }

}

.bp-table-container {
  .bp-table-column-name {
    font-size: 12px;
  }

  .bp-table-cell {
    font-size: 11px;
  }
}

.bp-table-selection-enabled {
  .pt-editable-text::before,
  .pt-editable-content {
    cursor: $select-cell-cursor !important;
  }
}

.bz-grid-hotkeys {
  display: flex;
  justify-content: center;
  font-size: 8px;
  flex-wrap: wrap;

  .bp4-key-combo {
    display: inline;
    margin-left: 5px;
    margin-right: 5px;
  }

  .bp4-key {
    font-size: 8px;
    min-width: 15px;
    height: 15px;
    padding: 3px 5px;
    line-height: 10px;
    margin: 0 0 5px;
  }

  .bp4-key-combo .bp4-key:not(:last-child) {
    margin-right: 5px;
  }
}

.bz-grid-paginator {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bz-grid-tooltip {
  max-width: 180px;
}

.bz-autocomplete-menu {
  min-width: 115px;
  max-width: 115px;
  max-height: 300px;
  overflow-y: auto;
}

.bz-autocomplete-menu-item {
  font-size: 12px !important;
  padding: 2px;
}

.bz-autocomplete-menu-item:hover {
  background-color: #ced9e0;
}

.bz-indicator {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .bz-indicator-icon {
    top: 0px;
    font-family: -apple-system, "BlinkMacSystemFont", "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Open Sans", "Helvetica Neue", sans-serif, "Icons16";
    font-size: 10px;
    right: 5px;
    position: absolute;
    z-index: 1000;
    cursor: pointer;
  }

  .bp-table-cell {
    width: 100%;
  }

  .bz-indicator-icon.disabled {
    display: none;
  }
}

.bz-table-invalid-region {
  background-color: rgba(224,69,74,.1);
  border: 1px solid #E0454A;
}

.bz-table-draft-region {
  background-color: rgba(255,173,74,.1);
  border: 1px solid #FFAD4A;
}

.bz-table-revision-current-region {
  background-color: rgba(80, 183, 141,.1);
  border: 1px solid #0f9960;
}

.bz-table-revision-difference-region {
  background-color: rgba(242, 227, 208,.5);
  border: 1px solid #f2e3d0;
}

.bz-grid-command-cell-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.bz-command-cell {
  padding: 0px !important;
}

.container-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.bz-login {
  min-width: 300px;

  .bz-logo {
    margin: 5px 0;
    background-image: url("/assets/static/img/android-chrome-512x512.png");
    background-repeat: no-repeat;
    background-size: 100% auto;
    width: 300px;
    height: 300px;
  }

  .pt-callout {
    margin-top: 5px;
    max-width: 300px;
  }
}

.bz-daterange-dropdown {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  padding: 7px;

  .pt-datepicker {
    border-bottom: 1px solid rgba(16, 22, 26, 0.15);
    margin-bottom: 7px;
  }
}

.bp4-dialog-footer {
  .bp4-button {
    min-width: 100px;
  }
}

.pt-link::before {
  margin-right: 7px;
}

.poster {
  display: flex;
  flex-direction: column;
  .bp4-tabs {
    border-bottom: 1px solid #C6CCCD;
    padding: 0 20px;
  }

  .bp4-tab-list {
    flex-flow: row wrap;
    width: 78%;

    .bp4-tab-indicator-wrapper {
      display: none;
    }

    .bp4-tab {
      border-radius: 4px 4px 0 0;
      float: left;
      background-color: #F1F2F4;
      width: 200px;
      border: 1px solid #ccc;
      border-bottom: none;
      padding: 10px;
      cursor: pointer;
      position: relative;
      margin: 0 2px -1px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 16px;
      outline: none;
      &[aria-selected=true] {
        background-color: #fff;
        border-bottom: 1px solid #fff;
      }
    }
  }
}

.css-1rhbuit-multiValue {
  padding-left: 5px;
}

.scrollable-div {
  height: 300px; /* Adjust the height as needed */
  overflow-y: auto; /* Enable vertical scrolling */
  border: 1px solid #ccc; /* Optional: add a border for better visual appearance */
  padding: 10px; /* Optional: add padding for better spacing */
  background-color: #f9f9f9; /* Optional: add a background color */
}

.bz-postbox {
  display: flex;
  flex-direction: column;

  .bz-form {
    display: flex;
    padding: 0 20px;
    flex-direction: row;
    flex-wrap: wrap;

    .row {
      margin-bottom: 10px;
    }

    .edit {
      margin-bottom: 10px;
      margin-top: 40px;
      margin-right: 40px;
    }

    .no-resize {
      resize: none;
    }
  }

  .pt-button.right {
    align-self: flex-end;
  }

  .pt-button.left {
    align-self: flex-start
  }

  .bz-control-group {
     display: flex;
     justify-content: center;
     align-content: center;
  }

  .bz-form-left {
    margin-top: 5px;
    width: 70%;
  }

  .bz-edit-form-left {
    margin-top: 5px;
    width: calc(50% - 30px);
  }

  .bz-edit-text-style{
    font-size: 14px;
    font-weight: 600;
    color: #1c2127;
  }

  .bz-edit-form-right {
    width: calc(50% - 30px);
    margin-left: 20px;
    margin-top: 5px;
  }

  .bz-form-right {
    width: calc(30% - 20px);
    margin-left: 20px;
    margin-top: 5px;
  }

  .bz-button-post {
    width: 100%;
  }

  hr {
    margin: 20px 0;
    border: none;
    border-bottom: 1px solid rgba(16, 22, 26, 0.15);
  }
}

.bz-editor-button {
  margin-top: 10px;
}

.bz-editor-stats {
   align-content: flex-start !important;
   justify-content: flex-start !important;

   span {
     margin-right: 5px;
   }
}

textarea.bz-editor {
  height: 400px;
  width: 100%;
}

.bz-dialog {
  width: auto !important;
  overflow: hidden;
  overflow-y: auto;
}

.page-header {
  border-bottom: 1px solid rgb(189, 195, 199);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 0.5em;
  padding-bottom: 0.3em;
}

.bp4-overlay-backdrop.warning-dialog {
  opacity: 0.5;
}

.new-row-button {
  min-width: 30px !important;
  min-height: 30px !important;
  &-container {
    margin-right: 5px;
  }
}

.tag-select {
 width: 100%;
}

.searchbar-button-container {
  display: flex;
}

.posting-portal {
  .bp4 {
    &-overlay {
      &-open {
        background-color: rgba(16, 22, 26, 0.7);
      }
      &-backdrop {
        display: none;
      }
    }
  }
}

.posting-portal {
  .pt-overlay-backdrop {
    display: none;
  }
  .pt-overlay-open {
    background-color: rgba(16, 22, 26, 0.7);
  }

}

.bp4-navbar {
  min-width: 830px;
}

.ck-editor__editable {
  min-height: 400px !important;
}

.ck.ck-editor {
  overflow-y: auto;
  height: 75% !important;
}


@media (max-width: 700px) {
  .bz-postbox {
    .bz-form-left {
      width: 100%;
    }
    .bz-form-right {
      width: 100%;
    }
  }
}