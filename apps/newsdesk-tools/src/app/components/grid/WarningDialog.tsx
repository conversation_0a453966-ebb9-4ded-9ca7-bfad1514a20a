import { Button, Dialog, Intent } from '@blueprintjs/core';
import { isEmpty, isNil } from 'ramda';
import { FunctionComponent } from 'react';
import * as React from 'react';
import { connect } from 'react-redux';
import { Dispatch } from 'redux';

import { removeWarningData } from '../../actions/grid';
import { RootState } from '../../redux/types';
import { CollectionId } from '../collections/collectionEntities';
import { selectCollectionById } from '../collections/collectionSelectors';
import { sanitizeHTML } from '@benzinga/frontend-utils';

interface DispatchableActions {
  removeWarning(): void;
}

interface OwnProps {
  collectionId: CollectionId;
}

interface ReduxState {
  warning: string;
}

type Props = DispatchableActions & OwnProps & ReduxState;

const WarningDialog: FunctionComponent<Props> = ({ removeWarning, warning: message }: Props) => {
  const onClose = () => {
    removeWarning();
  };

  const isOpen = !(isEmpty(message) || isNil(message));

  const renderContent = <div className="bp4-dialog-body" dangerouslySetInnerHTML={{ __html: sanitizeHTML(message) }} />;

  const renderButton = (
    <div className="bp4-dialog-footer">
      <div className="bp4-dialog-footer-actions">
        <Button intent={Intent.NONE} onClick={onClose} text="Ok" />
      </div>
    </div>
  );

  return (
    <Dialog
      backdropClassName="warning-dialog"
      canEscapeKeyClose={false}
      canOutsideClickClose={false}
      className="bz-alert-dialog"
      icon="ban-circle"
      isCloseButtonShown
      isOpen={isOpen}
      onClose={onClose}
      title="Warning"
    >
      {renderContent}
      {renderButton}
    </Dialog>
  );
};

const mapStateToProps = (state: RootState, { collectionId }: OwnProps): ReduxState => ({
  warning: selectCollectionById(state, { collectionId }).warning,
});

const mapDispatchToProps = (dispatch: Dispatch, { collectionId }: OwnProps): DispatchableActions => ({
  removeWarning: () => dispatch(removeWarningData(collectionId)),
});

export default connect<ReduxState, DispatchableActions, OwnProps>(mapStateToProps, mapDispatchToProps)(WarningDialog);
