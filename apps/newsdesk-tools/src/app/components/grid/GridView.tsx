import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-balham.css';

import * as agGridEnterprise from '@ag-grid-enterprise/core';

import { But<PERSON>, Dialog, Intent } from '@blueprintjs/core';
import {
  ColDef,
  Column,
  GridApi,
  CellPosition,
  GridOptions,
  TabToNextCellParams,
  CellFocusedEvent,
} from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';
import classNames from 'classnames';
import { equals, find, findIndex, flatten, isEmpty, isNil, map, pipe } from 'ramda';
import { isNull, isUndefined } from 'ramda-adjunct';
import * as React from 'react';
import { Component, createRef, RefObject } from 'react';

import { GridOperation, Row, UsersPosition } from '../../reducers/entities/collectionEntities';
import { expand, FIELDS, isFieldToBeConfirmed } from '../../utils/earningsUtils';
import { Events } from '../../utils/eventEntities';
import { buildUserPositions } from '../../utils/RTNotificationsUtils';
import { CollectionId, PermissionsByGroupId } from '../collections/collectionEntities';
import { selectCollectionName } from '../collections/collectionSelectors';
import { HotKeys } from '../postbox/entities';
import { checkRevisionOrNotification } from '../RTNotificationUtils';
import { columnDefinitionModifier, columnTypes, exportToCSV, rowStyle, sideBar } from './agGridUtils';
import GridHotkeys from './GridHotkeys';
import { getInputCellDOMNode } from './html';
import WarningDialog from './WarningDialog';
import moment from 'moment';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { numberFormatter } from '../../generators/utils';
import { RichSelectModule } from '@ag-grid-enterprise/rich-select';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getTranscriptStatus } from '../../services/transcript';

ModuleRegistry.registerModules([RichSelectModule]);

interface PopUpConfirmationState {
  doubleConfirm?: boolean;
  field: string;
  isDialogOpen: boolean;
  message: string;
  oldValue: string;
  rowId: string;
  setOldValue: boolean;
  value: string | boolean;
}

interface Props {
  collectionId: CollectionId;
  columnDefinitions: ColDef[];
  confirmMessage?: string;
  confirmRowId?: string;
  dataSource: Row[];
  defaultColumnDefinition: ColDef;
  defaultEditingColumn?: string;
  gridOperation?: GridOperation;
  isNotification?: boolean;
  isRevision?: boolean;
  loading?: boolean;
  query?: string;
  skippableFields?: string[];
  updatedField?: string;
  usersPosition?: UsersPosition;
  permissionSet: PermissionsByGroupId;
  sessionKey: string;
  loadRevisions?(row: number): void;
  notifyCellBlurred?(): void;
  notifyCellFocused?(id: string, field: string): void;
  onCancel?(): void;
  onConfirmCell?(modifiedRow: Row, _: any, dataKey: string, isConfirm?: boolean): void;
  onPostStory?(modifiedRow: Row): void;
  onRowCreate?(): void;
  onRowPublish?(isConfirm: boolean, row: Row): void;
}

interface PreviousRowData {
  cusip: string;
  exchange: string;
  isin: string;
  name: string;
  ticker: string;
}

interface State {
  popUpConfirmationData: PopUpConfirmationState;
  previousRowData: PreviousRowData;
  rowData: Row[];
}

class GridView extends Component<Props, State> {
  private api: GridApi;
  private customMenuItems: any;
  private gridOptions: GridOptions;
  private previousFocusedCell: CellPosition | undefined;
  private textEditorInputNode: Element | undefined;
  private wrapperRef: RefObject<HTMLDivElement> = createRef();

  constructor(props: Props) {
    super(props);
    this.state = {
      popUpConfirmationData: {
        field: '',
        isDialogOpen: false,
        message: '',
        oldValue: '',
        rowId: '',
        setOldValue: false,
        value: '',
      },
      previousRowData: {
        cusip: '',
        exchange: '',
        isin: '',
        name: '',
        ticker: '',
      },
      rowData: [],
    };

    agGridEnterprise.LicenseManager.setLicenseKey(`${global.env.AG_GRID_KEY}`);

    this.customMenuItems = {
      autoSizeThisColumn: (colId: string | Column) => ({
        action: () => {
          if (colId && this.api) {
            this.api.autoSizeColumns([colId]);
          }
        },
        name: 'Autosize This Column',
      }),

      exportToCSV: {
        action: () => {
          if (this.exportToCSV) {
            this.exportToCSV();
          }
        },
        name: 'Export to CSV',
      },

      fitAllColumns: {
        action: this.sizeColumnsToFit,
        name: 'Fit All Columns',
      },
    };

    const isRowRevision = props.isRevision;
    const rowSelection = isRowRevision ? undefined : 'single';

    this.gridOptions = {
      columnTypes,
      defaultColDef: {
        filter: !isRowRevision,
        resizable: true,
        sortable: true,
      },
      getContextMenuItems: params => {
        const columnId: string | Column = params.column ? params.column.getColId() : null;

        return [
          'autoSizeAll',
          'separator',
          this.customMenuItems.fitAllColumns,
          this.customMenuItems.autoSizeThisColumn(columnId),
          'separator',
          'copy',
          'copyWithHeaders',
          this.customMenuItems.exportToCSV,
          'paste',
        ];
      },
      getRowId: row => {
        return row.data._id ?? row.data.id;
      },
      getRowStyle: rowStyle.addUserPositionBackGround,
      onCellEditingStarted: params => {
        const inputCell = getInputCellDOMNode();
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        if (!(isNil(inputCell) || isEmpty(flatten(inputCell)))) {
          inputCell[0].addEventListener(Events.keydown, this.checkForNavigationKeys);
        } else {
          this.textEditorInputNode = undefined;
        }
        params.node.setSelected(true);
      },
      onCellEditingStopped: () => {
        const inputNode = this.textEditorInputNode;
        if (!isUndefined(inputNode)) {
          inputNode.removeEventListener(Events.keydown, this.checkForNavigationKeys);
        }
      },
      onCellFocused: (event: CellFocusedEvent) => {
        const { api, column, rowIndex } = event;
        const focusedCell = api.getFocusedCell();
        const row = api.getDisplayedRowAtIndex(rowIndex!);

        if (column && row && !equals((column as Column)?.getColId(), 'action') && !isNull(focusedCell)) {
          if (!equals(focusedCell, this.previousFocusedCell)) {
            this.getRowRevisions(row.data.index);
          }
          this.cellFocused(column, row.data._id);
          this.previousFocusedCell = focusedCell;
        } else if (column && row && !equals((column as Column)?.getColId(), 'action')) {
          this.props.notifyCellBlurred?.();
        }
      },
      onCellValueChanged: params => {
        const { isDialogOpen } = this.state.popUpConfirmationData;
        if (isDialogOpen) {
          return;
        }
        const {
          colDef: { field },
          data,
          newValue,
          node: { id: rowId },
          oldValue,
        } = params;
        const { collectionId, dataSource, onConfirmCell } = this.props;
        const { previousRowData } = this.state;
        const row = find(oldRow => equals(oldRow.data._id, data._id), dataSource);

        let preventCallBlock = true;

        const modifiedRow = {
          data,
          errors: {},
          state: data.state,
        };

        const fieldConfirmParams = {
          checkValueForConfirmation: this.checkValueForConfirmation,
          collectionId,
          data,
          newValue,
          oldValue,
          previousRowData,
          rowId,
        };
        preventCallBlock = isFieldToBeConfirmed(field, fieldConfirmParams);
        const {
          ACQUIRER_TICKER,
          DIVIDEND_SUSPENDED,
          DRUG,
          EPS,
          EXPIRED,
          FIRST_NAME,
          LAST_NAME,
          REVENUE,
          TARGET_TICKER,
          TICKER,
        } = FIELDS;
        switch (field) {
          // FIXME: This should be handled on Backend side.
          case FIRST_NAME:
          case LAST_NAME: {
            const { first_name: firstName, last_name: lastName } = modifiedRow.data;
            modifiedRow.data.full_name = `${firstName} ${lastName}`;
            break;
          }
          case ACQUIRER_TICKER:
          case TARGET_TICKER:
          case TICKER: {
            if (!(equals(newValue, oldValue) && equals(data.exchange, previousRowData.exchange))) {
              this.setState({
                popUpConfirmationData: {
                  ...this.state.popUpConfirmationData,
                  rowId,
                },
              });
              onConfirmCell(modifiedRow, row, field);
              return;
            }
            break;
          }
          case EXPIRED: {
            const { expired } = modifiedRow.data;
            modifiedRow.data.expired = expired && moment(expired).unix();
            break;
          }
          case DIVIDEND_SUSPENDED: {
            const { dividend_suspended } = modifiedRow.data;
            modifiedRow.data.dividend_suspended = dividend_suspended && moment(dividend_suspended).unix();
            break;
          }
          case DRUG: {
            const { drug } = modifiedRow.data;
            if (drug) {
              onConfirmCell(modifiedRow, row, field);
            }
            return;
          }
          case EPS: {
            const { eps } = modifiedRow.data;
            if (!equals(newValue, oldValue)) {
              row.data.eps = eps;
            }
            break;
          }
          case REVENUE: {
            const { revenue } = modifiedRow.data;
            if (!equals(newValue, oldValue)) {
              row.data.revenue = revenue;
            }
            break;
          }
        }

        if (!equals(newValue, oldValue) && preventCallBlock) {
          onConfirmCell(modifiedRow, row, field);
        }
      },
      onGridReady: params => {
        const { collectionId } = this.props;
        if (params) {
          this.api = params.api;
        }

        if (isRowRevision) {
          const allColumns = this.api.getColumns();
          const allColmnIds = map<Column, string>(column => column.getColId(), allColumns);
          this.api.setColumnsVisible(allColmnIds, true);
        }

        if (equals(collectionId, CollectionId.ipo)) {
          this.api.autoSizeAllColumns();
        } else {
          this.api.sizeColumnsToFit();
        }

        if (equals(collectionId, CollectionId.rating)) {
          this.api.updateGridOptions({ paginationPageSize: 400 });
        }
      },

      pagination: true,

      paginationPageSize: 100,

      rowBuffer: 100,

      rowHeight: 25,

      rowSelection,

      sideBar: sideBar.showToolPanels(isRowRevision),

      singleClickEdit: true,

      suppressScrollOnNewData: true,

      tabToNextCell: (params: TabToNextCellParams) => {
        const { skippableFields } = props;
        const { backwards, nextCellPosition, previousCellPosition } = params;
        const wasPreviousCellShippable =
          skippableFields &&
          find(column => equals(column, previousCellPosition.column.getColDef().field), skippableFields);

        if (
          backwards ||
          isNull(nextCellPosition) ||
          !equals(previousCellPosition.rowIndex, nextCellPosition.rowIndex) ||
          wasPreviousCellShippable
        ) {
          return nextCellPosition;
        }

        const visibleColumns = this.api.getAllDisplayedColumns();
        const columnsWithoutSkippables =
          skippableFields && visibleColumns.filter(column => !skippableFields.includes(column.getColDef().field ?? ''));
        const columnAfter =
          columnsWithoutSkippables &&
          columnsWithoutSkippables[
            findIndex(column => equals(column, previousCellPosition.column), columnsWithoutSkippables) + 1
          ];

        if (isEmpty(columnAfter) || isNil(columnAfter)) {
          return nextCellPosition;
        }

        const nextCell: any = {
          column: columnAfter,
          floating: null,
          rowIndex: previousCellPosition.rowIndex,
        };
        return nextCell;
      },
    };
  }

  exportToCSV = () => {
    if (!this.api) {
      return;
    }
    const { collectionId } = this.props;
    const allDisplayedColumns = this.api.getAllDisplayedColumns();
    const collectionName = selectCollectionName(collectionId);
    let name: string = collectionId;
    if (collectionName) {
      name = `NewsDesk Tools ${collectionName} Calendar`;
    }
    const params = exportToCSV(allDisplayedColumns, name);
    this.api.exportDataAsCsv(params);
  };

  componentDidMount() {
    window.addEventListener(Events.mousedown, this.handleClickOutside);
  }

  componentWillUnmount() {
    window.removeEventListener(Events.mousedown, this.handleClickOutside);
  }

  handleClickOutside = event => {
    const { confirmMessage, isRevision } = this.props;
    const { isDialogOpen } = this.state.popUpConfirmationData;
    const isConfirmationDialogOpen = !(isEmpty(confirmMessage) || isNil(confirmMessage)) || isDialogOpen;
    if (isConfirmationDialogOpen) {
      return;
    }
    if (!isRevision && this.wrapperRef && !this.wrapperRef.current.contains(event.target)) {
      // this.gridOptions.api.stopEditing();
      // this.gridOptions.api.clearFocusedCell();
      if (!(isEmpty(this.previousFocusedCell) || isNil(this.previousFocusedCell))) {
        this.props.notifyCellBlurred();
      }
      this.previousFocusedCell = null;
    }
  };

  shouldComponentUpdate(nextProps: Props, nextState: State) {
    if (!equals(this.state, nextState)) {
      return true;
    }
    const { gridOperation: prevGridOperation } = this.props;
    const { collectionId, gridOperation, isNotification, isRevision, permissionSet, usersPosition } = nextProps;
    if (isUndefined(gridOperation)) {
      return true;
    }
    const { row } = gridOperation;
    const notificationDataProps = {
      collectionId,
      permissionSet,
      row,
      usersPosition,
    };
    return checkRevisionOrNotification(
      this.api,
      isNotification,
      isRevision,
      notificationDataProps,
      prevGridOperation,
      gridOperation,
    );
  }

  componentDidUpdate(prevProps) {
    const { dataSource, defaultEditingColumn, isRevision } = this.props;
    // No need to handle focus manually for revisions
    if (isRevision || !equals(prevProps.query, this.props.query) || this.props.loading) {
      return;
    }
    // To detect that it only focuses on new row only but not on search or anything else
    const isRowAdded = equals(dataSource.length, prevProps.dataSource.length + 1);

    if (isRowAdded) {
      if (this.api) {
        this.api.setFocusedCell(0, defaultEditingColumn, null);
      }
    }

    if (this.api) {
      const cell = this.api.getFocusedCell();

      if (!isNil(cell)) {
        if (cell && cell.column && cell.column.getColDef().editable) {
          this.api.startEditingCell({
            colKey: cell.column,
            rowIndex: cell.rowIndex,
          });
        } else {
          this.api.setFocusedCell(cell.rowIndex, cell.column);
        }
      }
    }
  }

  sizeColumnsToFit = () => {
    if (this.api) {
      window.setTimeout(() => {
        if (this.api) {
          this.api.sizeColumnsToFit();
        }
      }, 0);
    }
  };

  getRowRevisions = rowNumber => {
    const { loadRevisions } = this.props;

    if (isNull(rowNumber)) {
      return;
    }

    if (!isUndefined(loadRevisions) && rowNumber >= 0) {
      loadRevisions(rowNumber);
    }
  };

  cellFocused = (column, rowId) => {
    const { isRevision, notifyCellFocused } = this.props;
    if (isRevision) {
      return;
    }
    const field = column.getColDef().field;
    notifyCellFocused(rowId, field);
  };

  checkValueForConfirmation = (
    field: string,
    rowId: string,
    message: string,
    newValue: string,
    oldValue: string,
  ): boolean => {
    const { popUpConfirmationData } = this.state;
    if (!equals(field, FIELDS.TICKER) && equals(newValue, oldValue)) {
      return true;
    }
    if (popUpConfirmationData.setOldValue) {
      this.setState({
        popUpConfirmationData: {
          ...popUpConfirmationData,
          setOldValue: false,
        },
      });
      return false;
    }
    if (!(isEmpty(message) || isNil(message))) {
      this.setState({
        popUpConfirmationData: {
          doubleConfirm: equals(field, FIELDS.REVENUE) || equals(field, FIELDS.EPS) ? true : false,
          field,
          isDialogOpen: true,
          message,
          oldValue,
          rowId,
          setOldValue: false,
          value: newValue,
        },
      });
      return false;
    }
    return true;
  };

  handleConfirm = (): void => {
    const { confirmMessage, confirmRowId, onConfirmCell, onRowPublish, updatedField } = this.props;
    const { popUpConfirmationData } = this.state;
    const { doubleConfirm, field, message, rowId, value } = popUpConfirmationData;

    if (this.api) {
      let rowNode;
      if (!(isEmpty(confirmMessage) || isNil(confirmMessage)) && (isEmpty(updatedField) || isNil(updatedField))) {
        this.api.forEachNode(node => {
          if (equals(node.data._id, confirmRowId)) {
            rowNode = node;
          }
        });
      } else {
        rowNode = this.api.getRowNode(isEmpty(rowId) ? confirmRowId ?? rowId : rowId);
      }
      const { data } = rowNode;
      const { cusip, eps_est, eps_prior, exchange, isin, name, revenue_est, revenue_prior, ticker } = data;

      if (doubleConfirm) {
        const val = parseFloat(expand(value));
        const isEstimateField = message.includes('Estimate');
        const isPriorField = message.includes('Prior');
        const denotes = isEstimateField ? 'estimates' : isPriorField ? 'prior' : eps_est ? 'estimates' : 'prior';
        const epsVal = isEstimateField
          ? parseFloat(expand(eps_est))
          : isPriorField
            ? parseFloat(expand(eps_prior))
            : eps_est
              ? parseFloat(expand(eps_est))
              : parseFloat(expand(eps_prior));
        const revenueVal = isEstimateField
          ? parseFloat(expand(revenue_est))
          : isPriorField
            ? parseFloat(expand(revenue_prior))
            : revenue_est
              ? parseFloat(expand(revenue_est))
              : parseFloat(expand(revenue_prior));

        const calField = field == FIELDS.EPS ? epsVal : revenueVal;
        const confirmMessage = `<p style='color: red;'>Are you certain you wish to update the ${field} to ${numberFormatter(
          {
            fixed: 3,
            value: val,
          },
        )} compared to the ${numberFormatter({ fixed: 3, value: calField })} ${denotes}?</p>`;
        this.setState({
          popUpConfirmationData: {
            ...popUpConfirmationData,
            doubleConfirm: false,
            message: confirmMessage,
          },
        });
        return;
      }

      const modifiedRow = {
        data,
        errors: data.errors,
        state: data.state,
      };

      if (!(isEmpty(updatedField) || isNil(updatedField))) {
        modifiedRow.data[field] = value;
        switch (field) {
          case FIELDS.TIME:
          case FIELDS.DATE: {
            if (!isNil(value)) {
              rowNode.setDataValue(field, value);
              this.api.redrawRows();
              break;
            }
          }
        }
      }

      if (isEmpty(field) && isEmpty(value) && !(isEmpty(updatedField) || isNil(updatedField))) {
        if (typeof modifiedRow.data[updatedField] == 'boolean') {
          modifiedRow.data[updatedField] = !modifiedRow.data[updatedField];
        }
      }

      this.setState({
        popUpConfirmationData: {
          ...popUpConfirmationData,
          isDialogOpen: false,
        },
        previousRowData: {
          cusip,
          exchange,
          isin,
          name,
          ticker,
        },
      });
      if (!(isEmpty(confirmMessage) || isNil(confirmMessage))) {
        if (isEmpty(updatedField) || isNil(updatedField)) {
          onRowPublish(true, modifiedRow);
        } else {
          onConfirmCell(modifiedRow, undefined, updatedField, true);
        }
        return;
      }
      modifiedRow.data[field] = value;
      onConfirmCell(modifiedRow, undefined, field);
    }
  };

  onCancel = (): void => {
    const { popUpConfirmationData, previousRowData } = this.state;
    const { confirmMessage, isNotification, onCancel, updatedField } = this.props;

    if (!(isEmpty(confirmMessage) || isNil(confirmMessage))) {
      onCancel();
      if (!(isEmpty(updatedField) || isNil(updatedField))) {
        const definedRowData = this.getDefinedRowData();
        setTimeout(() => {
          this.api.setRowData(definedRowData);
        }, 0);
      }
      return;
    }
    const { field, oldValue, rowId } = popUpConfirmationData;
    const rowNode = this.api.getRowNode(rowId);

    if (equals(field, FIELDS.TICKER)) {
      const newData = {
        ...rowNode.data,
        ...previousRowData,
      };
      rowNode.setData(newData);
    }
    rowNode.setDataValue(field, oldValue);
    setTimeout(() => {
      this.api.redrawRows();
    }, 0);
    this.setState({
      popUpConfirmationData: {
        ...popUpConfirmationData,
        isDialogOpen: false,
        setOldValue: true,
      },
    });
    if (isNotification) {
      this.forceUpdate();
    }
  };

  getDefinedRowData = () => {
    const { collectionId, dataSource, permissionSet, usersPosition } = this.props;
    const userCursors = Object.values(usersPosition ?? {});

    if (
      collectionId === CollectionId.conference &&
      dataSource.length > 0 &&
      this.state.rowData.length !== dataSource.length
    ) {
      const rows = dataSource.map(row => row.data._id);
      const call = async () => {
        try {
          // Await the async transcript status fetch
          const res = await getTranscriptStatus(this.props.sessionKey, { call_ids: rows });
          const { data } = res;
          const trans = data.filter(re => re.has_transcript).map(m => m.id);

          // Map through dataSource and build the final rowData synchronously
          const updatedRows = dataSource.map((row, index) => ({
            ...row.data,
            collectionId,
            errors: !(isEmpty(row.errors) || isNil(row.errors)) ? row.errors : {},
            has_transcript: trans.includes(row.data._id),
            index,
            permissionSet,
            usersPosition: buildUserPositions(userCursors, row.data._id),
          }));
          this.setState({
            rowData: updatedRows,
          });
          return updatedRows; // Return the result after the async call
        } catch (error) {
          console.error('Error fetching transcript status:', error);
          return []; // Return an empty array or handle error case
        }
      };
      call();
      if (this.state.rowData.length > 0) {
        return this.state.rowData;
      }
    }
    return dataSource.map((row, index) => ({
      ...row.data,
      collectionId,
      errors: !(isEmpty(row.errors) || isNil(row.errors)) ? row.errors : {},
      index,
      permissionSet,
      state: row.state,
      usersPosition: buildUserPositions(userCursors, row.data._id),
    }));
  };

  checkForNavigationKeys = (event: KeyboardEvent) => {
    const { code } = event;
    if (equals(code, HotKeys.Key_ARROW_UP) || equals(code, HotKeys.Key_ARROW_DOWN)) {
      this.shiftOneRow(code);
      event.preventDefault();
    }
  };

  shiftOneRow = (keyCode: string) => {
    const { column, rowIndex } = this.api.getFocusedCell();
    const rowCount = this.api.getDisplayedRowCount();
    switch (keyCode) {
      case HotKeys.Key_ARROW_UP: {
        if (!equals(rowIndex, 0)) {
          this.api.stopEditing();
          this.api.setFocusedCell(rowIndex - 1, column);
        }
        break;
      }
      case HotKeys.Key_ARROW_DOWN: {
        if (!equals(rowIndex, rowCount - 1)) {
          this.api.stopEditing();
          this.api.setFocusedCell(rowIndex + 1, column);
        }
        break;
      }
    }
  };

  render() {
    const {
      collectionId,
      confirmMessage,
      defaultColumnDefinition,
      isRevision,
      onPostStory,
      onRowCreate,
      onRowPublish,
      permissionSet,
    } = this.props;

    const {
      popUpConfirmationData: { isDialogOpen, message },
    } = this.state;
    const shouldDialogOpen = !(isEmpty(confirmMessage) || isNil(confirmMessage)) || isDialogOpen;
    const displayMessage = confirmMessage || message;

    const renderContent = (
      <div className="bp4-dialog-body" dangerouslySetInnerHTML={{ __html: sanitizeHTML(displayMessage) }} />
    );

    const renderButtons = (
      <div className="bp4-dialog-footer">
        <div className="bp4-dialog-footer-actions">
          <Button intent={Intent.DANGER} onClick={this.handleConfirm} text="Confirm" />
          <Button intent={Intent.NONE} onClick={this.onCancel} text="Cancel" />
        </div>
      </div>
    );

    const columnDefinitions = pipe(columnDefinitionModifier.addRowNumberColumn)(this.props.columnDefinitions);

    const definedRowData = this.getDefinedRowData();
    return (
      <div
        className={classNames('ag-theme-balham', collectionId, {
          'accordion-view': isRevision,
          'grid-view': !isRevision,
        })}
        ref={this.wrapperRef}
      >
        <AgGridReact
          columnDefs={columnDefinitions}
          defaultColDef={defaultColumnDefinition}
          gridOptions={this.gridOptions}
          rowData={definedRowData}
        />
        {!isRevision && (
          <GridHotkeys
            api={this.api}
            onPostStory={onPostStory}
            onRowCreate={onRowCreate}
            onRowPublish={onRowPublish}
            permissionSet={permissionSet}
          />
        )}
        <Dialog
          canEscapeKeyClose={false}
          canOutsideClickClose={false}
          className="bz-alert-dialog"
          icon="ban-circle"
          isCloseButtonShown={false}
          isOpen={shouldDialogOpen}
          onClose={this.onCancel}
          title="Confirmation"
        >
          {renderContent}
          {renderButtons}
        </Dialog>
        <WarningDialog collectionId={collectionId} />
      </div>
    );
  }
}

export default GridView;
