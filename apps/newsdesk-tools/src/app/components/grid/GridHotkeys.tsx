import { <PERSON>Combo } from '@blueprintjs/core';
import { equals, isEmpty, isNil } from 'ramda';
import React from 'react';

import { GridApi } from '@ag-grid-community/core';
import { Row, RowState } from '../../reducers/entities/collectionEntities';
import { Events } from '../../utils/eventEntities';
import { PermissionsByGroupId } from '../collections/collectionEntities';
import { HotKeys } from '../postbox/entities';

export interface GridHotkeysProps {
  api: GridApi;
  permissionSet: PermissionsByGroupId;
  onPostStory?(modifiedRow: Row): void;
  onRowCreate(): void;
  onRowPublish?(isConfirm: boolean, row: Row): void;
}

export type Props = GridHotkeysProps;

export const GridHotkeys: React.FC<Props> = props => {
  React.useEffect(() => {
    window.addEventListener(Events.keydown, handleKeyDown);
    return () => window.removeEventListener(Events.keydown, handleKeyDown);
  });

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.shiftKey && (event.altKey || event.ctrlKey)) {
      const {
        onPostStory,
        onRowCreate,
        onRowPublish,
        permissionSet: { permissions },
      } = props;
      const { api } = props;
      const key = event.code;

      if (equals(key, HotKeys.Key_N) && permissions.canAddNewRow) {
        onRowCreate();
      } else if (equals(key, HotKeys.Key_ENTER) && permissions.canPublishRow) {
        if (!isNil(api)) {
          const data = api.getSelectedRows()[0];
          const { errors, state } = data;
          if (!isNil(data) && (isEmpty(errors) || isNil(errors)) && !equals(state, RowState.PUBLISHED)) {
            const modifiedRow = {
              data,
              errors,
              state,
            };
            onRowPublish(false, modifiedRow);
          }
        }
      } else if (equals(key, HotKeys.Key_S) && permissions.canPostStory) {
        if (!isNil(api)) {
          const data = api.getSelectedRows()[0];
          const { errors, state } = data;
          if (!isNil(data) && (isEmpty(errors) || isNil(errors))) {
            const modifiedRow = {
              data,
              errors,
              state,
            };
            onPostStory(modifiedRow);
          }
        }
      } else {
        return;
      }
      event.preventDefault();
      event.stopPropagation();
    }
  };

  // useHotkeys()

  // // renderHotkeys renders the hotkeys
  // const renderHotkeys = () => {
  //   return (
  //     <Hotkeys>
  //       <Hotkey
  //         combo="tab"
  //         global
  //         label="Next Important Cell"
  //       />
  //       <Hotkey
  //         combo="shift + tab"
  //         global
  //         label="Previous Cell"
  //       />
  //       <Hotkey
  //         combo="up"
  //         global
  //         label="Up one row"
  //       />
  //       <Hotkey
  //         combo="down"
  //         global
  //         label="Down one row"
  //       />
  //       <Hotkey
  //         combo="esc"
  //         global
  //         label="Revert cell to normal"
  //       />

  //       <Hotkey
  //         combo="shift+ctrl+d"
  //         global
  //         label="Toggle Drawer"
  //       />
  //       <Hotkey
  //         combo="shift+alt+d"
  //         global
  //         label="Toggle Drawer"
  //       />

  //       <Hotkey
  //         combo="shift+ctrl+n"
  //         global
  //         label="Create New Row"
  //       />

  //       <Hotkey
  //         combo="shift+alt+n"
  //         global
  //         label="Create New Row"
  //       />

  //       <Hotkey
  //         combo="shift+ctrl+enter"
  //         global
  //         label="Save Row"
  //       />

  //       <Hotkey
  //         combo="shift+alt+enter"
  //         global
  //         label="Save Row"
  //       />

  //       <Hotkey
  //         combo="shift+alt+s"
  //         global
  //         label="Open Post Dialog"
  //       />

  //       <Hotkey
  //         combo="shift+alt+s"
  //         global
  //         label="Open Post Dialog"
  //       />
  //     </Hotkeys>
  //   );
  // }

  // render renders the component
  return (
    <div className="bz-grid-hotkeys">
      <div className="pt-text-muted">
        If you need help to create, edit, delete rows or navigate in the grid, press <KeyCombo combo="SHIFT + ?" /> for
        more information.
      </div>
    </div>
  );
};

export default GridHotkeys;
