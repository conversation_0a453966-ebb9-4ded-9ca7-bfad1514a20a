import { KeyCombo } from '@blueprintjs/core';
import * as React from 'react';
import { FunctionComponent } from 'react';

const Instructions: FunctionComponent<Record<string, never>> = () => (
  <div className="row bz-grid-hotkeys" style={{ float: 'left' }}>
    <div>
      <KeyCombo combo="Shift + Alt + N" />
      Create a tab
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + Number" />
      or
      <KeyCombo combo="Shift + Alt + Number" />
      Switch Tab
    </div>
    <div>
      <KeyCombo combo="Shift + Alt + X" />
      Remove Tab
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + /" />
      or
      <KeyCombo combo="Shift + Alt + /" />
      Go to Title
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + B" />
      or
      <KeyCombo combo="Shift + Alt + B" />
      Go to Body
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + K" />
      or
      <KeyCombo combo="Shift + Alt + K" />
      Go to Tickers
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + G" />
      or
      <KeyCombo combo="Shift + Alt + G" />
      Go to Tags
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + C" />
      or
      <KeyCombo combo="Shift + Alt + C" />
      Go to Categories
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + S" />
      <KeyCombo combo="Shift + Alt + S" />
      Go to Sentiment
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + Z" />
      or
      <KeyCombo combo="Shift + Alt + Z" />
      Toggle BZ
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + P" />
      or
      <KeyCombo combo="Shift + Alt + P" />
      Toggle PRO
    </div>
    <div>
      <KeyCombo combo="Shift + Ctrl + ENTER" />
      or
      <KeyCombo combo="Shift + Alt + ENTER" />
      Post the Article
    </div>
  </div>
);

export default Instructions;
