import { But<PERSON>, Classes, FileInput, InputGroup, Intent, Spinner, SpinnerSize } from '@blueprintjs/core';
import { isEmpty, isNil } from 'ramda';
import { isString } from 'ramda-adjunct';
import React, { Component } from 'react';

import { createLogo } from '../../actions/logo';

import { SketchPicker } from 'react-color';
import reactCSS from 'reactcss';
import { Logo } from '../../reducers/entities/logoEntities';
import { Select, message } from 'antd';
import generatePicker from 'antd/es/date-picker/generatePicker';
import moment from 'moment';

import type { Moment } from 'moment';
import momentGenerateConfig from 'rc-picker/lib/generate/moment';
import FigiAutoComplete, { Options } from '../FigiAutocomplete';
interface LogoError {
  code: string;
  id: string;
  value: string;
}

interface State {
  id?: string;
  background_dark: string;
  background_light: string;
  company_url: string;
  last_audited: string;
  error: boolean;
  errors: LogoError[];
  isColorPickerDarkBackground: boolean;
  isColorPickerLightBackground: boolean;
  logo_dark: any;
  logo_light: any;
  logo_vector_dark: any;
  logo_vector_light: any;
  mark_composite_dark: any;
  mark_composite_light: any;
  mark_dark: any;
  mark_light: any;
  mark_vector_dark: any;
  mark_vector_light: any;
  mark_vector_composite_light: any;
  mark_vector_composite_dark: any;
  requestProcessing: boolean;
  selectedType: string | undefined;
  searchType: string | undefined;
  micCode: string | undefined;
  selectedValue: string | undefined;
}

interface Props {
  onLogoCancel: () => void;
  fetchData: () => void;
  sessionKey: string;
  editRowObject?: Logo;
}
enum stateName {
  IsDarkBackground = 'isColorPickerDarkBackground',
  IsLightBackground = 'isColorPickerLightBackground',
  DarkBackground = 'background_dark',
  LightBackground = 'background_light',
}

const DatePicker = generatePicker<Moment>(momentGenerateConfig);
class LogoForm extends Component<Props, State> {
  constructor(props) {
    super(props);
    this.state = {
      background_dark: (this.props.editRowObject && this.props.editRowObject.colors.background_dark) || '#999999',
      background_light: (this.props.editRowObject && this.props.editRowObject.colors.background_light) || '#f0f0f0',
      company_url: (this.props.editRowObject && this.props.editRowObject.company_url) || '',
      error: false,
      errors: [],
      id: (this.props.editRowObject && this.props.editRowObject.id) || undefined,
      isColorPickerDarkBackground: false,
      isColorPickerLightBackground: false,
      last_audited: (this.props.editRowObject && this.props.editRowObject.last_audited) || '',
      logo_dark: (this.props.editRowObject && this.props.editRowObject.files.logo_dark) || {},
      logo_light: (this.props.editRowObject && this.props.editRowObject.files.logo_light) || {},
      logo_vector_dark: (this.props.editRowObject && this.props.editRowObject.files.logo_vector_dark) || {},
      logo_vector_light: (this.props.editRowObject && this.props.editRowObject.files.logo_vector_light) || {},
      mark_composite_dark: (this.props.editRowObject && this.props.editRowObject.files.mark_composite_dark) || {},
      mark_composite_light: (this.props.editRowObject && this.props.editRowObject.files.mark_composite_light) || {},
      mark_dark: (this.props.editRowObject && this.props.editRowObject.files.mark_composite_dark) || {},
      mark_light: (this.props.editRowObject && this.props.editRowObject.files.mark_composite_light) || {},
      mark_vector_composite_dark:
        (this.props.editRowObject && this.props.editRowObject.files.mark_vector_composite_dark) || {},
      mark_vector_composite_light:
        (this.props.editRowObject && this.props.editRowObject.files.mark_vector_composite_light) || {},
      mark_vector_dark: (this.props.editRowObject && this.props.editRowObject.files.mark_vector_dark) || {},
      mark_vector_light: (this.props.editRowObject && this.props.editRowObject.files.mark_vector_light) || {},
      micCode: (this.props.editRowObject && this.props.editRowObject.mic_code) || '',
      requestProcessing: false,
      searchType: 'symbol',
      selectedType: (this.props.editRowObject && this.props.editRowObject.type) || 'security',
      selectedValue: undefined,
    };
  }

  validate = (file, field) => {
    let valid;
    switch (file.type) {
      case 'image/png':
        if (
          field === 'logo_light' ||
          field === 'logo_dark' ||
          field === 'mark_light' ||
          field === 'mark_dark' ||
          field === 'mark_composite_light' ||
          field === 'mark_composite_dark'
        )
          valid = true;
        break;
      case 'image/svg+xml':
        if (
          field === 'logo_vector_light' ||
          field === 'logo_vector_dark' ||
          field === 'mark_vector_light' ||
          field === 'mark_vector_dark' ||
          field === 'mark_vector_composite_light' ||
          field === 'mark_vector_composite_dark'
        )
          valid = true;
        break;
      default:
        valid = false;
    }
    if (!valid) message.error(`File type ${file.type} is not supported!`, 3);

    return valid;
  };

  handleImageUpload = (e: any, field: string) => {
    const uploadedFile = e.target.files[0];
    if (this.validate(uploadedFile, field)) {
      this.setState({
        ...this.state,
        [e.target.id]: uploadedFile,
      });
    }
  };

  handleInputChange = (e: any) => {
    const text = e.target.value;

    this.setState({
      ...this.state,
      [e.target.id]: text,
    });
  };

  handleMicChange = (e: any) => {
    const text = e.target.value;

    this.setState({
      ...this.state,
      micCode: text,
    });
  };

  onLogoSubmit = () => {
    this.setState({ requestProcessing: true });
    const {
      background_dark,
      background_light,
      company_url,
      id,
      last_audited,
      logo_dark,
      logo_light,
      logo_vector_dark,
      logo_vector_light,
      mark_composite_dark,
      mark_composite_light,
      mark_dark,
      mark_light,
      mark_vector_composite_dark,
      mark_vector_composite_light,
      mark_vector_dark,
      mark_vector_light,
      micCode,
      selectedType,
      selectedValue,
    } = this.state;

    const formData = new FormData();
    formData.append('color_background_light', background_light);
    formData.append('color_background_dark', background_dark);
    formData.append('figi_share_class', selectedValue);
    formData.append('company_url', company_url);
    formData.append('logo_dark', logo_dark);
    formData.append('logo_light', logo_light);
    formData.append('logo_vector_dark', logo_vector_dark);
    formData.append('logo_vector_light', logo_vector_light);
    formData.append('mark_composite_dark', mark_composite_dark);
    formData.append('mark_composite_light', mark_composite_light);
    formData.append('mark_dark', mark_dark);
    formData.append('mark_light', mark_light);
    formData.append('mark_vector_dark', mark_vector_dark);
    formData.append('mark_vector_light', mark_vector_light);
    formData.append('mark_vector_composite_light', mark_vector_composite_light);
    formData.append('mark_vector_composite_dark', mark_vector_composite_dark);
    formData.append('last_audited', last_audited);
    formData.append('mic_code', micCode ?? '');
    formData.append('type', selectedType ?? '');

    // if (selectedType && !micCode) {
    //   const err: LogoError[] = [{ code: 'search_keys', id: 'search_keys', value: 'Mic Codes field is mandatory!' }];
    //   this.setState({
    //     error: true,
    //     errors: err,
    //     requestProcessing: false,
    //   });
    //   return;
    // }

    if (id) formData.append('id', id);

    createLogo(formData, this.props.sessionKey)
      .then(() => {
        this.props.fetchData();
        this.setState({
          requestProcessing: false,
        });
        this.props.onLogoCancel();
      })
      .catch(errorObj => {
        const {
          data: { errors },
        } = errorObj.response;

        this.setState({
          error: true,
          errors,
          requestProcessing: false,
        });
      });
  };
  handleColorPickerClick = (fieldName: string) => {
    this.setState({
      ...this.state,
      [fieldName]: true,
    });
  };

  handleColorPickerClickClose = (fieldName: string) => {
    this.setState({
      ...this.state,
      [fieldName]: false,
    });
  };
  handleChangeColorPicker = (color, stateName) => {
    this.setState({
      ...this.state,
      [stateName]: color.hex,
    });
  };
  getFileName = (fileName: any) => {
    let setfileName = fileName;
    if (typeof fileName !== 'object' && fileName !== undefined) {
      setfileName.replace(/^.*[\\/]/, '');
    } else {
      setfileName = fileName.name;
    }
    return setfileName;
  };

  handleTypeChange = value => {
    this.setState({
      ...this.state,
      selectedType: value,
    });
  };

  handleSearchTypeChange = value => {
    this.setState({
      ...this.state,
      searchType: value,
    });
  };

  onChange = (val: Options | Options[]) => {
    const value = val ? (val as Options).value : '';
    this.setState(preState => ({ ...preState, selectedValue: value }));
  };

  render() {
    const {
      background_dark,
      background_light,
      company_url,
      error,
      errors,
      last_audited,
      logo_dark,
      logo_light,
      logo_vector_dark,
      logo_vector_light,
      mark_composite_dark,
      mark_composite_light,
      mark_dark,
      mark_light,
      mark_vector_composite_dark,
      mark_vector_composite_light,
      mark_vector_dark,
      mark_vector_light,
      micCode,
      requestProcessing,
      searchType,
      selectedType,
    } = this.state;

    const styles = reactCSS({
      default: {
        cover: {
          bottom: '0px',
          left: '0px',
          position: 'fixed',
          right: '0px',
          top: '0px',
        },
        darkcolor: {
          background: `${background_dark}`,
          borderRadius: '2px',
          height: '19px',
          width: '36px',
        },
        lightcolor: {
          background: `${background_light}`,
          borderRadius: '2px',
          height: '19px',
          width: '36px',
        },
        popover: {
          position: 'absolute',
          zIndex: '2',
        },
        swatch: {
          background: '#fff',
          borderRadius: '1px',
          boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
          cursor: 'pointer',
          display: 'inline-block',
          marginLeft: '5px',
          padding: '5px',
        },
      },
    });

    const onLastAuditedChange = date => {
      this.setState({
        ...this.state,
        last_audited: date.toISOString(),
      });
    };

    const errorContent = errors ? (
      errors.map(err => {
        return (
          <li className="error-detail" key={err.id}>
            {err.value}
          </li>
        );
      })
    ) : (
      <div>Server Erorr</div>
    );
    const { Option } = Select;

    return (
      <div className="logos-container">
        <div className="form-field" style={{ display: 'flex' }}>
          <div>
            <div className="label">Type</div>
            <div>
              <Select
                defaultValue="security"
                id="type"
                onChange={this.handleTypeChange}
                placeholder="Select a Type"
                style={{ width: 200 }}
                value={selectedType}
              >
                <Option value="security">Security</Option>
                <Option value="exchange">Exchange</Option>
              </Select>
            </div>
          </div>
          {this.state.selectedType === 'security' && (
            <div style={{ marginLeft: '5px' }}>
              <div>
                <div className="label">Search Type</div>
                <Select
                  defaultValue="Symbol"
                  id="searchType"
                  onChange={this.handleSearchTypeChange}
                  placeholder="Select a Search Type"
                  style={{ width: 250 }}
                  value={searchType}
                >
                  <Option value="symbol">Symbol</Option>
                  <Option value="refinitiv_name">Company Name (Refinitiv Name)</Option>
                  <Option value="isin">ISIN</Option>
                  <Option value="cik">CIK</Option>
                  <Option value="figi_share_class">FIGI Share Class</Option>
                  <Option value="figi">FIGI</Option>
                  <Option value="mic_code:symbol">MIC:Symbol</Option>
                </Select>
              </div>
            </div>
          )}
          <div className="form-field"></div>
          {error && errors[0].value.includes('type') && (
            <span style={{ color: 'red' }}>{errors[0].value.split('error:')[1]}</span>
          )}
        </div>
        {this.state.selectedType === 'security' && (
          <div className="form-field">
            <div className="label">Company</div>
            <div>
              <FigiAutoComplete
                onChange={this.onChange}
                placeholder="SEARCH COMPANY"
                queryType={searchType ?? 'symbol'}
              />
            </div>
            {error && errors[0].value.includes('cik') && (
              <span style={{ color: 'red' }}>{errors[0].value.split('error:')[1]}</span>
            )}
          </div>
        )}
        {this.state.selectedType === 'exchange' && (
          <div className="form-field">
            <div className="label">Mic Codes</div>
            <div>
              <InputGroup id="search_keys" onChange={this.handleMicChange} type="text" value={micCode} />
            </div>
            {error && errors[0].value.includes('search_keys') && (
              <span style={{ color: 'red' }}>{errors[0].value.split('error:')[1]}</span>
            )}
          </div>
        )}
        <div className="form-field">
          <div className="label">Company URL</div>
          <div>
            <InputGroup id="company_url" onChange={this.handleInputChange} type="text" value={company_url} />
          </div>
          {error && errors[0].value.includes('companyURL') && (
            <span style={{ color: 'red' }}>{errors[0].value.split('error:')[1]}</span>
          )}
        </div>
        <div className="form-field">
          <div className="label">Light Background Color</div>
          <div className="input-field-wrap">
            <InputGroup
              id="color_background_light"
              onChange={this.handleInputChange}
              type="text"
              value={background_light}
            />
            <div onClick={() => this.handleColorPickerClick(stateName.IsLightBackground)} style={styles.swatch}>
              <div style={styles.lightcolor} />
            </div>
            {this.state.isColorPickerLightBackground ? (
              <div style={styles.popover}>
                <div
                  onClick={() => this.handleColorPickerClickClose(stateName.IsLightBackground)}
                  style={styles.cover}
                />
                <SketchPicker
                  color={this.state.background_light}
                  disableAlpha={true}
                  onChangeComplete={color => this.handleChangeColorPicker(color, stateName.LightBackground)}
                />
              </div>
            ) : null}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Dark Background Color</div>
          <div className="input-field-wrap">
            <InputGroup
              id="color_background_dark"
              onChange={this.handleInputChange}
              type="text"
              value={background_dark}
            />
            <div onClick={() => this.handleColorPickerClick(stateName.IsDarkBackground)} style={styles.swatch}>
              <div style={styles.darkcolor} />
            </div>
            {this.state.isColorPickerDarkBackground ? (
              <div style={styles.popover}>
                <div
                  onClick={() => this.handleColorPickerClickClose(stateName.IsDarkBackground)}
                  style={styles.cover}
                />
                <SketchPicker
                  color={this.state.background_dark}
                  disableAlpha={true}
                  onChangeComplete={color => this.handleChangeColorPicker(color, stateName.DarkBackground)}
                />
              </div>
            ) : null}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Logo Light</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'logo_light' }}
              onInputChange={event => this.handleImageUpload(event, 'logo_light')}
              text={logo_light.name || this.getFileName(logo_light)}
            />
            {(!isEmpty(logo_light) || isNil(logo_light)) && isString(logo_light) && (
              <div className="image-container">
                <a href={logo_light} rel="noreferrer" target="_blank">
                  <img src={logo_light} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Logo Dark</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'logo_dark' }}
              onInputChange={event => this.handleImageUpload(event, 'logo_dark')}
              text={logo_dark.name || this.getFileName(logo_dark)}
            />
            {(!isEmpty(logo_dark) || isNil(logo_dark)) && isString(logo_dark) && (
              <div className="image-container">
                <a href={logo_dark} rel="noreferrer" target="_blank">
                  <img src={logo_dark} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Logo Vector Light</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'logo_vector_light' }}
              onInputChange={event => this.handleImageUpload(event, 'logo_vector_light')}
              text={logo_vector_light.name || this.getFileName(logo_vector_light)}
            />
            {(!isEmpty(logo_vector_light) || isNil(logo_vector_light)) && isString(logo_vector_light) && (
              <div className="image-container">
                <a href={logo_vector_light} rel="noreferrer" target="_blank">
                  <img src={logo_vector_light} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Logo Vector Dark</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'logo_vector_dark' }}
              onInputChange={event => this.handleImageUpload(event, 'logo_vector_dark')}
              text={logo_vector_dark.name || this.getFileName(logo_vector_dark)}
            />
            {(!isEmpty(logo_vector_dark) || isNil(logo_vector_dark)) && isString(logo_vector_dark) && (
              <div className="image-container">
                <a href={logo_vector_dark} rel="noreferrer" target="_blank">
                  <img src={logo_vector_dark} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Light</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_light' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_light')}
              text={mark_light.name || this.getFileName(mark_light)}
            />
            {(!isEmpty(mark_light) || isNil(mark_light)) && isString(mark_light) && (
              <div className="image-container">
                <a href={mark_light} rel="noreferrer" target="_blank">
                  <img src={mark_light} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Dark</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_dark' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_dark')}
              text={mark_dark.name || this.getFileName(mark_dark)}
            />
            {(!isEmpty(mark_dark) || isNil(mark_dark)) && isString(mark_dark) && (
              <div className="image-container">
                <a href={mark_dark} rel="noreferrer" target="_blank">
                  <img src={mark_dark} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Vector Light</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_vector_light' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_vector_light')}
              text={mark_vector_light.name || this.getFileName(mark_vector_light)}
            />
            {(!isEmpty(mark_vector_light) || isNil(mark_vector_light)) && isString(mark_vector_light) && (
              <div className="image-container">
                <a href={mark_vector_light} rel="noreferrer" target="_blank">
                  <img src={mark_vector_light} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Vector Dark</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_vector_dark' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_vector_dark')}
              text={mark_vector_dark.name || this.getFileName(mark_vector_dark)}
            />
            {(!isEmpty(mark_vector_dark) || isNil(mark_vector_dark)) && isString(mark_vector_dark) && (
              <div className="image-container">
                <a href={mark_vector_dark} rel="noreferrer" target="_blank">
                  <img src={mark_vector_dark} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Composite Image (Light)</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_composite_light' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_composite_light')}
              text={mark_composite_light.name || this.getFileName(mark_composite_light)}
            />
            {(!isEmpty(mark_composite_light) || isNil(mark_composite_light)) && isString(mark_composite_light) && (
              <div className="image-container">
                <a href={mark_composite_light} rel="noreferrer" target="_blank">
                  <img src={mark_composite_light} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Composite Image (Dark)</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_composite_dark' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_composite_dark')}
              text={mark_composite_dark.name || this.getFileName(mark_composite_dark)}
            />
            {(!isEmpty(mark_composite_dark) || isNil(mark_composite_dark)) && isString(mark_composite_dark) && (
              <div className="image-container">
                <a href={mark_composite_dark} rel="noreferrer" target="_blank">
                  <img src={mark_composite_dark} />
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Vector Composite Light</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_vector_composite_light' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_vector_composite_light')}
              text={mark_vector_composite_light.name || this.getFileName(mark_vector_composite_light)}
            />
            {(!isEmpty(mark_vector_composite_light) || isNil(mark_vector_composite_light)) &&
              isString(mark_vector_composite_light) && (
                <div className="image-container">
                  <a href={mark_vector_composite_light} rel="noreferrer" target="_blank">
                    <img src={mark_vector_composite_light} />
                  </a>
                </div>
              )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Mark Vector Composite Dark</div>
          <div className="form-select-field">
            <FileInput
              inputProps={{ id: 'mark_vector_composite_dark' }}
              onInputChange={event => this.handleImageUpload(event, 'mark_vector_composite_dark')}
              text={mark_vector_composite_dark.name || this.getFileName(mark_vector_composite_dark)}
            />
            {(!isEmpty(mark_vector_composite_dark) || isNil(mark_vector_composite_dark)) &&
              isString(mark_vector_composite_dark) && (
                <div className="image-container">
                  <a href={mark_vector_composite_dark} rel="noreferrer" target="_blank">
                    <img src={mark_vector_composite_dark} />
                  </a>
                </div>
              )}
          </div>
        </div>

        <div className="form-field">
          <div className="label">Last Audited</div>
          <div className="form-select-field">
            <DatePicker
              onChange={onLastAuditedChange}
              showNow
              showTime
              style={{ height: '100%', margin: '0', padding: '0', paddingInline: '5px', width: '100%' }}
              {...(last_audited && { value: moment(last_audited) })}
              //value={last_audited && moment(last_audited)}
            />
          </div>
        </div>

        <div className="form-field">
          <Button className={Classes.INTENT_PRIMARY} disabled={requestProcessing} large onClick={this.onLogoSubmit}>
            {requestProcessing ? (
              <Spinner intent={Intent.PRIMARY} size={SpinnerSize.SMALL} />
            ) : this.props.editRowObject ? (
              'Update'
            ) : (
              'Submit'
            )}
          </Button>{' '}
          <Button className={Classes.INTENT_DANGER} large onClick={this.props.onLogoCancel}>
            Cancel
          </Button>
        </div>
        {error && (
          <div className="bp4-callout modifier bp4-intent-danger">
            <h3>Please fix the following</h3>
            <ul className="errors-ul">{errorContent}</ul>
          </div>
        )}
      </div>
    );
  }
}

export default LogoForm;
