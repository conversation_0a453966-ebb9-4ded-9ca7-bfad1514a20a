'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, FormGroup, InputGroup } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import type { Speaker } from '../types/types';
import styled from 'styled-components';

// Styled components
const CardContent = styled.div`
  padding: 8px;
`;

const EditingForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const FormGrid = styled.div`
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr 1fr;
`;

const ButtonsContainer = styled.div`
  display: flex;
  gap: 8px;
  justify-content: flex-end;
`;

const SpeakerInfo = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
`;

const SpeakerDetails = styled.div``;

const SpeakerName = styled.h3`
  font-weight: 500;
  margin: 0;
`;

const SpeakerRole = styled.p.attrs({ className: 'bp4-text-muted' })`
  margin: 0;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 8px;
`;

interface SpeakerEditorProps {
  speaker: Speaker;
  onUpdate: (speaker: Speaker) => void;
}

export function SpeakerEditor({ onUpdate, speaker }: SpeakerEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState(speaker.name);
  const [role, setRole] = useState(speaker.role);

  const handleSave = () => {
    onUpdate({
      ...speaker,
      name,
      role,
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setName(speaker.name);
    setRole(speaker.role);
    setIsEditing(false);
  };

  return (
    <Card>
      <CardContent>
        {isEditing ? (
          <EditingForm>
            <FormGrid>
              <FormGroup label="Name" labelFor={`speaker-name-${speaker.id}`}>
                <InputGroup id={`speaker-name-${speaker.id}`} onChange={e => setName(e.target.value)} value={name} />
              </FormGroup>
              <FormGroup label="Role" labelFor={`speaker-role-${speaker.id}`}>
                <InputGroup id={`speaker-role-${speaker.id}`} onChange={e => setRole(e.target.value)} value={role} />
              </FormGroup>
            </FormGrid>
            <ButtonsContainer>
              <Button onClick={handleCancel} text="Cancel" />
              <Button intent="primary" onClick={handleSave} text="Save" />
            </ButtonsContainer>
          </EditingForm>
        ) : (
          <SpeakerInfo>
            <SpeakerDetails>
              <SpeakerName>{speaker.name}</SpeakerName>
              <SpeakerRole>{speaker.role}</SpeakerRole>
            </SpeakerDetails>
            <ActionButtons>
              <Button icon={IconNames.EDIT} minimal onClick={() => setIsEditing(true)} />
            </ActionButtons>
          </SpeakerInfo>
        )}
      </CardContent>
    </Card>
  );
}
