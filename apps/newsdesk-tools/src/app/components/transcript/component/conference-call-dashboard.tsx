'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { ConferenceCallList } from './conference-call-list';
import { TranscriptEditor } from './transcript-editor';
import type { <PERSON><PERSON><PERSON>, Speaker as AppSpeaker, TranscriptSegment } from '../types/types';
import { SegmentType } from '../types/types';
import { fetchCallDetails } from '../../../services/conference';
import styled from 'styled-components';
import { Icon } from '@blueprintjs/core';

// Import Blueprint.js core styles
import '@blueprintjs/core/lib/css/blueprint.css';
import '@blueprintjs/icons/lib/css/blueprint-icons.css';
import '@blueprintjs/popover2/lib/css/blueprint-popover2.css';

// Styled components
const DashboardContainer = styled.div.attrs({ className: 'bp4-light' })`
  display: flex;
  flex-direction: column;
  height: 100vh;
`;

const FlexContainer = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
  width: 100%;
`;

const LeftPanel = styled.div`
  border-right: 1px solid rgba(16, 22, 26, 0.4);
  flex: 0 0 100%;
  overflow: auto;
  transition: flex 0.3s ease;
  &.right-panel-visible {
    flex: 0 0 60%;
  }
`;

const RightPanel = styled.div`
  flex: 0 0 40%;
  overflow: hidden;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  &.visible {
    transform: translateX(0);
  }
`;

const CloseButton = styled.button`
  position: relative;
  background: transparent;
  border: none;
  cursor: pointer;
  z-index: 30;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: background-color 0.2s ease;
  border-radius: 0;
  margin: 0;
  padding: 0;

  &:hover {
    background-color: rgba(167, 182, 194, 0.3);
  }

  &:active {
    background-color: rgba(115, 134, 148, 0.3);
  }
`;

const LoadingContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  padding: 20px;
  overflow: hidden;
`;

const LoadingSpinner = styled.div.attrs({ className: 'bp4-spinner bp4-intent-primary' })`
  height: 50px;
  width: 50px;
`;

const LoadingText = styled.p.attrs({ className: 'bp4-text-muted' })`
  font-weight: 500;
  margin-top: 16px;
`;

const LoadingSubText = styled.p.attrs({ className: 'bp4-text-muted bp4-text-small' })``;

const ErrorContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  padding: 20px;
  overflow: hidden;
`;

const EmptyStateMessage = styled.div`
  align-items: center;
  display: flex;
  justify-content: center;
  flex: 1;
  padding: 20px;
  overflow: hidden;
`;

const EmptyStateText = styled.p.attrs({ className: 'bp4-text-muted' })``;

const ResizeHandle = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 20;

  &:hover,
  &.dragging {
    background: rgba(16, 22, 26, 0.1);
  }
`;

// Add a styled header bar for the right panel
const RightPanelHeader = styled.div`
  background: linear-gradient(to right, #f5f8fa, #ebf1f5);
  border-bottom: 1px solid #d8e1e8;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
`;

const RightPanelContent = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

// Internal interfaces for processing API response
interface ApiSpeaker {
  id: string;
  name: string;
  organization: string;
  role: string;
}

export function ConferenceCallDashboard() {
  // Set a default selected call to null initially
  const [selectedCall, setSelectedCall] = useState<ConferenceCall | null>(null);
  // Track the live call ID if any
  const [liveCallId, setLiveCallId] = useState<string | undefined>(undefined);
  // Loading state for call details
  const [loading, setLoading] = useState(false);
  // Error state for API calls
  const [error, setError] = useState<string | null>(null);
  // Tracking current API call to prevent duplicate concurrent requests
  const pendingRequestRef = useRef<string | null>(null);
  const [isRightPanelVisible, setIsRightPanelVisible] = useState(false);
  const [rightPanelWidth, setRightPanelWidth] = useState(40); // Default 40%
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Update liveCallId whenever a call with 'STARTED' status is selected
  useEffect(() => {
    if (
      (selectedCall && selectedCall.status === 'STARTED') ||
      (selectedCall && selectedCall.status === 'IN_PROGRESS')
    ) {
      console.log('Setting live call ID from selected call:', selectedCall.id);
      setLiveCallId(selectedCall.id);
    }
  }, [selectedCall]);

  const handleSelectCall = useCallback(
    async (call: ConferenceCall) => {
      try {
        // Skip API call if call is not completed or if the same call is already selected
        if (call.id === selectedCall?.id) {
          console.log('Call already selected, skipping duplicate fetch');
          return;
        }

        // For live calls, just set the basic info without fetching details
        if (call.status === 'LIVE') {
          console.log('Non-completed call selected, setting basic info:', call.status);
          setSelectedCall(call);
          setIsRightPanelVisible(true);
          return;
        }

        // Check if there's already a pending request for this call
        if (pendingRequestRef.current === call.id) {
          console.log('Request already in progress for call:', call.id);
          return;
        }

        setLoading(true);
        setError(null);
        pendingRequestRef.current = call.id;

        // Fetch detailed call information including transcripts
        const callDetails = await fetchCallDetails(call.id);

        // Extract transcript data
        // Filter transcripts to get non-live transcript
        const transcriptData =
          callDetails.transcripts?.find(t => t.type === 'NON_LIVE') || callDetails.transcripts?.[0] || {};
        const segments = transcriptData.segments || [];
        const speakers: AppSpeaker[] = [];

        // Process segments to extract unique speakers
        const speakersMap = new Map<string, ApiSpeaker>();
        segments.forEach(segment => {
          if (segment.speaker && !speakersMap.has(segment.speaker)) {
            speakersMap.set(segment.speaker, {
              id: `speaker-${speakersMap.size + 1}`,
              name: segment.speaker,
              organization: callDetails.name || '',
              role: '',
            });
          }
        });

        // Add participants info if available
        if (callDetails.participants && callDetails.participants.length > 0) {
          callDetails.participants.forEach(participant => {
            if (participant.name && !speakersMap.has(participant.name)) {
              speakersMap.set(participant.name, {
                id: `speaker-${speakersMap.size + 1}`,
                name: participant.name,
                organization: participant.organization || callDetails.name || '',
                role: participant.role || '',
              });
            } else if (participant.name && speakersMap.has(participant.name)) {
              // Update existing speaker with additional info
              const speaker = speakersMap.get(participant.name);
              if (speaker) {
                speaker.role = participant.role || speaker.role;
                speaker.organization = participant.organization || speaker.organization;
                speakersMap.set(participant.name, speaker);
              }
            }
          });
        }

        // Convert speakers map to array and format for the application
        Array.from(speakersMap.values()).forEach(speaker => {
          speakers.push({
            id: speaker.id,
            name: speaker.name,
            role: speaker.role,
          });
        });

        // Format segment data for the transcript editor
        const formattedSegments: TranscriptSegment[] = segments.map(segment => {
          const startTimeMs = parseInt(segment.start_time, 10) || 0;
          return {
            id: String(segment.segment_id),
            segmentType: segment.type === 'NON_LIVE' ? SegmentType.NON_LIVE : undefined,
            speakerId: speakersMap.get(segment.speaker)?.id || 'unknown',
            text: segment.text || '',
            timestamp: String(startTimeMs),
          };
        });

        // Update the call with detailed transcript information
        const enhancedCall: ConferenceCall = {
          ...call,
          callTitle: callDetails.call_title || call.callTitle,
          companyName: callDetails.name || call.companyName,
          createdAt: callDetails.created_at || call.createdAt,
          duration: callDetails.duration || call.duration,
          endTime: callDetails.end_time
            ? new Date(callDetails.end_time).toISOString().split('T')[1].substring(0, 8)
            : call.endTime,
          exchange: callDetails.exchange || call.exchange,
          headline: callDetails.headline || call.headline,
          startTime: callDetails.start_time
            ? new Date(callDetails.start_time).toISOString().split('T')[1].substring(0, 8)
            : call.startTime,
          status: callDetails.status || call.status,
          // Add summary data - pass it through directly without restructuring
          summary: callDetails.summary || undefined,
          ticker: callDetails.symbol || call.ticker,
          transcript: {
            segments: formattedSegments,
            speakers: speakers,
          },
          updatedAt: callDetails.updated_at || call.updatedAt,
        };

        setSelectedCall(enhancedCall);
        setIsRightPanelVisible(true);
      } catch (err) {
        console.error('Error fetching call details:', err);
        setError('Failed to load call details');
        // Still set the basic call info without the transcript details
        setSelectedCall(call);
        setIsRightPanelVisible(true);
      } finally {
        setLoading(false);
        pendingRequestRef.current = null;
      }
    },
    [selectedCall],
  );

  const handleCloseRightPanel = () => {
    setIsRightPanelVisible(false);
  };

  // Event handler for keyboard events to close the right panel with ESC key
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isRightPanelVisible) {
        handleCloseRightPanel();
      }
    },
    [isRightPanelVisible],
  );

  // Add and remove event listener for ESC key
  useEffect(() => {
    if (isRightPanelVisible) {
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isRightPanelVisible, handleKeyDown]);

  const isSelectedCallLive = selectedCall?.id === liveCallId;

  console.log('Dashboard state:', {
    isSelectedCallLive,
    liveCallId,
    selectedCallId: selectedCall?.id,
    selectedCallStatus: selectedCall?.status,
  });

  // Drag handlers for resizing
  const handleDragStart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDrag = useCallback(
    (e: MouseEvent) => {
      if (isDragging && containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        const mouseX = e.clientX;
        const containerRect = containerRef.current.getBoundingClientRect();

        // Calculate percentage of container width
        const leftPanelWidthPercentage = ((mouseX - containerRect.left) / containerWidth) * 100;
        const rightPanelWidthPercentage = 100 - leftPanelWidthPercentage;

        // Apply constraints (min 20%, max 80% for right panel)
        if (rightPanelWidthPercentage >= 20 && rightPanelWidthPercentage <= 80) {
          setRightPanelWidth(rightPanelWidthPercentage);
        }
      }
    },
    [isDragging],
  );

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Add and remove event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDrag);
      window.addEventListener('mouseup', handleDragEnd);
    }

    return () => {
      window.removeEventListener('mousemove', handleDrag);
      window.removeEventListener('mouseup', handleDragEnd);
    };
  }, [isDragging, handleDrag, handleDragEnd]);

  return (
    <DashboardContainer>
      <FlexContainer ref={containerRef}>
        <LeftPanel
          className={isRightPanelVisible ? 'right-panel-visible' : ''}
          style={isRightPanelVisible ? { flex: `0 0 ${100 - rightPanelWidth}%` } : undefined}
        >
          <ConferenceCallList
            liveCallId={liveCallId}
            onSelectCall={handleSelectCall}
            selectedCallId={selectedCall?.id}
          />
        </LeftPanel>
        <RightPanel
          className={isRightPanelVisible ? 'visible' : ''}
          style={isRightPanelVisible ? { flex: `0 0 ${rightPanelWidth}%` } : undefined}
        >
          {isRightPanelVisible && (
            <>
              <ResizeHandle className={isDragging ? 'dragging' : ''} onMouseDown={handleDragStart} />
              <RightPanelHeader>
                <CloseButton onClick={handleCloseRightPanel} title="Close panel">
                  <Icon color="#5C7080" icon="cross" size={16} />
                </CloseButton>
              </RightPanelHeader>
              <RightPanelContent>
                {loading ? (
                  <LoadingContainer>
                    <LoadingSpinner>
                      <div className="bp4-spinner-animation"></div>
                    </LoadingSpinner>
                    <LoadingText>Loading transcript data...</LoadingText>
                    <LoadingSubText>Please wait while we fetch the call details</LoadingSubText>
                  </LoadingContainer>
                ) : error ? (
                  <ErrorContainer>
                    <div className="bp4-non-ideal-state">
                      <div className="bp4-non-ideal-state-visual">
                        <span className="bp4-icon bp4-icon-error"></span>
                      </div>
                      <h4 className="bp4-heading">{error}</h4>
                    </div>
                  </ErrorContainer>
                ) : selectedCall ? (
                  <TranscriptEditor call={selectedCall} isLive={isSelectedCallLive} />
                ) : (
                  <EmptyStateMessage>
                    <EmptyStateText>Select a conference call to see its transcript</EmptyStateText>
                  </EmptyStateMessage>
                )}
              </RightPanelContent>
            </>
          )}
        </RightPanel>
      </FlexContainer>
    </DashboardContainer>
  );
}
