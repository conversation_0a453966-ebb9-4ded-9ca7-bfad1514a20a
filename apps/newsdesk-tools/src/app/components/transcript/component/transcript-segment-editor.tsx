'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, TextArea, Icon, HTMLSelect } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import type { Speaker, TranscriptSegment } from '../types/types';
import styled from 'styled-components';

// Styled components
const SegmentCard = styled(Card)<{ $speakerColor: string }>`
  border-left: 4px solid ${props => props.$speakerColor};
  transition: all 0.2s;
`;

const CardContent = styled.div`
  padding: 16px;
`;

const ContentContainer = styled.div`
  align-items: flex-start;
  display: flex;
  gap: 16px;
`;

const MainContent = styled.div`
  width: 100%;
`;

const Header = styled.div`
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
`;

const SpeakerContainer = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
`;

const SpeakerInfo = styled.div``;

const SpeakerName = styled.span<{ $color: string }>`
  color: ${props => props.$color};
  font-weight: 500;
`;

const SpeakerRole = styled.span.attrs({ className: 'bp4-text-muted' })`
  margin-left: 8px;
`;

const TimestampContainer = styled.div.attrs({ className: 'bp4-text-muted' })`
  align-items: center;
  display: flex;
  font-size: 12px;
`;

const TimestampIcon = styled(Icon)`
  margin-right: 4px;
`;

const TextContainer = styled.div`
  white-space: pre-wrap;
`;

const EditorButtons = styled.div`
  display: flex;
  gap: 8px;
  justify-content: flex-end;
`;

const SegmentTextArea = styled(TextArea)`
  margin-bottom: 8px;
  min-height: 100px;
`;

interface TranscriptSegmentEditorProps {
  segment: TranscriptSegment;
  speakers: Speaker[];
  onUpdate: (segment: TranscriptSegment) => void;
}

export function TranscriptSegmentEditor({ onUpdate, segment, speakers }: TranscriptSegmentEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [speakerId, setSpeakerId] = useState(segment.speakerId);
  const [text, setText] = useState(segment.text);

  const speaker = speakers.find(s => s.id === segment.speakerId);

  // Generate a consistent color based on speaker ID
  const getSpeakerColor = (id: string) => {
    const colors = [
      '#137CBD', // Blue
      '#D9822B', // Orange
      '#0F9960', // Green
      '#C274C2', // Purple
      '#D13913', // Red
    ];

    // Use the first character of the ID to determine the color
    const index = id.charCodeAt(0) % colors.length;
    return colors[index];
  };

  const handleSave = () => {
    onUpdate({
      ...segment,
      speakerId,
      text,
    });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setSpeakerId(segment.speakerId);
    setText(segment.text);
    setIsEditing(false);
  };

  const speakerColor = speaker ? getSpeakerColor(speaker.id) : 'gray';

  return (
    <SegmentCard $speakerColor={speakerColor}>
      <CardContent>
        <ContentContainer>
          <MainContent>
            <Header>
              <SpeakerContainer>
                {isEditing ? (
                  <HTMLSelect onChange={e => setSpeakerId(e.target.value)} style={{ width: '200px' }} value={speakerId}>
                    {speakers.map(s => (
                      <option key={s.id} value={s.id}>
                        {s.name} ({s.role})
                      </option>
                    ))}
                  </HTMLSelect>
                ) : (
                  <SpeakerInfo>
                    <SpeakerName $color={speakerColor}>{speaker?.name}</SpeakerName>
                    <SpeakerRole>({speaker?.role})</SpeakerRole>
                  </SpeakerInfo>
                )}
                <TimestampContainer>
                  <TimestampIcon icon={IconNames.TIME} size={12} />
                  <span>{segment.timestamp}</span>
                </TimestampContainer>
              </SpeakerContainer>
              {!isEditing && <Button icon={IconNames.EDIT} minimal onClick={() => setIsEditing(true)} />}
            </Header>

            {isEditing ? (
              <>
                <SegmentTextArea
                  fill
                  growVertically
                  onChange={e => setText(e.target.value)}
                  rows={text.split('\n').length + 2}
                  value={text}
                />
                <EditorButtons>
                  <Button onClick={handleCancel} text="Cancel" />
                  <Button intent="primary" onClick={handleSave} text="Save" />
                </EditorButtons>
              </>
            ) : (
              <TextContainer>{segment.text}</TextContainer>
            )}
          </MainContent>
        </ContentContainer>
      </CardContent>
    </SegmentCard>
  );
}
