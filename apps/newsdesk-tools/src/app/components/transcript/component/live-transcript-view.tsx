'use client';

import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { Card, Tag, Icon, Elevation } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import type { ConferenceCall } from '../types/types';
import styled from 'styled-components';

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const Header = styled.div`
  align-items: center;
  border-bottom: 1px solid rgba(16, 22, 26, 0.4);
  display: flex;
  justify-content: space-between;
  padding: 16px;
`;

const HeaderLeft = styled.div`
  align-items: center;
  display: flex;
  gap: 8px;
`;

const LiveTag = styled(Tag)`
  align-items: center;
  display: inline-flex;
  gap: 4px;
`;

const TimeDisplay = styled.span.attrs({ className: 'bp4-text-muted' })`
  align-items: center;
  display: flex;
  gap: 4px;
`;

const TranscriptContainer = styled.div`
  flex: 1;
  overflow: auto;
  padding: 24px;
`;

const WaitingContainer = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 48px 0;
  text-align: center;
`;

const PhoneIcon = styled(Icon).attrs({ className: 'bp4-text-muted' })`
  margin-bottom: 16px;
`;

const TranscriptText = styled.p`
  white-space: pre-wrap;
`;

interface LiveTranscriptViewProps {
  call: ConferenceCall;
  onCallComplete: () => void;
}

// Keep track of active websocket connections by call ID to prevent duplicates
const activeWebSockets = new Map<string, WebSocket>();

// Regex for punctuation detection - compiled once for better performance
const PUNCTUATION_REGEX = /^[.,:;?!]$/;
const PUNCTUATION_SPLIT_REGEX = /^(.*?)([.,:;?!]+)$/;

export function LiveTranscriptView({ call, onCallComplete }: LiveTranscriptViewProps) {
  const [elapsedTime, setElapsedTime] = useState(0); // Start at 0:00:00 instead of 1:15
  const transcriptRef = useRef<HTMLDivElement>(null);
  const [transcript, setTranscript] = useState('');
  const wsRef = useRef<WebSocket | null>(null);
  const queueRef = useRef<string[]>([]);
  const typingRef = useRef<boolean>(false);
  const previousSegmentsProcessedRef = useRef<boolean>(false);

  // Function to format elapsed time as HH:MM:SS - memoized for performance
  const formatElapsedTime = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Process a sentence into words and punctuation
  const processSentence = useCallback((sentence: string): string[] => {
    const words: string[] = [];
    const spaceSplit = sentence
      .trim()
      .split(/\s+/)
      .filter(w => w.length > 0);

    spaceSplit.forEach(chunk => {
      const match = chunk.match(PUNCTUATION_SPLIT_REGEX);
      if (match) {
        if (match[1]) words.push(match[1]);
        match[2].split('').forEach(p => words.push(p));
      } else {
        words.push(chunk);
      }
    });

    return words;
  }, []);

  const typeWord = useCallback(async () => {
    if (typingRef.current || queueRef.current.length === 0) return;
    typingRef.current = true;

    const word = queueRef.current.shift();
    if (word) {
      // Check if this is a punctuation mark
      const isPunctuation = PUNCTUATION_REGEX.test(word);
      console.log(`Typing word: "${word}", isPunctuation: ${isPunctuation}`);

      setTranscript(prev => {
        if (isPunctuation) {
          // For punctuation, don't add space before it
          return prev + word + ' ';
        } else {
          // For normal words, add space if needed
          const needsSpace = prev.length > 0 && !prev.endsWith(' ');
          return prev + (needsSpace ? ' ' : '') + word;
        }
      });

      // Adjust delay based on word type - longer pause after punctuation
      const delayTime = isPunctuation ? 500 : 200;
      console.log(`Delaying for ${delayTime}ms`);
      try {
        await new Promise(resolve => setTimeout(resolve, delayTime));
        console.log(`Delay of ${delayTime}ms completed`);
      } catch (err) {
        console.error('Error in delay:', err);
      }
    }

    typingRef.current = false;

    // Process next word if available
    if (queueRef.current.length > 0) {
      console.log(`Queue has ${queueRef.current.length} more words, scheduling next word`);
      setTimeout(typeWord, 10);
    }
  }, []);

  useEffect(() => {
    // Check if there's already an active websocket for this call
    if (activeWebSockets.has(call.id)) {
      console.log('Reusing existing WebSocket connection for call:', call.id);
      wsRef.current = activeWebSockets.get(call.id) || null;
      return;
    }

    console.log('Creating new WebSocket connection for call:', call.id);
    const ws = new WebSocket(`${global.env.TRANSCRIPT_WS}/stream?token=${global.env.TRANSCRIPT_V1_TOKEN}`);

    wsRef.current = ws;
    activeWebSockets.set(call.id, ws);

    ws.onopen = () => {
      console.log('WebSocket connected');
      ws.send(
        JSON.stringify({
          action: 'subscribe',
          previousSegments: true,
          ticker: call.ticker,
        }),
      );
    };

    ws.onmessage = event => {
      try {
        const data = JSON.parse(event.data);

        // Handle previousSegments if they exist and haven't been processed yet
        if (
          !previousSegmentsProcessedRef.current &&
          data?.previousSegments &&
          Array.isArray(data.previousSegments) &&
          data.previousSegments.length > 0
        ) {
          console.log('Received previous segments:', data.previousSegments.length);

          // Combine all previous segments into one initial transcript
          const initialTranscript = data.previousSegments
            .map(segment => segment.text || '')
            .filter(text => text.trim() !== '')
            .join(' ');

          if (initialTranscript) {
            setTranscript(initialTranscript);
            previousSegmentsProcessedRef.current = true;
          }
        }

        // Process new sentence if it exists
        const sentence = data?.sentence;
        if (!sentence) return;

        // Process the sentence into words and add to queue
        const words = processSentence(sentence);
        queueRef.current.push(...words);

        typeWord();
      } catch (err) {
        console.error('WebSocket error:', err);
      }
    };

    ws.onerror = error => {
      console.error('WebSocket error:', error);
      activeWebSockets.delete(call.id);
    };

    ws.onclose = () => {
      console.log('WebSocket closed');
      activeWebSockets.delete(call.id);
      previousSegmentsProcessedRef.current = false;
      onCallComplete();
    };

    // Start timer to increase elapsed time
    const timer = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    // Cleanup on component unmount or when popup closes
    return () => {
      clearInterval(timer);
      previousSegmentsProcessedRef.current = false;

      // Only close the connection if this component is unmounting
      // and not just being hidden temporarily
      if (wsRef.current && !document.querySelector(`[data-call-id="${call.id}"]`)) {
        console.log('Component unmounting, closing WebSocket...');
        wsRef.current.close();
        activeWebSockets.delete(call.id);
        wsRef.current = null;
      }
    };
  }, [call.id, call.ticker, onCallComplete, typeWord, processSentence]);

  // Auto-scroll to bottom when new content is added
  useEffect(() => {
    if (transcriptRef.current) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
    }
  }, [transcript]);

  // Memoized empty transcript check
  const isTranscriptEmpty = useMemo(() => !transcript || transcript.trim() === '', [transcript]);

  return (
    <Container data-call-id={call.id}>
      <Header>
        <HeaderLeft>
          <LiveTag intent="danger" minimal>
            <Icon color="#F55656" icon={IconNames.Pulse} intent="success" />
            LIVE
          </LiveTag>
          <TimeDisplay>
            <Icon icon={IconNames.TIME} size={12} />
            {formatElapsedTime(elapsedTime)}
          </TimeDisplay>
        </HeaderLeft>
        <div className="bp4-text-muted">Raw transcription in progress...</div>
      </Header>

      <TranscriptContainer ref={transcriptRef}>
        <Card elevation={Elevation.ONE} style={{ padding: '16px' }}>
          {isTranscriptEmpty ? (
            <WaitingContainer>
              <PhoneIcon icon={IconNames.PHONE} size={48} />
              <h3 className="bp4-heading">Waiting for call to begin...</h3>
              <p className="bp4-text-muted">The transcript will appear here once the call starts.</p>
            </WaitingContainer>
          ) : (
            <TranscriptText>{transcript}</TranscriptText>
          )}
        </Card>
      </TranscriptContainer>
    </Container>
  );
}
