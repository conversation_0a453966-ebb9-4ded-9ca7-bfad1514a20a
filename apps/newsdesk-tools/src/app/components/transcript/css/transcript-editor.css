.transcript-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.transcript-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.bp4-tab-panel {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.tab-panel-content {
  height: 100%;
  overflow-y: auto;
  padding: 24px;
}

/* Fix for dialog mode */
.bp4-dialog .tab-panel-content {
  max-height: calc(100vh - 200px);
  overflow: auto;
}

.transcript-segments {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
}

.empty-state {
  align-items: center;
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  text-align: center;
}

.empty-state-icon {
  margin-bottom: 16px;
  opacity: 0.7;
}

.speakers-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.speakers-panel h3 {
  margin-bottom: 16px;
}

.speakers-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.empty-speakers {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 32px;
  text-align: center;
}

.add-speaker-btn {
  margin-top: 16px;
}

.details-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.details-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr 1fr;
}

.transcript-fullscreen-dialog .bp4-tabs {
  height: 100%;
}

.transcript-fullscreen-dialog .transcript-editor-container {
  height: 100%;
}

.transcript-fullscreen-dialog .bp4-dialog-body {
  padding: 0;
}

/* New styles for seamless layout in dashboard */
.transcript-section > div {
  height: 100%;
  padding: 0; /* Remove padding from transcript section */
}

.transcript-section .bp4-card {
  border-radius: 0 0 3px 0; /* Only round the bottom-right corner */
  border-color: rgba(16, 22, 26, 0.3);
  border-style: solid;
  border-width: 0 1px 1px 0; /* Only add right and bottom borders */
  height: calc(100% - 74px); /* Account for the header height */
  margin: 74px 16px 16px 0; /* Match margin with grid section, but offset for header */
}

.transcript-editor-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

.transcript-header {
  align-items: center;
  border-top: 1px solid rgba(16, 22, 26, 0.3);
  border-right: 1px solid rgba(16, 22, 26, 0.3);
  border-radius: 0 3px 0 0;
  display: flex;
  justify-content: space-between;
  margin: 0 16px 16px 0;
  padding: 16px 24px;
}

/* Add padding to the first tab */
.transcript-tabs .bp4-tab-list {
  padding-left: 16px;
}

/* Dialog title and subtitle styling */
.dialog-subtitle {
  color: #5c7080;
  font-size: 14px;
  font-style: italic;
  font-weight: normal;
  margin-top: 4px;
}

/* Enhanced button styling */
.transcript-editor-container .bp4-button {
  box-shadow: 0 1px 2px rgba(16, 22, 26, 0.1);
  transition: all 0.2s ease;
}

.transcript-editor-container .bp4-button:hover {
  box-shadow: 0 2px 4px rgba(16, 22, 26, 0.2);
  transform: translateY(-1px);
}

.transcript-editor-container .bp4-button.bp4-intent-primary {
  background-image: linear-gradient(to bottom, #2b95d6, #2d72d2);
}

.transcript-editor-container .bp4-button.bp4-intent-primary:hover {
  background-image: linear-gradient(to bottom, #2b95d6, #166bcc);
}
