import * as React from 'react';
import { Component, createRef, RefObject } from 'react';
import { connect } from 'react-redux';

import { Button, Dialog, Icon, Intent, Slider } from '@blueprintjs/core';
import { IconNames } from '@blueprintjs/icons';
import classnames from 'classnames';
import { reject, round, size } from 'lodash';
import { addIndex, equals, isNil, map } from 'ramda';

import { Subscription } from 'rxjs';

import * as SquawkSDK from '@benzinga/benzinga-squawk-sdk';
import Select from 'react-select';
import { RootState } from '../../redux/types';
import { selectSession } from '../../selectors/sessionSelectors';
import siren from '../../../assets/static/audio/siren.wav';
import { makeIdGenerator } from '../utils';
import AudioVisualizer from './AudioVisualizer';
import { Radio, RadioChangeEvent, Space } from 'antd';

interface Log {
  id: number;
  message: string;
  type: 'info' | 'error';
}

enum BroadcasterState {
  initial = 'initial',
  connected = 'connected',
  connecting = 'connecting',
  broadcasting = 'broadcasting',
  broadcastReconnecting = 'broadcastReconnecting',
}

interface State {
  areChannelsFetched: boolean;
  broadcasterState: BroadcasterState;
  channelOptions: Channel[];
  isMuted: boolean;
  isProcessing: boolean;
  logs: Log[];
  otherBroadcasters: Presenter[];
  showChannelChangeConfirmation: boolean;
  volumeLevel: number;
  wasBroadcasting: boolean;
  pushToTalk: boolean;
  altKeyPress: boolean;
}

interface StateProps {
  token: string;
  username: string;
}

interface Channel {
  data: SquawkSDK.Stream;
  label: string;
  value: number;
}

interface Presenter {
  streamId: number;
  userId: string;
  username: string;
}

type Props = StateProps;

const formatMessage = (...args) =>
  args.reduce((message, arg) => {
    let value = arg;
    if (typeof arg === 'object') {
      value = arg instanceof Error ? arg.message : JSON.stringify(arg);
    }
    return `${message} ${value}`;
  }, '');

const transportConfig = {
  connectionTimeoutInMs: 5000 + 10000,

  // 15 second
  requestTimeoutInMs: 10000,

  serverAddress: global.env.SQUAWK_ADDR,
};

const resilienceConfig = {
  maxRetry: 0,
  maxRetryIntervalInMs: 10000,
  retryIntervalBackoffInMs: 1000,
  retryIntervalInMs: 5000,
};

class Squawk extends Component<Props, Readonly<State>> {
  streams: SquawkSDK.Stream[] = [];
  selectedChannel: Channel;
  broadcasterAudioEle: RefObject<HTMLAudioElement> = createRef();
  subscriptions: [Subscription, Subscription];
  nextChangingChannel: Channel;
  localBroadcasterStream: MediaStream;
  clientConsumer: SquawkSDK.SquawkJS;
  clientBroadcaster: SquawkSDK.SquawkJS;
  listenerAudioEle: RefObject<HTMLAudioElement> = createRef();
  channelId?: number = undefined;
  broadcasterState: BroadcasterState;
  alarmAudio: HTMLAudioElement;
  generateId = makeIdGenerator();
  buttonPressTimer: NodeJS.Timeout;

  constructor(props: Props) {
    super(props);

    this.state = {
      altKeyPress: false,
      areChannelsFetched: false,
      broadcasterState: BroadcasterState.initial,
      channelOptions: [],
      isMuted: false,
      isProcessing: false,
      logs: [],
      otherBroadcasters: [],
      pushToTalk: false,
      showChannelChangeConfirmation: false,
      volumeLevel: 0,
      wasBroadcasting: false,
    };
    this.handleButtonPress = this.handleButtonPress.bind(this);
    this.handleButtonRelease = this.handleButtonRelease.bind(this);
  }

  async componentDidMount() {
    const { volumeLevel } = this.state;
    if (this.listenerAudioEle.current) {
      this.listenerAudioEle.current.volume = volumeLevel;
    }
    this.clientBroadcaster = new SquawkSDK.Builder(resilienceConfig, transportConfig, this.broadcasterCallbacks(this))
      .asBroadcaster()
      .withSession()
      .build();
    this.clientConsumer = new SquawkSDK.Builder(resilienceConfig, transportConfig, this.listenerCallbacks(this))
      .asListener()
      .withSession()
      .build();
    await this.initBroadcasterSDK();
    await this.initListenerSDK();
    this.alarmAudio = new Audio(siren);
    this.alarmAudio.preload = 'auto';
    window.addEventListener('keydown', this.handleKeyPressHold);
    window.addEventListener('keyup', this.handleKeyPressLeave);
  }

  componentWillUnmount() {
    this.dispose();
    window.removeEventListener('keydown', this.handleKeyPressHold);
    window.removeEventListener('keyup', this.handleKeyPressLeave);
  }

  async initBroadcasterSDK() {
    try {
      const { token } = this.props;
      await this.clientBroadcaster.initialize(token);
      this.log('Broadcaster initialized successfully');
      await this.initChannels();
      const acitveBroadcasters = this.clientBroadcaster.getExistingPresenters();
      this.setState({ otherBroadcasters: this.state.otherBroadcasters.concat(acitveBroadcasters) });
    } catch (err) {
      this.error('Failed to initialize Squawk Broadcaster after max attempts:', resilienceConfig.maxRetry);
    }
  }

  async initListenerSDK() {
    try {
      const { token } = this.props;
      await this.clientConsumer.initialize(token);
      this.log('Listener initialized successfully');
      await this.initChannels();
    } catch (err) {
      this.error('Failed to initialize Squawk Listener after max attempts:', resilienceConfig.maxRetry);
    }
  }

  async initChannels() {
    const { areChannelsFetched } = this.state;
    if (areChannelsFetched) {
      return;
    }
    try {
      this.log('Fetching list of channels');
      const availableChannel = await this.clientBroadcaster.getAvailableChannels();
      const toChannel = (stream: SquawkSDK.Stream) => {
        return {
          data: stream,
          label: stream.description,
          value: stream.id,
        };
      };
      const channelOptions = map(
        toChannel,
        availableChannel.streams.filter(c => c.allowedBroadcasting),
      );
      this.setState({ areChannelsFetched: true, channelOptions });
      this.log('Got channels list');
    } catch (err) {
      this.error('Failed to fetch available channels', err);
    }
  }

  broadcasterCallbacks = (context: Squawk): SquawkSDK.PublisherCallback => {
    const callback: SquawkSDK.PublisherCallback = {
      onBroadcastOverride(): void {
        context.handleBroadcastOverride();
      },
      onBroadcasterLeft(_: string, channelId: number, userId: string): void {
        if (channelId === context.channelId) {
          context.setState({ otherBroadcasters: reject(context.state.otherBroadcasters, { userId }) });
        }
      },
      onLocalStream(localStream: MediaStream): void {
        context.localBroadcasterStream = localStream;
      },
      onNewBroadcaster(name: string, channelId: number, userId: string): void {
        if (channelId === context.channelId) {
          const newPresenter: Presenter = {
            streamId: channelId,
            userId,
            username: name,
          };
          context.setState({ otherBroadcasters: context.state.otherBroadcasters.concat(newPresenter) });
        }
      },
      onRTCStateChange(rtcState: SquawkSDK.RTCState, channelId: number): void {
        if (channelId === context.channelId) {
          context.handleBroadcasterRTCStateChange(rtcState);
        }
      },
      onTransportStateChange(transportState: SquawkSDK.TransportState): void {
        context.handleBroadcasterTransportChange(transportState);
      },
    };
    return callback;
  };

  listenerCallbacks = (context: Squawk): SquawkSDK.ListenerCallback => {
    const callback: SquawkSDK.ListenerCallback = {
      onMediaOverride(channelId: number): void {
        if (channelId === context.channelId) {
          context.handleListenerMediaOverride(channelId);
        }
      },
      onPresenterStateChange(presenterState: SquawkSDK.PresenterState, channelId: number): void {
        if (channelId === context.channelId) {
          context.log('Presenter state change: ', presenterState);
        }
      },
      onRTCStateChange(rtcState: SquawkSDK.RTCState, channelId: number): void {
        if (channelId === context.channelId) {
          context.log('Listener RTC state change: ', rtcState);
          if (equals(rtcState, SquawkSDK.RTCState.failed)) {
            context.log('Reconnecting listener rtcState...', rtcState);
          }
        }
      },
      onRemoteStream(e: MediaStream, channelId: number): void {
        if (channelId === context.channelId) {
          if (context.listenerAudioEle.current) {
            context.listenerAudioEle.current.srcObject = e;
          }
        }
      },
      onTransportStateChange(transportState: SquawkSDK.TransportState): void {
        context.handleListenerTransportChange(transportState);
      },
    };
    return callback;
  };

  handleListenerTransportChange = async (transportState: SquawkSDK.TransportState) => {
    if (equals(transportState, SquawkSDK.TransportState.connected)) {
      if (!isNil(this.channelId)) {
        this.startListening();
      }
      // eslint-disable-next-line no-restricted-globals
    } else if (equals(status, SquawkSDK.TransportState.disconnected)) {
      await this.disposeConsumer();
      await this.initListenerSDK();
    }
  };

  handleBroadcasterTransportChange = async (transportState: SquawkSDK.TransportState) => {
    if (equals(transportState, SquawkSDK.TransportState.connected)) {
      const { wasBroadcasting } = this.state;
      if (wasBroadcasting) {
        this.setBroadcasting(true);
      }
    } else if (equals(transportState, SquawkSDK.TransportState.disconnected)) {
      await this.disposeBroadcaster();
      this.setState({ broadcasterState: BroadcasterState.broadcastReconnecting, isProcessing: true });
      this.setOffAlarm();
      await this.initBroadcasterSDK();
    }
  };

  setOffAlarm = () => {
    this.alarmAudio.play();
    setTimeout(() => {
      const { broadcasterState } = this.state;
      if (broadcasterState === BroadcasterState.broadcastReconnecting) {
        this.setOffAlarm();
      }
    }, 3000);
  };

  handleListenerMediaOverride = (channelId: number) => {
    this.log('This account started playing from another session, channel Id:', channelId);
  };

  handleBroadcasterRTCStateChange = (rtcState: SquawkSDK.RTCState) => {
    if (equals(rtcState, SquawkSDK.RTCState.failed)) {
      this.setOffAlarm();
      this.setState(prevState => {
        if (!equals(prevState.broadcasterState, BroadcasterState.broadcastReconnecting)) {
          return { broadcasterState: BroadcasterState.broadcastReconnecting, isProcessing: true };
        }
        return null;
      });
    } else if (equals(rtcState, SquawkSDK.RTCState.connected)) {
      this.setState(prevState => {
        if (!equals(prevState.broadcasterState, BroadcasterState.broadcasting)) {
          const { isMuted, wasBroadcasting } = this.state;
          if (wasBroadcasting && isMuted) {
            this.clientBroadcaster.muteChannel(this.channelId);
          }
          return {
            broadcasterState: BroadcasterState.broadcasting,
            isMuted: wasBroadcasting ? isMuted : false,
            isProcessing: false,
            wasBroadcasting: true,
          };
        }
        return null;
      });
    }
  };

  addLog(type, ...args) {
    let { logs } = this.state;
    if (logs.length > 500) {
      logs = logs.slice(200);
    }
    this.setState({ logs: logs.concat({ id: this.generateId(), message: formatMessage(...args), type }) });
    /* tslint:disable-next-line:no-console */
    console.log(...args);
  }

  log = (...args) => {
    this.addLog('info', ...args);
  };

  error = (message, error) => {
    this.addLog('error', message, ': ', error);
  };

  startListening = async () => {
    if (isNil(this.channelId)) {
      this.log('No channel selected. Please select a channel!');
      return;
    }
    try {
      this.listenerAudioEle.current.play();
      await this.clientConsumer.subscribeChannel(this.channelId);
      this.log('Started listening to to channel, channel Id: ', this.channelId);
    } catch (err) {
      this.error('Failed to start listening to channel, channel Id: ' + this.channelId, err);
    }
  };

  stopListening = async () => {
    if (isNil(this.channelId)) {
      this.log('Not connected to any channel!');
      return;
    }
    try {
      await this.clientConsumer.unsubscribeChannel(this.channelId);
      this.log('Stopped listening to channel, channelId: ' + this.channelId);
    } catch (err) {
      this.error('Failed to stop listening to channel, channel Id: ' + this.channelId, err);
    }
  };

  getVolume() {
    return this.state.volumeLevel;
  }

  updateState = (expectedState: BroadcasterState) => (update: () => Partial<State>) => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    this.setState(({ broadcasterState }: State) => {
      if (broadcasterState !== expectedState) {
        return null;
      }
      return update();
    });
  };

  setBroadcasting = (isReconnecting: boolean) => {
    if (!this.channelId) {
      this.error('Error broadcasting', 'no channel connected');
      return;
    }
    this.setState({ isProcessing: true }, async () => {
      const updateState = this.updateState(
        isReconnecting ? BroadcasterState.broadcastReconnecting : BroadcasterState.connected,
      );
      const { isMuted } = this.state;
      try {
        this.broadcasterAudioEle.current.play();
        await this.clientBroadcaster.startBroadcasting(this.channelId);
        updateState(() => ({
          broadcasterState: BroadcasterState.broadcasting,
          isMuted: isReconnecting ? isMuted : false,
          isProcessing: false,
          pushToTalk: true,
          wasBroadcasting: true,
        }));
        this.clientBroadcaster.muteChannel(this.channelId);
        this.log('Started broadcasting to channel, channel Id: ', this.channelId);
      } catch (err) {
        this.error('Error broadcasting', err);
        updateState(() => ({ isProcessing: false }));
      }
    });
  };

  stopBroadcast = () => {
    if (!this.channelId) {
      this.error('Error broadcasting', 'no channel connected');
      return;
    }
    this.setState({ isProcessing: true }, async () => {
      const updateState = this.updateState(BroadcasterState.broadcasting);
      try {
        await this.clientBroadcaster.stopBroadcasting(this.channelId);
        this.log('Stopped broadcasting to channel, channelId: ' + this.channelId);
        updateState(() => ({
          broadcasterState: BroadcasterState.connected,
          isProcessing: false,
          wasBroadcasting: false,
        }));
      } catch (err) {
        updateState(() => ({ isProcessing: false }));
        this.error('Failed to stop broadcasting', err);
      }
    });
  };

  handleBroadcastOverride() {
    this.log('Received broadcastOverride. Started broadcasting from another session.');
    this.setState({ broadcasterState: BroadcasterState.initial, isProcessing: false }, () => {
      this.selectedChannel = null;
    });
  }

  toggleMute = () => {
    this.setState(
      prevState => ({ isMuted: !prevState.isMuted }),
      () => {
        const { isMuted } = this.state;
        try {
          if (isMuted) {
            this.clientBroadcaster.muteChannel(this.channelId);
          } else {
            this.clientBroadcaster.unMuteChannel(this.channelId);
          }
        } catch (e) {
          this.error('Failed to mute broadcasting channel', e);
        }
      },
    );
  };

  async dispose() {
    await this.clientBroadcaster.stop();
    await this.clientConsumer.stop();
  }

  async disposeConsumer() {
    try {
      await this.clientConsumer.stop();
    } catch (e) {
      this.error('Failed to dispose listener client', e);
    }
  }

  async disposeBroadcaster() {
    try {
      await this.clientBroadcaster.stop();
    } catch (e) {
      this.error('Failed to dispose broadcaster client', e);
    }
  }

  onVolumeChange = volumeLevel => {
    this.setState({ volumeLevel }, () => {
      if (this.listenerAudioEle.current) {
        this.listenerAudioEle.current.volume = volumeLevel;
      }
    });
  };

  getBroadcastAction() {
    switch (this.state.broadcasterState) {
      case BroadcasterState.connecting:
        return 'Connecting';

      case BroadcasterState.connected:
        return 'Start broadcast';

      case BroadcasterState.initial:
        return 'Not Connected';

      case BroadcasterState.broadcastReconnecting:
        return 'Reconnecting';

      default:
        break;
    }
  }

  isBroadcasting() {
    return this.state.broadcasterState === BroadcasterState.broadcasting;
  }

  isBroadcastDisabled() {
    const { broadcasterState, isProcessing } = this.state;
    return equals(broadcasterState, BroadcasterState.initial) || isProcessing;
  }

  joinChannel = (channel: Channel) => {
    this.selectedChannel = channel;
    if (channel.value !== this.channelId) {
      const { wasBroadcasting } = this.state;
      this.setState({ isProcessing: true }, async () => {
        const updateState = this.updateState(BroadcasterState.connecting);
        if (!isNil(this.channelId)) {
          await this.stopListening();
          if (wasBroadcasting) {
            await this.stopBroadcast();
          }
        }
        this.channelId = channel.value;
        this.startListening();
        updateState(() => ({
          broadcasterState: BroadcasterState.connected,
          isProcessing: false,
          wasBroadcasting: false,
        }));
      });
    }
  };

  handleChannelChange = (channel: Channel) => {
    const { broadcasterState } = this.state;
    if (channel.value !== this.channelId && broadcasterState === BroadcasterState.broadcasting) {
      this.nextChangingChannel = channel;
      this.setState({ showChannelChangeConfirmation: true });
    } else {
      this.setState({ broadcasterState: BroadcasterState.connecting, isProcessing: true });
      this.joinChannel(channel);
    }
  };

  handleChannelChangeConfirm = async (): Promise<void> => {
    this.setState({
      broadcasterState: BroadcasterState.connecting,
      isProcessing: true,
      showChannelChangeConfirmation: false,
    });
    await this.joinChannel(this.nextChangingChannel);
    this.nextChangingChannel = null;
  };

  onCancel = (): void => {
    this.setState({ showChannelChangeConfirmation: false });
    this.nextChangingChannel = null;
  };

  renderChannelChangeConfirmation() {
    const renderConfirmMessage = <span>You are broadcasting. Are you sure that you want to change channel?</span>;

    const renderContent = <div className="bp4-dialog-body">{renderConfirmMessage}</div>;

    const renderActionButtons = (
      <div className="bp4-dialog-footer">
        <div className="bp4-dialog-footer-actions">
          <Button intent={Intent.DANGER} onClick={this.handleChannelChangeConfirm} text="Yes" />
          <Button intent={Intent.NONE} onClick={this.onCancel} text="No" />
        </div>
      </div>
    );

    return (
      <Dialog
        className="bz-alert-dialog"
        icon="info-sign"
        isOpen={this.state.showChannelChangeConfirmation}
        onClose={this.onCancel}
        title="Confirmation"
      >
        {renderContent}
        {renderActionButtons}
      </Dialog>
    );
  }

  handleKeyPressHold = event => {
    if (event.code === 'AltRight') {
      if (!this.state.altKeyPress && this.state.pushToTalk) {
        this.pushToTalkEnable();
      }
    }
  };

  pushToTalkEnable = () => {
    if (this.channelId) {
      console.log('push to talk enabled');
      this.setState({ altKeyPress: true });
      this.clientBroadcaster.unMuteChannel(this.channelId);
    }
  };

  pushToTalkRelease = () => {
    if (this.channelId) {
      this.setState({ altKeyPress: false });
      this.clientBroadcaster.muteChannel(this.channelId);
    }
  };

  handleKeyPressLeave = event => {
    if (event.code === 'AltRight') {
      if (this.state.altKeyPress && this.channelId) {
        this.pushToTalkRelease();
      }
    }
  };

  onChange = (e: RadioChangeEvent) => {
    if (e.target.value === 2 && this.channelId) {
      this.clientBroadcaster.muteChannel(this.channelId);
      this.setState({ pushToTalk: true });
    } else {
      this.clientBroadcaster.unMuteChannel(this.channelId);
      this.setState({ pushToTalk: false });
    }
  };

  handleButtonPress = () => {
    this.buttonPressTimer = setTimeout(() => this.pushToTalkEnable(), 500);
  };

  handleButtonRelease = () => {
    console.log('unpressed');
    clearTimeout(this.buttonPressTimer);
    this.pushToTalkRelease();
  };

  renderBroadcastActions() {
    if (this.isBroadcasting()) {
      const { isMuted, isProcessing } = this.state;

      return (
        <>
          <div className="squawk__broadcast-actions">
            <button className="squawk__broadcast-btn" disabled={isProcessing} onClick={this.stopBroadcast}>
              Stop broadcast
            </button>
            {!this.state.pushToTalk && (
              <Button
                className="squawk__mute-toggle"
                icon={isMuted ? 'volume-down' : 'volume-up'}
                onClick={this.toggleMute}
              >
                {isMuted ? 'Unmute' : 'Mute'}
              </Button>
            )}

            {this.state.pushToTalk && (
              <Button
                className="squawk__mute-toggle"
                icon={this.state.altKeyPress ? 'volume-down' : 'volume-up'}
                onMouseDown={this.handleButtonPress}
                onMouseUp={this.handleButtonRelease}
                onTouchEnd={this.handleButtonRelease}
                onTouchStart={this.handleButtonPress}
              >
                {this.state.altKeyPress ? `Leave button to Squawk (Mute)` : `Long Press to Squawk (Unmute)`}.
              </Button>
            )}
          </div>
          <div className="squawk__title">Input Mode:</div>
          <Radio.Group defaultValue={2} onChange={this.onChange}>
            <Space direction="vertical">
              {/* <Radio value={1}>Always ON</Radio> */}
              <Radio value={2}>
                Push to Talk{' '}
                <b style={{ fontSize: `13px`, fontWeight: `600` }}>(Hold Down Right ALT/Right Option to Squawk)</b>
              </Radio>
            </Space>
          </Radio.Group>
        </>
      );
    }

    const handleSetBroadcastingClick = () => this.setBroadcasting(false);

    return (
      <div className="squawk__broadcast-actions">
        <button
          className="squawk__broadcast-btn"
          disabled={this.isBroadcastDisabled()}
          onClick={handleSetBroadcastingClick}
        >
          {this.getBroadcastAction()}
        </button>
      </div>
    );
  }

  renderOtherBroadcaters() {
    if (isNil(this.channelId)) {
      return;
    }

    const mapIndexed = addIndex(map);
    const { otherBroadcasters } = this.state;

    return (
      <div>
        <div className="squawk__title">Broadcasters:</div>
        <div className="squawk__broadcasters-list">
          {mapIndexed(
            (broadcaster: any, index) => (
              <span key={broadcaster.userId}>
                {broadcaster.username}
                {index !== size(otherBroadcasters) - 1 && ', '}
              </span>
            ),
            otherBroadcasters.filter(broadcaster => broadcaster.streamId == this.channelId),
          )}
          {(!otherBroadcasters || otherBroadcasters.length == 0) && <span>No broadcasters</span>}
        </div>
      </div>
    );
  }

  render() {
    const { logs, volumeLevel } = this.state;
    return (
      <div className="squawk">
        <div className="squawk__header">Audio Broadcaster</div>
        <div className="squawk__channel-dropdown">
          <Select
            className="row"
            isClearable={false}
            onChange={this.handleChannelChange}
            options={this.state.channelOptions}
            placeholder="Select Channel"
            value={this.selectedChannel || null}
          />
        </div>
        {this.renderBroadcastActions()}
        {this.isBroadcasting() && !isNil(this.localBroadcasterStream) && (
          <AudioVisualizer className="squawk__audio-visualizer" initialMediaStream={this.localBroadcasterStream} />
        )}
        <audio muted ref={this.broadcasterAudioEle}></audio>
        <audio autoPlay muted={false} ref={this.listenerAudioEle}></audio>
        {this.renderOtherBroadcaters()}
        <div className="squawk__title">Broadcaster Playback Volume:</div>
        <div className="squawk__volume">
          <Icon icon={IconNames.VOLUME_OFF} />
          <Slider
            className="squawk__volume-control"
            // labelRenderer={false}
            max={1}
            min={0}
            onChange={this.onVolumeChange}
            stepSize={0.01}
            value={volumeLevel}
          />
          <Icon icon={IconNames.VOLUME_UP} />
          <span className="squawk__volume-value">{round(volumeLevel * 100)}</span>
        </div>
        <div className="squawk__console">
          <div className="squawk__console-header">Console</div>
          <ul className="squawk__log-container">
            {map(
              log => (
                <li className={classnames('squawk__log-message', `squawk__log-message--${log.type}`)} key={log.id}>
                  {log.message}
                </li>
              ),
              logs,
            )}
          </ul>
        </div>
        {this.renderChannelChangeConfirmation()}
      </div>
    );
  }
}

const mapStateToProps = (state: RootState): StateProps => {
  const { email: username, key: token } = selectSession(state);
  return {
    token,
    username,
  };
};

export default connect<StateProps>(mapStateToProps)(Squawk);
