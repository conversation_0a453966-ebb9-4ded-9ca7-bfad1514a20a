import moment from 'moment';
import * as React from 'react';
import { Component } from 'react';

import { Callout, Divider, TextArea } from '@blueprintjs/core';
import { ENTER } from '@blueprintjs/core/lib/esm/common/keys';
import { isEmpty, isUndefined, map } from 'lodash';
import { connect } from 'react-redux';
import { Dispatch } from 'redux';
import { addNoteAsync } from '../actions';
import { NoteState } from '../reducers/entities';
import { Comment } from '../reducers/entities/noteEntities';
import { RootState } from '../state';
import { CollectionId } from './collections/collectionEntities';
import { selectNotes } from './collections/collectionSelectors';
import { Row } from './grid';

interface OwnProps {
  collectionId: CollectionId;
  row?: Row;
}

interface ReduxState {
  notes: NoteState;
}

interface State {
  comment: string;
}

interface DispatchableActions {
  onNoteAdd(comment: string, ticker: string): void;
}

type Props = OwnProps & DispatchableActions & ReduxState;

class Notes extends Component<Props, Readonly<State>> {
  constructor(props) {
    super(props);

    this.state = {
      comment: '',
    };
  }

  handleCommentChange = event => {
    this.setState({ comment: event.target.value.replace(/\n/g, '') });
  };

  postComment = event => {
    const { onNoteAdd, row } = this.props;
    const { comment } = this.state;
    if (event.keyCode === ENTER && comment && !isUndefined(row)) {
      onNoteAdd(comment, row.data.ticker);
      this.setState({ comment: '' });
    }
  };

  commentRender = (note: Comment, index: number) => {
    const { comment, created, user_name } = note;
    return (
      <Callout className="message" key={index}>
        <div>{`${moment.unix(created).format('YYYY-MM-DD HH:mm:ss')} ${user_name}`}</div>
        <Divider />
        {comment}
      </Callout>
    );
  };

  render() {
    const {
      notes: { comments },
      row,
    } = this.props;
    let rows = [];
    if (!isEmpty(comments) && !isEmpty(row)) {
      rows = map(comments, this.commentRender);
    }
    const displayMessage = !isEmpty(rows) ? (
      <div className="messages-wrapper">{rows}</div>
    ) : (
      <div className="no-messages">
        <span className="text">No messages</span>
      </div>
    );
    return (
      <div className="comments">
        <div className="messages">{displayMessage}</div>
        <div className="post-block">
          <div className="post-wrapper">
            <TextArea
              className="comment-input"
              onChange={this.handleCommentChange}
              onKeyDown={this.postComment}
              placeholder="Enter Notes"
              value={this.state.comment}
            />
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state: RootState): ReduxState => ({
  notes: selectNotes(state),
});

const mapDispatchToProps = (dispatch: Dispatch, { collectionId }: OwnProps): DispatchableActions => ({
  onNoteAdd: (comment: string, ticker: string) => dispatch(addNoteAsync(collectionId, comment, ticker)),
});

export default connect<ReduxState, DispatchableActions, OwnProps>(mapStateToProps, mapDispatchToProps)(Notes);
