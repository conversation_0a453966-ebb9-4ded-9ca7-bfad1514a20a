import React, { Component, createRef, ReactNode, RefObject } from 'react';

import classnames from 'classnames';

interface Props {
  animationSpeed?: number;
  children?: ReactNode;
  className?: string;
}

export default class Marquee extends Component<Props> {
  MARQUEE: RefObject<HTMLUListElement> = createRef();

  componentDidMount() {
    this.computeAnimationDuration();
    if (this.MARQUEE.current) {
      this.MARQUEE.current.classList.add('Marquee-content');
    }
  }

  componentDidUpdate() {
    this.computeAnimationDuration();
  }

  computeAnimationDuration() {
    if (!this.MARQUEE.current) {
      return;
    }
    const { offsetWidth } = this.MARQUEE.current;

    const { animationSpeed = 50 } = this.props;
    const duration = offsetWidth / animationSpeed;
    if (duration > 0) {
      this.MARQUEE.current.style.animationDuration = `${duration}s`;
    }
  }

  render() {
    const { children, className } = this.props;
    return (
      <div className={classnames('Marquee', className)}>
        <ul className="u-flexHorizontal" ref={this.MARQUEE}>
          {children}
          {children}
          {children}
          {children}
        </ul>
      </div>
    );
  }
}
