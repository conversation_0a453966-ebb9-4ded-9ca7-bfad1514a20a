import React, { MouseEvent } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { ThunkDispatch } from 'redux-thunk';

import classnames from 'classnames';
import { setStreamVisibility } from '../actions/settingsActions';
import styled from '@benzinga/themetron';
import { DevelopmentStage } from '../entities/developmentStageEntity';
import { RootAction, RootState } from '../redux/types';
import { squawkAccessSelector } from '../selectors/accessSelectors';
import { DevelopmentStageCornerBanner } from './ui/DevelopmentStageCornerBanner';
import { WindowBarButton } from './widgets/WidgetWindowBar';
import { UnpackedArray, objectShallowEqualWithExclude } from '@benzinga/utils';
import { ProContext } from '@benzinga/pro-tools';
import { WidgetType } from '@benzinga/widget-tools';
import { Manifest } from '../manifest';
import { usePermission } from '@benzinga/user-context';
import { Close, Help } from '@benzinga/valentyn-icons';
import { isProductionSite } from './Site';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { ProUserSettingsManager } from '@benzinga/pro-user-settings';

interface ReduxState {
  canAccessSquawk: boolean;
}

interface DispatchableActions {
  setStreamVisibility: typeof setStreamVisibility;
}

interface OwnProps {
  className?: string;
  isShown: boolean;
}

type Props = OwnProps & ReduxState & DispatchableActions;

type ButtonType = 'flyout' | 'sidebar' | 'popout';

const OPEN_POPOUT_CLASSNAME = 'SideBar-launch-button--popout';

const SideBar: React.FC<Props> = React.memo(
  props => {
    const proContext = React.useContext(ProContext);

    const session = React.useContext(SessionContext);
    const proUserSettingsManager = session.getManager(ProUserSettingsManager);

    const scrollbar = React.useRef<HTMLUListElement>(null);
    const flyoutRef = React.useRef<Map<WidgetType, HTMLDivElement>>(new Map());

    const handleScroll = () => {
      const position = scrollbar.current?.scrollTop;
      flyoutRef.current.forEach(v => {
        v.style.marginTop = `-${position}px`;
      });
    };

    React.useEffect(() => {
      const ogScrollbar = scrollbar.current;
      ogScrollbar?.addEventListener('scroll', handleScroll, { passive: true });

      return () => {
        ogScrollbar?.removeEventListener('scroll', handleScroll);
      };
    });

    // adds back the flyout on hover
    const handleMouseEnter = React.useCallback(({ currentTarget }: MouseEvent<HTMLDivElement>) => {
      if (currentTarget) {
        currentTarget.classList.remove('no-hover');
      }
    }, []);

    const handleNewWidget = (manifest: UnpackedArray<typeof Manifest>, buttonType: ButtonType) => () => {
      session.getManager(TrackingManager).trackLinkEvent('click', {
        link_action: 'open_widget',
        link_id: manifest.id,
        link_type: buttonType,
        value: manifest.name,
      });
      proContext.addWidget(manifest.id);
    };

    // hides the flyout if the user has opened the widget in a new window (popout)
    const handleFlyoutClick = React.useCallback((event: MouseEvent<HTMLDivElement>) => {
      const { currentTarget } = event;
      const target = event.target as HTMLDivElement;
      if (target && target.classList && target.classList.contains(OPEN_POPOUT_CLASSNAME) && currentTarget) {
        currentTarget.classList.add('no-hover');
      }
    }, []);

    // does not close the flyout because we are using event bubbling to close the flyout, rather than searching for
    // the container by looping through parentElement on the target.
    const handlePopoutClick = React.useCallback(
      (widgetType: WidgetType) => async () => {
        session.getManager(TrackingManager).trackLinkEvent('click', {
          link_action: 'open_widget',
          link_id: widgetType,
          link_type: 'popout',
          value: widgetType,
        });

        proContext.addPopoutForWidget({ widgetType });
      },
      [session, proContext],
    );

    const renderFlyout = (manifest: UnpackedArray<typeof Manifest>) => (
      <div className="SideBar-flyout" ref={(el: HTMLDivElement) => flyoutRef.current.set(manifest.id, el)}>
        <div className="SideBar-launch">
          <div
            className={classnames('SideBar-launch-button', OPEN_POPOUT_CLASSNAME)}
            onClick={handlePopoutClick(manifest.id)}
          >
            New Window
          </div>
          <div className="SideBar-launch-button" onClick={handleNewWidget(manifest, 'flyout')}>
            In Workspace
          </div>
        </div>
      </div>
    );

    const goToHelp = React.useCallback(() => {
      const openHelpPage = proContext.openHelpPage;
      openHelpPage?.('');
    }, [proContext.openHelpPage]);

    const makeWidgetButton = (manifest: UnpackedArray<typeof Manifest>) => {
      const Icon = manifest.icon;

      return (
        <div className="SideBar-btn-container" onClick={handleFlyoutClick} onMouseEnter={handleMouseEnter}>
          <div
            className={`SideBar-btn TUTORIAL_SideBar-${manifest.id} SideBar-btn-contentsContainer`}
            onClick={handleNewWidget(manifest, 'sidebar')}
          >
            {manifest.state === 'beta' && <DevelopmentStageCornerBanner stage={DevelopmentStage.beta} />}
            {manifest.state === 'alpha' && <DevelopmentStageCornerBanner stage={DevelopmentStage.alpha} />}
            {manifest.state === 'new' && <DevelopmentStageCornerBanner stage={DevelopmentStage.new} />}
            {manifest.state === 'deprecated' && <DevelopmentStageCornerBanner stage={DevelopmentStage.deprecated} />}

            <StyleWidgetIcon as={Icon as any} title={manifest.name} />
            <div className="SideBar-btn-label">{manifest.name}</div>
          </div>
          {renderFlyout(manifest)}
        </div>
      );
    };

    const { isShown } = props;
    const isAdmin = usePermission('#', '#');

    return (
      <StyledSidebarSection
        className={classnames('SideBar u-flexVertical', { 'SideBar--hidden': !isShown }, props.className)}
      >
        <ul className="SideBar-tools SideBar-tools-container u-flexSpacer" ref={scrollbar}>
          <MenuToolbarDiv>
            <WindowBarButton
              className="u-closeIcon TUTORIAL_Widget-SideBar-Remove"
              key="closeTool"
              onClick={() => proUserSettingsManager.setSetting('sidebarVisible', false)}
            >
              <Close title="Remove Sidebar" />
            </WindowBarButton>
          </MenuToolbarDiv>
          <ManifestList>
            {Manifest.filter(m => m.menuItem)
              .filter(m => (m.state === 'alpha' ? !isProductionSite() : true))
              .filter(m => (m.state === 'deprecated' ? isAdmin : true))
              .map(makeWidgetButton)}
            <div className="SideBar-btn-container">
              <div className={`SideBar-btn TUTORIAL_SideBar- SideBar-btn-contentsContainer`} onClick={goToHelp}>
                <StyleWidgetIcon as={Help} />
                <div className="SideBar-btn-label">Help</div>
              </div>
            </div>
          </ManifestList>
        </ul>
      </StyledSidebarSection>
    );
  },
  (p, n) => objectShallowEqualWithExclude(p, n, ['setStreamVisibility']),
);

const ManifestList = styled.div`
  overflow-y: auto;
  direction: rtl;
  height: 100%;

  &::-webkit-scrollbar {
    width: 6px !important;
    z-index: 9000;
  }
`;

const StyledSidebarSection = styled.section`
  height: 100%;
  margin-top: 0px;
`;
const StyleWidgetIcon = styled.svg`
  height: 22px;
  width: 22px;

  color: ${props => props.theme.colors.foreground};
`;

const MenuToolbarDiv = styled.div`
  border-top: 4px solid ${props => props.theme.colors.background};
  background-color: ${props => props.theme.colors.backgroundActive};
  display: flex;
  flex-direction: row;
`;

const mapStateToProps = (state: RootState): ReduxState => ({
  canAccessSquawk: squawkAccessSelector(state),
});

const mapDispatchToProps = (dispatch: ThunkDispatch<RootState, void, RootAction>): DispatchableActions =>
  bindActionCreators(
    {
      setStreamVisibility,
    },
    dispatch,
  );

export default connect<ReduxState, DispatchableActions, OwnProps, RootState>(
  mapStateToProps,
  mapDispatchToProps,
)(SideBar);
