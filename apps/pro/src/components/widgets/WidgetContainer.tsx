import { Column } from '@benzinga/fission';
import styled, { css, hueToColor } from '@benzinga/themetron';
import classnames from 'classnames';
import React, { CSSProperties, FunctionComponent } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  WidgetDefinition,
  WidgetContextProps,
  WidgetContextProvider,
  TooltipButton,
  ControlsDeclaration,
  ControlsKey,
  WidgetParametersContextProvider,
  WidgetParametersContextProps,
  WidgetToolbarContextProviderProps,
  WidgetToolbarContextProvider,
  DispatchEvent,
  WidgetToolsManager,
  WidgetType,
  WidgetId,
} from '@benzinga/widget-tools';
import { arrayDeepEqual, arrayShallowEqual, objectShallowEqualWithExclude } from '@benzinga/utils';

import ErrorBoundary from '../ui/ErrorBoundary';
import { WidgetWindowBar } from './WidgetWindowBar';
import { faEllipsis } from '@fortawesome/pro-solid-svg-icons';
import NoneWidget from './none/widget';

import { MosaicBranch, MosaicWindow as MW } from 'react-mosaic-component';
import { Icon } from '@benzinga/core-ui';
import { WidgetLinkHue } from '@benzinga/widget-linking';
import { ToolbarSubMenu } from './ToolbarSubMenu';
import Hooks from '@benzinga/hooks';
import { isPopout } from '@benzinga/pro-ui';
import { updateWidgetParameters } from '../../reducers/widgetsReducer';
import { ProContext } from '@benzinga/pro-tools';
import { Manifest } from '../../manifest';
import { SessionContext } from '@benzinga/session-context';
import { Close } from '@benzinga/valentyn-icons';
import { RootState } from '../../redux/types';
import { Popout, Chat } from '@benzinga/themed-icons';

interface DockedProps {
  chatLocation: 'left' | 'right';
  setChatLocation: (location: 'left' | 'right') => void;
}
interface Props {
  path?: MosaicBranch[];
  style?: CSSProperties;
  theme?: string;
  widgetId: WidgetId;
  docked?: DockedProps;
}

interface State {
  widgetDefinition: Omit<WidgetDefinition, 'controlsNodes'>;
  widgetType: WidgetType;
}

interface GenericWidgetProps {
  type: WidgetType | undefined;
  widgetId: WidgetId;
}

const WidgetSelector: FunctionComponent<GenericWidgetProps> = ({ type, widgetId }) => {
  const manifest = Manifest.find(manifest => manifest.id === type);
  if (manifest) {
    const { WidgetRender } = manifest;
    return <WidgetRender key={widgetId} />;
  } else {
    return <NoneWidget />;
  }
};

export const WidgetContainer: React.FC<Props> = React.memo(
  props => {
    const session = React.useContext(SessionContext);
    const proContext = React.useContext(ProContext);
    const [widgetParams, setWidgetParams] = React.useState(() =>
      session.getManager(WidgetToolsManager).getWidgetParameters(props.widgetId),
    );
    const widgetType = React.useMemo(
      () => session.getManager(WidgetToolsManager).getWidgetType(props.widgetId),
      [props.widgetId, session],
    );

    React.useEffect(() => {
      session.getManager(WidgetToolsManager).onWidgetParametersChange(props.widgetId, parameters => {
        setWidgetParams(parameters);
      });
    }, [props.widgetId, session]);

    const manifest = React.useMemo(() => Manifest.find(manifest => manifest.id === widgetType), [widgetType]);
    const [state, setState] = React.useState<State>(() => {
      const controlsDisplay: ControlsKey[] = ['submenu', 'closeTool'];
      if (!isPopout) {
        controlsDisplay.unshift('popoutTool');
      }

      return {
        widgetDefinition: {
          controlsDisplay,
          icon: manifest?.icon ? <StyledWidgetIcon as={manifest.icon} /> : null,
          title: manifest?.name ?? '',
          toolbar: null,
        },
        widgetType: 'newsfeed',
      };
    });

    const [toolbarColor, setToolbarColor] = React.useState<WidgetLinkHue>(() => null);
    const controlsNodes = React.useRef(new Map<ControlsKey, ControlsDeclaration>());
    controlsNodes.current.set('closeTool', {
      toolbarNode: (
        <TooltipButton
          key="closeTool"
          onClick={React.useCallback(() => {
            const removeWidget = proContext.removeWidget;
            removeWidget(props.widgetId);
          }, [proContext.removeWidget, props.widgetId])}
        >
          <Close title={`Remove ${state.widgetDefinition.title} Tool`} />
        </TooltipButton>
      ),
    });
    controlsNodes.current.set('popoutTool', {
      submenuNode: {
        action: React.useCallback(() => {
          const addPopoutForWidget = proContext.addPopoutForWidget;
          addPopoutForWidget({ widgetId: props.widgetId });
        }, [proContext.addPopoutForWidget, props.widgetId]),
        icon: <Popout />,
        key: 'popoutTool',
        name: 'Pop Out Tool',
        type: 'Item',
      },
    });

    React.useEffect(() => {
      const docked = props.docked;
      if (docked) {
        const newLocation = docked.chatLocation === 'left' ? 'right' : 'left';
        controlsNodes.current.set('setChatLocation', {
          submenuNode: {
            action: () => {
              docked.setChatLocation(newLocation);
            },
            icon: <Chat />,
            key: 'setChatLocation',
            name: `Set Chat Position ${newLocation[0].toUpperCase() + newLocation.slice(1)}`,
            type: 'Item',
          },
        });
        setState(old => ({
          ...old,
          widgetDefinition: {
            ...old.widgetDefinition,
            controlsDisplay: [
              'setChatLocation',
              ...old.widgetDefinition.controlsDisplay.filter(f => f !== 'setChatLocation'),
            ],
          },
        }));
      }
    }, [props.docked]);

    const updateWidgetDefinition = React.useCallback((newWidgetDefinition: Partial<WidgetDefinition>) => {
      setState(old => ({
        ...old,
        widgetDefinition: { ...old.widgetDefinition, ...newWidgetDefinition },
      }));
      if (newWidgetDefinition.controlsNodes) {
        newWidgetDefinition.controlsNodes.forEach((v, k) => {
          controlsNodes.current.set(k, v);
        });
      }
    }, []);

    const defineControls = React.useCallback((...controls: (ControlsDeclaration & { key: ControlsKey })[]) => {
      controls.forEach(v => {
        const { key, ...control } = v;
        controlsNodes.current.set(key, control);
      });

      //update controlsDisplay when we overwrite a existing node
      setState(oldState => {
        if (
          oldState.widgetDefinition.controlsDisplay.some(node => controls.some(displayNode => displayNode.key === node))
        ) {
          return {
            ...oldState,
            widgetDefinition: {
              ...oldState.widgetDefinition,
              controlsDisplay: [...oldState.widgetDefinition.controlsDisplay],
            },
          };
        } else return oldState;
      });
    }, []);

    const setControlsDisplay = React.useCallback((callback: (old: ControlsKey[]) => ControlsKey[]) => {
      setState(oldState => {
        if (!(Array.prototype as any).findLastIndex) {
          (Array.prototype as any).findLastIndex = function (callback) {
            for (let i = this.length - 1; i >= 0; i--) {
              if (callback(this[i], i, this)) {
                return i;
              }
            }
            return -1;
          };
        }

        const newControlsDisplay = callback(oldState.widgetDefinition.controlsDisplay).reduce<ControlsKey[]>(
          (acc, key, i, array) => {
            if ((array as any).findLastIndex(f => f === key) > i) {
              return acc;
            }
            acc.push(key);
            return acc;
          },
          [],
        );

        return {
          ...oldState,
          widgetDefinition: { ...oldState.widgetDefinition, controlsDisplay: newControlsDisplay },
        };
      });
    }, []);

    const setToolbar = React.useCallback(
      (toolbar: React.ReactNode) =>
        setState(old => ({
          ...old,
          widgetDefinition: { ...old.widgetDefinition, toolbar },
        })),
      [],
    );

    const setIcon = React.useCallback(
      (icon: React.ReactNode) => updateWidgetDefinition({ icon }),
      [updateWidgetDefinition],
    );

    const setTitle = React.useCallback(
      (title: string | React.ReactNode) => updateWidgetDefinition({ title }),
      [updateWidgetDefinition],
    );

    const { style = {}, widgetId } = props;
    const [widgetRef, setWidgetRef] = React.useState<HTMLDivElement | null>(null);
    const setNode = React.useCallback(node => {
      if (node !== null) {
        setWidgetRef(node);
      }
    }, []);

    const setPushEvent = React.useCallback(
      (pushEvent: (event: DispatchEvent) => void) => {
        const setDispatchEvent = proContext.setDispatchEvent;
        setDispatchEvent(widgetId, pushEvent);
      },
      [proContext.setDispatchEvent, widgetId],
    );

    const closeWidget = React.useCallback(() => {
      const removeWidget = proContext.removeWidget;
      removeWidget(widgetId);
    }, [proContext.removeWidget, widgetId]);

    const contextValue = React.useMemo<WidgetContextProps>(
      () => ({
        closeWidget,

        setDispatchEvent: setPushEvent,

        widgetId,
        widgetRef,
        widgetType: widgetType ?? 'newsfeed',
      }),
      [closeWidget, setPushEvent, widgetRef, widgetId, widgetType],
    );

    const contextToolbarValue = React.useMemo<WidgetToolbarContextProviderProps>(
      () => ({
        defineControls,
        definition: { controlsNodes: controlsNodes.current, ...state.widgetDefinition },
        setControlsDisplay,
        setIcon,
        setTitle,
        setToolbar,
        setToolbarColor,
        updateDefinition: updateWidgetDefinition,
      }),
      [
        defineControls,
        setControlsDisplay,
        setIcon,
        setTitle,
        setToolbar,
        state.widgetDefinition,
        updateWidgetDefinition,
      ],
    );

    const dispatch = useDispatch<any>();
    const setParameters = React.useCallback<(callback: <T extends object>(parameters: T) => T) => void>(
      callback => dispatch(updateWidgetParameters(props.widgetId, callback)),
      [dispatch, props.widgetId],
    );

    const contextParametersValue = React.useMemo<Partial<WidgetParametersContextProps>>(
      () => ({
        parameters: widgetParams,
        setParameters: setParameters as any,
      }),
      [setParameters, widgetParams],
    );

    const prevControlsDisplay = Hooks.usePrevious(state.widgetDefinition.controlsDisplay);
    React.useEffect(() => {
      if (arrayDeepEqual(state.widgetDefinition.controlsDisplay, prevControlsDisplay ?? [])) return;
      defineControls({
        key: 'submenu',
        toolbarNode: (
          <ToolbarSubMenu
            controlsDisplay={state.widgetDefinition.controlsDisplay}
            controlsNodes={controlsNodes.current}
          >
            <TooltipButton key={'submenu'}>
              <DropDownIcon icon={faEllipsis} title={`${state.widgetDefinition.title} Tool Menu`} />
            </TooltipButton>
          </ToolbarSubMenu>
        ),
      });
    }, [defineControls, prevControlsDisplay, state.widgetDefinition.controlsDisplay, state.widgetDefinition.title]);

    const globalChatWidgetId = useSelector((state: RootState) => state.settings.globalChatWidgetId);

    if (!widgetType) {
      return null;
    }

    const children = props.path ? (
      <LinkedColor tooltipColor={toolbarColor}>
        <MosaicWindow
          // additionalControls={widgetId === 3 ? additionalControls : EMPTY_ARRAY}
          // createNode={createNode}
          createNode={() => 'new'}
          onDragEnd={type => console.log(`MosaicWindow.onDragEnd, ${type}`, type)}
          onDragStart={() => console.log('MosaicWindow.onDragStart')}
          path={props.path ?? []}
          renderToolbar={() => (
            <div style={{ width: '100%' }}>
              <WidgetWindowBar />
            </div>
          )}
          title={
            typeof state.widgetDefinition.title === 'string'
              ? state.widgetDefinition.title
              : manifest?.name ?? widgetType
          }
        >
          <WidgetWrapper
            className={classnames({ Widget: widgetId !== globalChatWidgetId })}
            ref={setNode}
            style={style}
          >
            <ErrorBoundary>
              <WidgetSelector type={widgetType} widgetId={widgetId} />
            </ErrorBoundary>
          </WidgetWrapper>
        </MosaicWindow>
      </LinkedColor>
    ) : (
      <LinkedColor tooltipColor={toolbarColor}>
        <WidgetWrapper className={classnames({ Widget: widgetId !== globalChatWidgetId })} ref={setNode} style={style}>
          <WidgetWindowBar />
          <ErrorBoundary>
            <WidgetSelector type={widgetType} widgetId={widgetId} />
          </ErrorBoundary>
        </WidgetWrapper>
      </LinkedColor>
    );

    return (
      <WidgetContextProvider {...contextValue}>
        <WidgetToolbarContextProvider {...contextToolbarValue}>
          <WidgetParametersContextProvider {...contextParametersValue}>{children}</WidgetParametersContextProvider>
        </WidgetToolbarContextProvider>
      </WidgetContextProvider>
    );
  },
  (prevProps, nextProps) => {
    if (objectShallowEqualWithExclude(prevProps, nextProps, ['path']) === false) {
      return false;
    } else if (prevProps.path === undefined || nextProps.path === undefined) {
      return prevProps.path === nextProps.path;
    } else {
      return arrayShallowEqual(prevProps.path, nextProps.path);
    }
  },
);

const StyledWidgetIcon = styled.svg`
  color: ${props => props.theme.colors.foregroundInactive};
`;

const DropDownIcon = styled(Icon)`
  display: contents;
  font-size: 70%;
  color: ${props => props.theme.colors.foregroundInactive} !important;
  &:hover {
    color: ${props => props.theme.colors.foregroundActive} !important;
  }
`;
const WidgetWrapper = styled(Column)`
  background: ${props => props.theme.colors.background};
  box-sizing: border-box;
  color: ${props => props.theme.colors.foreground};
  flex: 1;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
`;

const LinkedColor = styled.span<{ tooltipColor: WidgetLinkHue }>`
  display: inline-grid;
  width: 100%;
  height: 100%;
  background-color: ${props =>
    props.tooltipColor !== null
      ? css`color-mix(in srgb, ${hueToColor(props.tooltipColor)}, ${props.theme.colors.backgroundInactive} 50%)`
      : props.theme.colors.background};
  padding-top: 3px;
`;

const MosaicWindow = styled(MW)`
  border-radius: initial !important;
  border-top-right-radius: initial !important;
  border-top-left-radius: initial !important;
  box-shadow: initial !important;

  .mosaic.mosaic-blueprint-theme .mosaic-window {
    border-radius: initial !important;
    border-top-right-radius: initial !important;
    border-top-left-radius: initial !important;
    box-shadow: initial !important;
  }

  .mosaic-window-toolbar {
    align-items: end !important;
    background-color: ${props => props.theme.colors.background} !important;
    border-top-right-radius: 0px !important;
    border-top-left-radius: 0px !important;
    box-shadow: initial !important;
  }

  .draggable:hover {
    background: ${props => props.theme.colors.background} !important;
  }

  .mosaic-window-toolbar.draggable:hover {
    background: ${props => props.theme.colors.background} !important;
  }

  .mosaic-window-body {
    background: ${props => props.theme.colors.border} !important;
  }
`;
