import createCachedSelector from 're-reselect';
import { createSelector } from 'reselect';

import { Workspace, WorkspaceId } from './workspaceEntity';
import { RootState } from '../../redux/types';
import { propWidgetId, selectWidgets } from '../../selectors/widgetSelectors';
import { getWidgetsIdsFromConfig } from './workspaceUtils';

import { homePageWorkspace } from './homeMenu/homeMenu';
import { Widget } from '../../entities/widgetEntity';

export const selectWorkspaces = (state: RootState) => state.workspaces;
export const selectUserWorkspaces = (state: RootState) => selectWorkspaces(state).userWorkspaces;

export const propWorkspaceId = (_: RootState, { workspaceId }: { workspaceId: WorkspaceId }) => workspaceId;

export const getWidgetsFromWorkspace = (workspaces, workspaceId): Workspace => {
  switch (workspaceId) {
    case 'home-page':
      return homePageWorkspace;
    case 'morning-report':
      return {
        config: null,
        lastAccessed: 0,
        name: 'Morning Report Page',
        version: 3 as const,
        workspaceId: 'morning-report',
      };
    case 'welcome-page':
      return {
        config: null,
        lastAccessed: 0,
        name: 'Welcome Page',
        version: 3 as const,
        workspaceId: 'welcome-page',
      };
    default:
      return workspaces.find(workspace => workspace.workspaceId === workspaceId) ?? homePageWorkspace;
  }
};

export const selectWorkspaceById = createCachedSelector(
  selectUserWorkspaces,
  propWorkspaceId,
  getWidgetsFromWorkspace,
)(propWorkspaceId);

export const selectActiveWorkspaceId = createSelector(selectWorkspaces, workspace => workspace.activeWorkspaceId);

export const selectActiveWorkspace = createSelector(
  (state: RootState): RootState => state,
  selectActiveWorkspaceId,
  (state, workspaceId) => selectWorkspaceById(state, { workspaceId }),
);

export const selectWorkspaceWidgetIds = createCachedSelector(selectWorkspaceById, workspace =>
  getWidgetsIdsFromConfig(workspace.config),
)(propWorkspaceId);

export const selectWorkspaceWidgets = createCachedSelector(
  selectWorkspaceWidgetIds,
  selectWidgets,
  (widgetIds, widgetsById) => widgetIds.map(id => widgetsById[id]),
)(propWorkspaceId);

export const selectActiveWorkspaceWidgetIds = createSelector(selectActiveWorkspace, activeWorkspace =>
  activeWorkspace ? getWidgetsIdsFromConfig(activeWorkspace.config) : [],
);

export const selectActiveWorkspaceWidgets = createCachedSelector(
  selectActiveWorkspaceWidgetIds,
  selectWidgets,
  (widgetIds, widgetsById) =>
    widgetIds.reduce<Widget[]>(
      (widgets, id) => (widgetsById[id] ? [...widgets, widgetsById[id]] : widgets) as Widget[],
      [],
    ),
)(selectActiveWorkspaceId);

export const isWidgetInActiveWorkspace = createSelector(
  selectActiveWorkspaceWidgetIds,
  propWidgetId,
  (activeWorkspaceWidgetIds: string[] = [], widgetId: string) => activeWorkspaceWidgetIds.includes(widgetId),
);
