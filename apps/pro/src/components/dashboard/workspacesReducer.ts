import { LOAD_LAYOUT, NEW_LAYOUT } from '../../actions/layoutActions';
import { ADD_WIDGET, REMOVE_WIDGET } from '../../actions/widgetActions';
import {
  ADD_WORKSPACE,
  CHANGE_WORKSPACE_NAME,
  HIDE_WORKSPACE,
  MOVE_WORKSPACE_TAB,
  REMOVE_WORKSPACE,
  RESTORE_WORKSPACE,
  UPDATE_WORKSPACE,
  SWITCH_WORKSPACE,
  OPEN_WELCOME_PAGE,
  OPEN_MORNING_REPORT_PAGE,
} from './workspaceActions';
import { Workspaces } from './workspaceEntity';
import { RootAction } from '../../redux/types';
import { createWorkspace, getInitialWorkspaceState } from '../../utils/workspaces';
import { addWidgetToConfig, removeWidgetFromConfig } from './workspaceUtils';
import { isPopout } from '@benzinga/pro-ui';

export function moveUserWorkspaceTab(state: Workspaces, newIndex: number): Workspaces {
  const userWorkspaces = [...state.userWorkspaces];
  const activeWorkspaceIndex =
    userWorkspaces.findIndex(({ workspaceId }) => workspaceId === state.activeWorkspaceId) || 0;
  const activeWorkspace = userWorkspaces[activeWorkspaceIndex];

  if (activeWorkspaceIndex === newIndex) {
    return { ...state, userWorkspaces };
  }
  userWorkspaces.splice(activeWorkspaceIndex, 1);
  userWorkspaces.splice(newIndex, 0, activeWorkspace);
  return {
    ...state,
    userWorkspaces,
  };
}

export function workspacesReducer(state: Workspaces = getInitialWorkspaceState(), action: RootAction): Workspaces {
  switch (action.type) {
    case ADD_WIDGET: {
      const {
        payload: { widgetId, workspaceId },
      } = action;

      if (isPopout) {
        return state;
      }

      if (workspaceId) {
        return {
          ...state,
          userWorkspaces: state.userWorkspaces.map(workspace =>
            workspace.workspaceId === workspaceId
              ? {
                  ...workspace,
                  config: addWidgetToConfig(widgetId, workspace.config),
                }
              : workspace,
          ),
        };
      }

      return state;
    }

    case REMOVE_WIDGET: {
      const {
        payload: { widgetId, workspaceId },
      } = action;
      return {
        ...state,
        userWorkspaces: state.userWorkspaces.map(workspace =>
          workspace.workspaceId === workspaceId
            ? {
                ...workspace,
                config: removeWidgetFromConfig(widgetId, workspace.config),
              }
            : workspace,
        ),
      };
    }

    case MOVE_WORKSPACE_TAB:
      return moveUserWorkspaceTab(state, action.payload.newIndex);

    case UPDATE_WORKSPACE: {
      const {
        payload: { config, workspaceId },
      } = action;

      return {
        ...state,
        userWorkspaces: state.userWorkspaces.map(workspace =>
          workspace.workspaceId === workspaceId
            ? {
                ...workspace,
                config,
              }
            : workspace,
        ),
      };
    }

    case ADD_WORKSPACE:
      return {
        ...state,
        activeWorkspaceId: action.payload.workspaceId,
        userWorkspaces: state.userWorkspaces.concat(createWorkspace(action.payload.workspaceId)),
      };

    case OPEN_WELCOME_PAGE:
      return {
        ...state,
        activeWorkspaceId: 'welcome-page',
        'welcome-page': { currentPanel: action.panel, isOnPlatformBar: true, lastAccessed: Date.now() },
      }; // todo dejan - check if active

    case OPEN_MORNING_REPORT_PAGE:
      return {
        ...state,
        activeWorkspaceId: 'morning-report',
        'morning-report': { isOnPlatformBar: true, lastAccessed: Date.now() },
      }; // todo dejan - check if active

    case REMOVE_WORKSPACE:
      if (action.payload.workspaceId === 'welcome-page') {
        return {
          ...state,
          'welcome-page': { ...state['welcome-page'], isOnPlatformBar: false },
        }; // todo dejan - check if active
      } else if (action.payload.workspaceId === 'morning-report') {
        return {
          ...state,
          'morning-report': { ...state['morning-report'], isOnPlatformBar: false },
        };
      } else {
        return {
          ...state,
          userWorkspaces: state.userWorkspaces.filter(
            workspace => workspace.workspaceId !== action.payload.workspaceId,
          ),
        }; // todo dejan - check if active
      }
    case HIDE_WORKSPACE:
      return {
        ...state,
        userWorkspaces: state.userWorkspaces.filter(workspace => workspace.workspaceId !== action.payload.workspaceId),
      };

    case RESTORE_WORKSPACE:
      return {
        ...state,
        userWorkspaces: state.userWorkspaces.map(workspace =>
          workspace.workspaceId === action.payload.workspaceId
            ? {
                ...workspace,
                isHidden: false,
              }
            : workspace,
        ),
      };

    case SWITCH_WORKSPACE:
      return {
        ...state,
        activeWorkspaceId: action.payload.workspaceId,
        'home-page':
          state.activeWorkspaceId === 'home-page'
            ? { ...state['home-page'], lastAccessed: Date.now() }
            : state['home-page'],
        userWorkspaces: state.userWorkspaces.map(workspace =>
          workspace.workspaceId === state.activeWorkspaceId ? { ...workspace, lastAccessed: Date.now() } : workspace,
        ),
        'welcome-page':
          state.activeWorkspaceId === 'welcome-page'
            ? { ...state['welcome-page'], lastAccessed: Date.now() }
            : state['welcome-page'],
      };

    case CHANGE_WORKSPACE_NAME:
      return {
        ...state,
        userWorkspaces: state.userWorkspaces.map(workspace =>
          workspace.workspaceId === action.payload.workspaceId
            ? {
                ...workspace,
                name: action.payload.name,
              }
            : workspace,
        ),
      };

    case LOAD_LAYOUT:
      return {
        ...state,
        ...action.payload.workspaces,
      };

    case NEW_LAYOUT:
      return getInitialWorkspaceState();

    default:
      return state;
  }
}
