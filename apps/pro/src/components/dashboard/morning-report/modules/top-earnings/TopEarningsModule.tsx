import React, { useEffect, useMemo } from 'react';
import ModuleWrapper from '../../components/module/ModuleWrapper';
import { SessionContext } from '@benzinga/session-context';
import { columns } from './ColumnsSettings';
import { ModuleTable } from '../../components/table/ModuleTable';
import { CalendarManager, Earnings } from '@benzinga/calendar-manager';
import { DateTime } from 'luxon';
import { getMarketTimeStatus, MarketTimeStatus } from '../../components/table/utils/time';
import AddToWatchlist from '../../components/watchlist/AddToWatchlist';

const TopEarningsModule: React.FC = () => {
  const session = React.useContext(SessionContext);
  const [loading, setLoading] = React.useState(true);
  const [data, setData] = React.useState<Earnings[]>([]);

  const tickers = useMemo(() => {
    return data?.map(earning => earning.ticker) ?? [];
  }, [data]);

  useEffect(() => {
    const quotesManager = session.getManager(CalendarManager);
    quotesManager
      .getCalendarData('earnings', { date: DateTime.now().toFormat('yyyy-LL-dd') }, true)
      .then(response => {
        const earningsData = response?.ok ?? [];
        earningsData.sort((a, b) => DateTime.fromISO(a.time).toMillis() - DateTime.fromISO(b.time).toMillis());

        const afterMarket: Earnings[] = [];
        const preMarket: Earnings[] = [];

        earningsData.forEach(earning => {
          const marketTimeStatus = getMarketTimeStatus(earning.time);
          if (marketTimeStatus === MarketTimeStatus.BEFORE_MARKET && preMarket.length < 10) {
            preMarket.push(earning);
          } else if (marketTimeStatus === MarketTimeStatus.AFTER_MARKET && afterMarket.length < 10) {
            afterMarket.push(earning);
          }
        });

        setData([...preMarket, ...afterMarket]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [session]);

  return (
    <ModuleWrapper
      title="Top Earnings For The Day"
      titleAffix={!!tickers?.length && <AddToWatchlist symbols={tickers} />}
    >
      <ModuleTable columns={columns} dataSource={data} loading={loading} pagination={false} tableLayout="auto" />
    </ModuleWrapper>
  );
};

export default TopEarningsModule;
