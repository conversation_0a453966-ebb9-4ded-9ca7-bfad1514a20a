import { getTimeDisplayFormat } from '@benzinga/date-utils';
import { useTime } from '@benzinga/time-manager-hooks';
import { DateTime } from 'luxon';
import React from 'react';

type DateDisplay = 'date' | 'time' | 'datetime';

interface DateColumnProps {
  date: string;
  time?: string;
  display?: DateDisplay;
}

const DateColumn: React.FC<DateColumnProps> = ({ date, display, time }) => {
  const { timeFormat, timeOffsetFromEastern: timeOffset } = useTime();

  const formattedDate = DateTime.fromISO(date).plus({ minutes: timeOffset }).toFormat('LLL dd, yyyy');
  const formattedTime = time
    ? DateTime.fromISO(time)
        .plus({ minutes: timeOffset })
        .toFormat(getTimeDisplayFormat({ timeFormat: timeFormat }))
    : '';

  return (
    <div style={{ display: 'flex', flexDirection: 'column', fontSize: '14px' }}>
      {(display === 'date' || display === 'datetime') && <span style={{ fontWeight: '700' }}>{formattedDate}</span>}
      {(display === 'time' || display === 'datetime') && (
        <span
          style={{
            color: display === 'time' ? 'var(--bz-foreground-active, #fff)' : '#929EAE',
            fontWeight: display === 'time' ? '700' : '400',
          }}
        >
          {formattedTime}
        </span>
      )}
    </div>
  );
};

export default DateColumn;
