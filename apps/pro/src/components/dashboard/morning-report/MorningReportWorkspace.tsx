import React from 'react';
import { selectWorkspaces } from '../workspaceSelectors';
import { RootState } from '../../../redux/types';
import { useSelector } from 'react-redux';
import { MorningReport } from './MorningReport';
import styled from '@benzinga/themetron';

interface Props {
  className?: string;
}

const MorningReportWorkspace: React.FC<Props> = props => {
  const morningReportPage = useSelector((state: RootState) => selectWorkspaces(state)['morning-report']);

  if (morningReportPage.isOnPlatformBar === false) {
    return null;
  }

  return (
    <Holder className={props.className}>
      <MorningReport />
    </Holder>
  );
};

const Holder = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${props => props.theme.colors.border};
`;

export default MorningReportWorkspace;
