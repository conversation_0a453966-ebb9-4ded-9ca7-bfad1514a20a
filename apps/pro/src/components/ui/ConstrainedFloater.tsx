import React, { Component, createRef, CSSProperties, ReactNode, RefObject } from 'react';

interface Props {
  children?: ReactNode;
  constrainHeight?: boolean;
  constrainWidth?: boolean;
}

/**
 * Creates a floating div whose size is restricted so that it will fit in a parent
 * with overflow: hidden ensuring that no part of its children will be hidden
 * due to extending outside of said parent.
 */
export default class Constrained extends Component<Props> {
  constrainingDiv: RefObject<HTMLDivElement> = createRef();

  render() {
    const { constrainHeight = true, constrainWidth } = this.props;
    const constrainedStyle: CSSProperties = {};

    if (this.constrainingDiv.current) {
      const refRect = this.constrainingDiv.current.getBoundingClientRect();

      let parentEl = this.constrainingDiv.current.parentElement;
      let parentRect = {
        height: 0,
        left: 0,
        top: 0,
        width: 0,
      };

      while (parentEl) {
        const style = getComputedStyle(parentEl);

        if (style.overflow === 'hidden') {
          // Bounding rect has numbers for width/height, whereas style can have 'auto'
          parentRect = parentEl.getBoundingClientRect();

          break;
        }

        parentEl = parentEl.parentElement;
      }

      const remainingHeight = parentRect.height - (refRect.top - parentRect.top);
      const remainingWidth = parentRect.width - (refRect.left - parentRect.left);

      if (constrainWidth) {
        constrainedStyle.maxWidth = remainingWidth;
      }

      if (constrainHeight) {
        constrainedStyle.maxHeight = remainingHeight;
      }
    }

    return (
      <div className="ConstrainedFloater" ref={this.constrainingDiv} style={constrainedStyle}>
        <div className="scroll-container">{this.props.children}</div>
      </div>
    );
  }
}
