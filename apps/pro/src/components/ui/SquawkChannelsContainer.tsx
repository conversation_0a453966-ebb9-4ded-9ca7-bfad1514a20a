import React from 'react';

import Hooks from '@benzinga/hooks';
import { SessionContext } from '@benzinga/session-context';
import styled, { ThemeContext } from '@benzinga/themetron';
import { SquawkManagerEvent, SquawkManager } from '@benzinga/squawk-manager';
import { PermissionPrompt, PermissionType, PermissionArgs, LoggedInComponent } from '@benzinga/user-context';
import { TrackingManager } from '@benzinga/tracking-manager';

import SquawkChannel from './SquawkChannel';
import { ConnectionState, SquawkIndicator } from './SquawkIndicator';
import { MenuItem } from '../dashboard/MenuItem';
import { Result, message } from 'antd';
import { LoggingManager, PersistStorageKey } from '@benzinga/session';
import { TextToSpeechManager } from '@benzinga/text-to-speech-manager';
import { SettingsV30 } from '../../entities/settingsEntity';
import { safeJsonParse } from '@benzinga/safe-await';
import { Squawk, Lock } from '@benzinga/themed-icons';
import { OneClickUpSellModal } from '@benzinga/pro-ui';

interface State {
  connectionError: string | null;
  connectionState: ConnectionState;
  onDisconnectTimer: { type: 'pending'; timer: ReturnType<typeof setTimeout> } | { type: 'triggered' } | null;
}

interface Props {
  menuHoverable: boolean;
}

const SquawkChannelsContainer: React.FC<Props> = props => {
  const session = React.useContext(SessionContext);
  const theme = React.useContext(ThemeContext);
  const squawkManager = session.getManager(SquawkManager);
  const connectedChannelIDs = React.useRef(new Set<number>());
  const receivingMediaChannelIDs = React.useRef(new Set<number>());
  const channelAudio = React.useRef(new Map<number, HTMLAudioElement | null>());

  const [state, setState] = React.useState<State>({
    connectionError: null,
    connectionState: 'stopped',
    onDisconnectTimer: null,
  });

  const update = Hooks.useForceUpdate();

  Hooks.useSubscriber(squawkManager, (event: SquawkManagerEvent) => {
    switch (event.type) {
      case 'squawk:media_override':
        channelStopped(event.channel?.getId(), 'stopped');

        session.getManager(LoggingManager).log(
          'error',
          {
            category: 'Squawk',
            message:
              'Squawk Audio Stopped. This account started playing channel ' +
              event.channel.getDescription() +
              ' from another session',
          },
          ['toast'],
        );

        session.getManager(TrackingManager).trackWidgetEvent('error', 'squawk', {
          error_message: 'This account started playing from another session',
          widget_sub_type: event.channel.getDescription(),
        });
        break;
      case 'squawk:remote_stream': {
        const audio = channelAudio.current.get(event.channel.getId());
        if (audio) {
          audio.srcObject = event.mediaStream;
        }
        break;
      }
      case 'squawk:transport_state_change':
        if (event.transportState === 'disconnected') {
          setState(preState => ({
            ...preState,
            connectionError: 'Unable to connect. Retrying...',
            connectionState: 'disconnected',
          }));
        }
        break;
      case 'squawk:rtc_state_change':
        switch (event.rtcState) {
          case 'closed':
            channelStopped(event.channel?.getId(), 'stopped');
            break;
          case 'connected':
            channelStarted(event.channel?.getId());
            break;
          case 'failed':
            channelStopped(event.channel?.getId(), 'disconnected');
            break;
        }
        break;
      case 'squawk:presenter_state_change':
        if (event.presenterState === 'streaming') {
          receivingMediaChannelIDs.current.add(event.channel?.getId());
        } else {
          receivingMediaChannelIDs.current.delete(event.channel?.getId());
        }
        setState(preState =>
          receivingMediaChannelIDs.current.size !== 0
            ? { ...preState, connectionState: 'receivingMedia' }
            : connectedChannelIDs.current.size !== 0
              ? { ...preState, connectionState: 'connected' }
              : { ...preState, connectionState: 'stopped' },
        );

        break;
      case 'squawk:squawk_channels_update':
        bzLog('Got channels list');
        update();
        break;
      case 'error':
        switch (event.errorType) {
          case 'squawk:failed_to_initialize':
            bzLog('Failed to initialize Squawk client SDK', event.error);
            setState(preState => ({ ...preState, connectionState: 'disconnected' }));
            session.getManager(LoggingManager).log(
              'error',
              {
                category: 'Squawk',
                message:
                  'Squawk was unable to establish a connection and will continue to retry. Please try refreshing webpage. If still facing an issue, contact support to troubleshoot',
              },
              ['toast'],
            );
            break;
          case 'squawk:get_channels':
            bzLog('Failed to fetch available channels', event.error);
            break;
        }
        break;
      case 'squawk:priorities_updated':
        update();
        break;
    }
  });

  React.useEffect(() => {
    squawkManager.getChannels();
  }, [squawkManager]);

  React.useEffect(() => {
    // the first time this doesn't return anything and we want that good voice
    window.speechSynthesis.getVoices();
  }, []);

  const disconnectMessages = React.useCallback(() => {
    const settings = window.sessionStorage.getItem(PersistStorageKey.settings);
    if (settings) {
      const parsedSettings = safeJsonParse<SettingsV30>(settings).ok;
      message.error(
        {
          content: 'Squawk has disconnected',
          key: 'squawk_disconnect',
        },
        () => {
          message.destroy('squawk_disconnect');
        },
      );
      if (parsedSettings?.narrateSquawkStatus === true) {
        if (session.getManager(TextToSpeechManager).getBZNativeVoicesEnabled() === true) {
          session.getManager(TextToSpeechManager).setBZNativeVoicesEnabled(false);
          session.getManager(TextToSpeechManager).textToSpeech('Benzinga Pro Squawk has disconnected');
          session.getManager(TextToSpeechManager).setBZNativeVoicesEnabled(true);
        } else {
          session.getManager(TextToSpeechManager).textToSpeech('Benzinga Pro Squawk has disconnected');
        }
      }
    }
    setState(s => ({ ...s, onDisconnectTimer: { type: 'triggered' } }));
  }, [session]);

  const reconnectMessages = React.useCallback(() => {
    const settings = window.sessionStorage.getItem(PersistStorageKey.settings);
    if (settings) {
      const parsedSettings: SettingsV30 = JSON.parse(settings);
      message.success(
        {
          content: 'Squawk has reconnected',
          key: 'squawk_reconnect',
        },
        () => {
          message.destroy('squawk_reconnect');
        },
      );
      if (parsedSettings.narrateSquawkStatus === true) {
        if (session.getManager(TextToSpeechManager).getBZNativeVoicesEnabled() === true) {
          session.getManager(TextToSpeechManager).textToSpeech('Benzinga Pro Squawk has reconnected');
        } else {
          session.getManager(TextToSpeechManager).textToSpeech('Benzinga Pro Squawk has reconnected');
        }
      }
    }
  }, [session]);

  React.useEffect(() => {
    if (state.connectionState === 'disconnected' && !state.onDisconnectTimer) {
      setState(s => ({ ...s, onDisconnectTimer: { timer: setTimeout(disconnectMessages, 15000), type: 'pending' } }));
    } else if (state.connectionState === 'connected' && state.onDisconnectTimer) {
      if (state.onDisconnectTimer.type === 'triggered') {
        reconnectMessages();
      } else {
        clearTimeout(state.onDisconnectTimer.timer);
      }
      setState(s => ({ ...s, onDisconnectTimer: null }));
    }
  }, [disconnectMessages, reconnectMessages, state.connectionState, state.onDisconnectTimer]);

  const channelStopped = (channelId: number, state: ConnectionState) => {
    connectedChannelIDs.current.delete(channelId);
    receivingMediaChannelIDs.current.delete(channelId);
    if (connectedChannelIDs.current.size === 0) {
      //this.setState({ connectionState: state });
      setState(preState => ({ ...preState, connectionState: state }));
      // meaning user clicked on stop channel
    } else if (receivingMediaChannelIDs.current.size === 0) {
      //this.setState({ connectionState: 'connected' });
      setState(preState => ({ ...preState, connectionState: 'connected' }));
    }
  };

  const channelStarted = (channelId: number) => {
    connectedChannelIDs.current.add(channelId);
    receivingMediaChannelIDs.current.delete(channelId);
    setState(preState =>
      preState.connectionState === 'connected' || receivingMediaChannelIDs.current.size > 0
        ? preState
        : { ...preState, connectionState: 'connected' },
    );
  };

  const renderIndicator = () => {
    const { connectionError, connectionState } = state;
    return (
      <SquawkIndicator
        $connectionState={connectionState}
        $error={connectionError}
        $isHeaderMenu={true}
        $isSidebar={false}
      />
    );
  };

  const channels = squawkManager.getCashedChannels();

  // the following is simply a hack so that we can close the dialog when the permission dialog is open
  const [displayPrompt, setDisplayPrompt] = React.useState<(PermissionArgs & PermissionType) | undefined>(undefined);
  const refDisplayPrompt = React.useRef(displayPrompt);
  const setDisplay = React.useCallback((perm: PermissionArgs & PermissionType) => {
    refDisplayPrompt.current = perm;
    setDisplayPrompt(perm);
  }, []);
  const closePrompt = React.useCallback(() => {
    refDisplayPrompt.current = undefined;
    setDisplayPrompt(undefined);
  }, []);

  // keep menu open after user action
  const onClick = React.useCallback(
    (e: React.MouseEvent) => (refDisplayPrompt.current ? undefined : e.stopPropagation()),
    [],
  );

  const [showUpsellModal, setShowUpsellModal] = React.useState(false);

  const squawkRender = () => {
    const allowedChannels = channels.filter(c => c.isAllowedToListen() || c.getProducts().includes('benzinga-pro'));
    return (
      <div onClick={onClick}>
        <SquawkSidebar numChannels={allowedChannels.length}>
          {allowedChannels.length === 0 && <Result title="No Squawks are available" />}
          {allowedChannels
            .sort((a, b) => (squawkManager.getPriorityOfChannel(a) > squawkManager.getPriorityOfChannel(b) ? 1 : -1))
            .map(channel => (
              <SquawkChannel
                channel={channel}
                channelAudio={channelAudio.current.get(channel.getId()) ?? null}
                key={channel.getId()}
                setDisplayPrompt={setDisplay}
                setUpsellModal={setShowUpsellModal}
                squawkManager={squawkManager}
                theme={theme}
              />
            ))}
        </SquawkSidebar>
      </div>
    );
  };

  return (
    <>
      {showUpsellModal ? (
        <OneClickUpSellModal
          buttonText="Get High Beta Squawk"
          plans={[
            {
              interval: 'month',
              slug: 'squawk-channel-4-monthly',
            },
            {
              interval: 'year',
              slug: 'squawk-channel-4-annual',
            },
          ]}
          product="squawk"
          setShowModal={setShowUpsellModal}
          show={showUpsellModal}
          title="Add High Beta Squawk to your subscription?"
        />
      ) : (
        <LoggedInComponent>
          {access => (
            <MenuItem
              dropdownContent={squawkRender()}
              menuHoverable={props.menuHoverable}
              placement="left"
              visible={displayPrompt ? false : undefined}
            >
              <SquawkIcon className={`TUTORIAL_MenuBar-Squawk`}>
                {access ? (
                  <LockIconDiv>{renderIndicator()}</LockIconDiv>
                ) : (
                  <LockIconDiv>
                    <LockIcon />
                  </LockIconDiv>
                )}
                <Squawk title="Open Audio News Squawk" />
              </SquawkIcon>
              {channels.map(c => (
                <audio
                  autoPlay={true}
                  key={c.getId()}
                  muted={false}
                  ref={el => channelAudio.current.set(c.getId(), el)}
                />
              ))}
            </MenuItem>
          )}
        </LoggedInComponent>
      )}

      {displayPrompt && <PermissionPrompt {...displayPrompt} onClose={closePrompt} />}
    </>
  );
};

const SquawkIcon = styled.div`
  background: transparent;

  .ant-dropdown-open > & {
    background: ${props => props.theme.colors.backgroundActive};
  }
`;

const SquawkSidebar = styled.div<{ numChannels: number }>`
  flex-flow: column nowrap !important;
  height: ${props => (props.numChannels === 0 ? `240` : props.numChannels * 120)}px !important;
  /* margin-top: -${props => props.numChannels * 120 - 60}px; // to allign botom of flyout with bottom of squawk menu */
  justify-content: center !important;

  display: flex;
  color: ${props => props.theme.colors.foreground};
  width: 390px;
  overflow: hidden;
  font-size: 13px;
  z-index: -1;
  background: ${props => props.theme.colors.backgroundActive};
  transform: translateX(0);
  transition-delay: 0;
  /* :hover {
    simpleTransition(transform)
    transform: translateX(0);
    transition-delay: 0;
  } */
`;

const LockIcon = styled(Lock)`
  cursor: pointer;
  left: 15px;
  position: absolute;
  top: -3px;
  font-size: 10px;
  fill: ${props => props.theme.colors.accent} !important;
`;

const LockIconDiv = styled.div`
  position: relative;
`;

export default SquawkChannelsContainer;
