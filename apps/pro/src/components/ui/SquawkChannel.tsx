import React from 'react';
import { connect } from 'react-redux';

import { Div, Row } from '@benzinga/fission';
import styled, { Theme, TC } from '@benzinga/themetron';
import Slider from 'rc-slider';
import * as Sentry from '@sentry/browser';

import { persistSquawkChannelVolume, setSquawkChannelConnectionAction } from '../../actions/settingsActions';
import { RootState } from '../../redux/types';
import { selectSquawkChannelsVolume, selectSquawkDefaultVolume } from '../../selectors/settingsSelectors';
import * as SquawkSDK from '@benzinga/benzinga-squawk-sdk';
import { ConnectionState, SquawkIndicator } from './SquawkIndicator';
import { SquawkChannel as SquawkCh, SquawkManager } from '@benzinga/squawk-manager';
import { PermissionArgs, PermissionType } from '@benzinga/user-context';
import Hooks from '@benzinga/hooks';

import { SessionContext } from '@benzinga/session-context';
import { DesktopNotificationManager } from '@benzinga/desktop-notification-manager';
import { Pause, PlayCircle, Reset, UpX2, Volume, VolumeOFF } from '@benzinga/valentyn-icons';
import { TrackingManager } from '@benzinga/tracking-manager';
import { Lock } from '@benzinga/themed-icons';
interface ReduxState {
  defaultVolume: number;
  initialChannelsVolume: {
    [key: number]: number;
  };
}

interface DispatchableActions {
  persistChannelVolume: typeof persistSquawkChannelVolume;
  setConnectionAction: typeof setSquawkChannelConnectionAction;
}

interface OwnProps {
  channel: SquawkCh;
  channelAudio: HTMLAudioElement | null;
  squawkManager: SquawkManager;
  theme: Theme;
  setDisplayPrompt: (permission: PermissionArgs & PermissionType) => void;
  setUpsellModal: (show: boolean) => void;
}

type Props = ReduxState & DispatchableActions & OwnProps;

const SquawkChannel: React.FC<Props> = props => {
  const [volumeLevel, setVolumeLevel] = React.useState<number>(
    props.initialChannelsVolume?.[props.channel.getId()] ?? 0.5,
  );
  const [muffled, setMuffled] = React.useState(false);
  const session = React.useContext(SessionContext);

  const update = Hooks.useForceUpdate();
  Hooks.useSubscriber(props.channel, event => {
    switch (event.type) {
      case 'squawk:channel_muffle_audio': {
        if (props.channelAudio) {
          if (event.muffle) {
            props.channelAudio.volume = 0.1;
          } else {
            props.channelAudio.volume = props.initialChannelsVolume?.[props.channel.getId()] ?? 0.5;
          }
          setMuffled(event.muffle);
        }
        break;
      }
      default:
        update();
    }
  });

  const webRTCFailureNotification = () => {
    session.getManager(DesktopNotificationManager).showInternalNotification(
      'Squawk Connection Failed',
      {
        body: `Squawk was unable to establish a connection to the server. This is likely a network firewall or antivirus
            related issue. Please consult the
            help docs
            or contact support to troubleshoot.`,
      },
      () => {
        window.open('https://pro.benzinga.help/en/articles/3521474-squawk-tips-and-troubleshooting');
      },
    );
  };

  const handleStartListeningErr = (err: Error) => {
    Sentry.captureException(err);
    webRTCFailureNotification();
    bzLog('Failed to start listening to channel, channel Id: ' + props.channel.getId(), err);
    session.getManager(TrackingManager).trackWidgetEvent('error', 'squawk', {
      error_message: 'WebRTC failed',
      widget_sub_type: props.channel.getDescription(),
    });
  };

  const onVolumeChange = (value: number | number[]) => {
    setVolumeLevel(value as number);

    session.getManager(TrackingManager).trackWidgetEvent('volume_up', 'squawk', {
      widget_sub_type: props.channel.getDescription(),
    });
  };

  React.useEffect(() => {
    if (props.channelAudio) {
      props.channelAudio.volume = volumeLevel;
    }
  }, [props.channelAudio, volumeLevel]);

  const persistVolume = (value: number | number[]) => {
    props.persistChannelVolume(props.channel.getId(), value as number);
  };

  const toggleMute = async () => {
    const result = props.channel.isMuted() ? props.channel.unMuteChannel() : props.channel.muteChannel();
    if (result.err) {
      Sentry.captureException(result.err);
      bzLog('Failed to mute broadcasting channel', result.err);
    }
    session.getManager(TrackingManager).trackWidgetEvent(props.channel.isMuted() ? 'mute' : 'unmute', 'squawk', {
      widget_sub_type: props.channel.getDescription(),
    });
  };

  const startConnect = async () => {
    props.channelAudio?.play?.().catch(null);
    const request = await props.channel.start();
    props.setConnectionAction(props.channel.getId(), 'running');
    if (request.err) {
      if (request.err.data instanceof SquawkSDK.SquawkError) {
        const err: SquawkSDK.SquawkError = request.err.data;
        if (err.code !== SquawkSDK.ErrorType.ALREADY_SUBSCRIBED) {
          handleStartListeningErr(err);
        }
      } else {
        handleStartListeningErr(request.err);
      }
    } else {
      bzLog('Started listening to to channel, channel Id: ', props.channel.getId());
    }
    session.getManager(TrackingManager).trackWidgetEvent('connected', 'squawk', {
      widget_sub_type: props.channel.getDescription(),
    });
  };

  const startDisconnect = async () => {
    const result = await props.channel.stop();
    props.setConnectionAction(props.channel.getId(), 'stopped');
    if (result.err) {
      bzLog('Failed to dispose listener client', result.err);
    }
    session.getManager(TrackingManager).trackWidgetEvent('disconnected', 'squawk', {
      widget_sub_type: props.channel.getDescription(),
    });
  };

  const handleUpsellModal = React.useCallback(() => {
    if (props.channel.getDescription() !== 'High Beta Squawk') {
      props.setDisplayPrompt({});
      return;
    }
    props.setUpsellModal(true);
  }, [props]);

  const RenderPlayToggle = () => {
    const renderIcon = React.useMemo(() => {
      switch (props.channel.getState()) {
        case 'connected':
        case 'receivingMedia':
          return (
            <IconWrapper onClick={startDisconnect}>
              <Pause />
            </IconWrapper>
          );
        case 'stopped':
          return (
            <IconWrapper onClick={startConnect}>
              <PlayCircle />
            </IconWrapper>
          );

        default:
          return (
            <IconWrapper onClick={startDisconnect}>
              <Reset />
            </IconWrapper>
          );
      }
    }, []);
    return (
      <PlayToggle>
        {props.channel.isAllowedToListen() ? (
          renderIcon
        ) : (
          <div onClick={handleUpsellModal}>
            <PlayCircle />
            <LockIcon />
          </div>
        )}
      </PlayToggle>
    );
  };

  const getStatusText = React.useCallback(
    (connectionState: ConnectionState, error: string | null) => {
      if (error) {
        return error;
      }
      switch (connectionState) {
        case 'stopped':
        default:
          return 'Stopped';

        case 'connecting':
          return 'Connecting...';

        case 'connected':
          return 'Connected. Waiting for audio...';

        case 'receivingMedia':
          return muffled ? 'Connected. Receiving audio (muffled)...' : 'Connected. Receiving audio...';
      }
    },
    [muffled],
  );

  const renderIndicator = (isSidebar: boolean) => {
    const connectionState = props.channel.getState();
    const connectionError = props.channel.getStoppingReason() ?? null;
    return <SquawkIndicator $connectionState={connectionState} $error={connectionError} $isSidebar={isSidebar} />;
  };

  const renderStatusBar = () => {
    const connectionState = props.channel.getState();
    const connectionError = props.channel.getStoppingReason() ?? null;
    return (
      <SquawkStatusContainer>
        <SquawkStatus>
          <SquawkStatusLabelContainer>
            {renderIndicator(false)}
            <SquawkStatusLabel>Status:</SquawkStatusLabel>
          </SquawkStatusLabelContainer>
          <SquawkStatusText>{getStatusText(connectionState, connectionError)}</SquawkStatusText>
        </SquawkStatus>
      </SquawkStatusContainer>
    );
  };

  const promoteClick = React.useCallback(() => {
    props.channel.promotePriority();
  }, [props.channel]);

  const renderSquawkTime = () => {
    if (props.channel.getId() === 1) {
      return '6 AM - 6 PM EST on market days';
    } else if (props.channel.getId() === 3) {
      return '11 AM - 1 PM EST on market days';
    } else if (props.channel.getId() === 4) {
      return 'Market Open - Market Close';
    } else {
      return '';
    }
  };

  const renderFlyout = () => (
    <SquawkFlyoutContainer>
      <SquawkFlyoutHeader>
        <TC.StretchRow>
          <div>
            {props.channel.getDescription()} <SquawkFlyoutHeaderTime>{renderSquawkTime()}</SquawkFlyoutHeaderTime>
          </div>
          <IconWrapper>
            <PromoteIcon onClick={promoteClick} title="increase priority" />
          </IconWrapper>
        </TC.StretchRow>
      </SquawkFlyoutHeader>
      <SquawkFlyoutContent>
        {RenderPlayToggle()}
        <SquawkFlyoutBtn onClick={toggleMute}>{props.channel.isMuted() ? 'UNMUTE' : 'MUTE'}</SquawkFlyoutBtn>
        <SquawkFlyoutSlider>
          <VolumeOFF />
          <SquawkFlyoutSliderTrack
            className={`Slider-track bz-theme-${props.theme.name}`}
            max={1}
            min={0}
            onAfterChange={persistVolume}
            onChange={onVolumeChange}
            step={0.01}
            value={volumeLevel}
          />
          <Volume />
        </SquawkFlyoutSlider>
        <SquwkFlyoutVolumn>{Math.round(volumeLevel * 100)}</SquwkFlyoutVolumn>
      </SquawkFlyoutContent>
    </SquawkFlyoutContainer>
  );

  return (
    <>
      {renderFlyout()}
      {props.channel.isAllowedToListen() && renderStatusBar()}
    </>
  );
};

const IconWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  svg path {
    fill: ${({ theme }) => theme.colors.foregroundActive};
  }
`;

const SquawkContainer = styled.div`
  display: flex;
  flex: 1;
  margin-left: 1em;
  margin-right: 1em;
`;

const SquawkFlyoutContainer = styled(SquawkContainer)`
  flex-flow: column nowrap;
  justify-content: center;
`;

const SquawkStatusContainer = styled(SquawkContainer)`
  align-items: center;
  flex-flow: row nowrap;
`;

const SquawkStatus = styled(Row)`
  align-items: center;
  border: 3px solid rgba(0, 143, 217, 0.2);
  flex: 1;
  height: 60%;
`;

const SquawkStatusText = styled(Div)`
  color: ${props => props.theme.colors.foregroundInactive};
  margin-left: 1em;
`;

const SquawkStatusLabelContainer = styled(Row)`
  align-items: center;
  background: rgba(0, 143, 217, 0.2);
  height: 100%;
  width: 22%;
`;

const SquawkStatusLabel = styled(Div)`
  color: ${props => props.theme.colors.brandForeground};
`;

const SquawkFlyoutHeader = styled.div`
  color: ${props => props.theme.colors.foregroundActive};
  text-transform: uppercase;
  margin-bottom: 0.5em;
  font-size: 1em;
  font-weight: 400;
`;

const SquawkFlyoutHeaderTime = styled.span`
  color: ${props => props.theme.colors.foregroundActive};
  text-transform: capitalize;
  font-size: 0.75em;
  font-style: italic;
`;

const SquawkFlyoutContent = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const SquawkFlyoutBtn = styled.div`
  flex-basis: 2em;
  min-width: 4em;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  color: ${props => props.theme.colors.foregroundInactive};
  background: ${props => props.theme.colors.background};
  padding: 1em;
  margin-right: 10px;
  height: 0.1em;
  width: 2.5em;
  transition: background 0.2s ease;
  :hover {
    color: ${props => props.theme.colors.foreground};
    background: ${props => props.theme.colors.backgroundInactive};
  }
  font-size: 0.85em;
  text-transform: uppercase;
`;

const SquawkFlyoutSlider = styled.div`
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  svg {
    font-size: 1.5em;
  }
`;

const SquawkFlyoutSliderTrack = styled(Slider)`
  flex: 1;
  position: relative;
  margin-right: 1em;
  margin-left: 1em;
`;

const SquwkFlyoutVolumn = styled.div`
  margin-left: 10px;
  color: ${props => props.theme.colors.foregroundActive};
  font-size: 0.9em;
  width: 25px;
  user-select: none;
`;

const PlayToggle = styled(Row)`
  align-items: center;
  position: relative;
  background: ${props => props.theme.colors.brand};
  color: ${props => props.theme.colors.brandForeground};
  cursor: pointer;
  font-size: 0.85em;
  height: 1em;
  justify-content: center;
  margin-right: 1em;
  padding: 1em;
`;

const LockIcon = styled(Lock)`
  cursor: pointer;
  position: absolute;
  top: 2px;
  right: 4px;
  font-size: 10px;
  fill: ${props => props.theme.colors.accent} !important;
`;

const PromoteIcon = styled(UpX2)`
  width: 22px;
  &:hover {
    cursor: pointer;
  }
  svg path {
    fill: ${props => props.theme.colors.brand} !important;
  }
`;

const mapStateToProps = (state: RootState): ReduxState => ({
  defaultVolume: selectSquawkDefaultVolume(state),
  initialChannelsVolume: selectSquawkChannelsVolume(state),
});

const mapDispatchToProps = {
  persistChannelVolume: persistSquawkChannelVolume,
  setConnectionAction: setSquawkChannelConnectionAction,
};

export default connect<ReduxState, DispatchableActions, OwnProps, RootState>(
  mapStateToProps,
  mapDispatchToProps,
)(SquawkChannel) as React.FC<OwnProps>;
