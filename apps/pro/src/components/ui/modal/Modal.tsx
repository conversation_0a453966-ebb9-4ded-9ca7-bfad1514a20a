import React, { Component, createRef, CSSProperties, RefObject, SyntheticEvent } from 'react';

import classnames from 'classnames';
import { Error, Warning, Close } from '@benzinga/themed-icons';

interface Props {
  alert?: boolean;
  className?: string;
  closeOnBackgroudClick?: boolean;
  containerStyle?: CSSProperties;
  coverScreen?: boolean;
  fullScreen?: boolean;
  isError?: boolean;
  isWarning?: boolean;
  showCrossBtn?: boolean;
  showTitle?: boolean;
  title?: string | JSX.Element;
  toggle(toggle?: boolean): void;
}

export default class Modal extends Component<React.PropsWithChildren<Props>> {
  private BACKGROUND: RefObject<HTMLDivElement> = createRef();

  backgroundClick = (event: SyntheticEvent<HTMLElement>) => {
    const { closeOnBackgroudClick = true } = this.props;
    if (event.target === this.BACKGROUND.current && closeOnBackgroudClick) {
      this.closeModal();
    }
  };

  closeModal = () => {
    this.props.toggle(false);
  };

  render() {
    const {
      alert,
      children,
      className,
      containerStyle = {},
      coverScreen,
      fullScreen = false,
      isError,
      isWarning,
      showCrossBtn = true,
      showTitle = true,
    } = this.props;
    const title = this.props.title || '';
    let prefix: JSX.Element | null = null;

    if (isError) {
      prefix = (
        <span className="u-warnText">
          <Error />
          Error:{' '}
        </span>
      );
    } else if (isWarning) {
      prefix = (
        <span className="u-accentText">
          <Warning />
          Error:{' '}
        </span>
      );
    }

    const modal = (
      <div
        className={classnames('Modal', 'Modal-background', className)}
        onClick={this.backgroundClick}
        ref={this.BACKGROUND}
      >
        <div
          aria-labelledby="modalTitle"
          className={classnames('Modal-container', {
            'Modal-alert': alert,
            'Modal-coverScreen': coverScreen,
          })}
          role="dialog"
          style={containerStyle}
        >
          {showTitle && (
            <div className="Modal-titleBar">
              <span className="Modal-titleBar-title" id="modalTitle">
                {prefix}
                {title}
              </span>
              {showCrossBtn && <Close className="u-closeIcon" onClick={this.closeModal} />}
            </div>
          )}
          <div className="scroll-container">{children}</div>
        </div>
      </div>
    );

    if (fullScreen) {
      return <div className="ModalWrapper-fullScreen">{modal}</div>;
    }

    return modal;
  }
}
