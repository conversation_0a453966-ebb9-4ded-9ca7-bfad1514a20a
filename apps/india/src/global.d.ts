interface Window {
  __REDUX_DEVTOOLS_EXTENSION_COMPOSE__: any;
  analytics: any;
  pbjs: any;
  env: Environments;
  EventCapture: any;
  google: google;
  Intercom?: intercomEvent;
  bzLog: (...args: any[]) => void;
}

declare module '*.svg' {
  const content: any;
  export default content;
}

declare module '*.png' {
  const content: any;
  export default content;
}

declare global {
  const bzCookies: string;
  export default bzCookies;
  interface Window {
    env: Environments;
    google: google;
  }
}

declare namespace JSX {
  interface IntrinsicElements {
    'amp-story-player': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
  }
}

declare const global: {
  analytics: any;
  geoip: (json: any) => void;
  geo: GeoData;
};
