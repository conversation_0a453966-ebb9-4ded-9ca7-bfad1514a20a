'use server';
import { headers, cookies } from 'next/headers';
import type { IncomingHttpHeaders } from 'http';

interface RequestInfoReturn {
  cookies: Partial<{
    [key: string]: string;
  }>;
  headers: IncomingHttpHeaders;
}

export async function getRequestInfo(): Promise<RequestInfoReturn> {
  const headerList = headers();
  const cookieStore = cookies();

  const headersObject: IncomingHttpHeaders = {};
  headerList.forEach((value, key) => {
    headersObject[key.toLowerCase()] = value;
  });

  // Convert cookies to a plain object of type Partial<{ [key: string]: string }>
  const cookiesObject: Partial<{ [key: string]: string }> = {};
  cookieStore.getAll().forEach(cookie => {
    cookiesObject[cookie.name] = cookie.value;
  });

  return {
    cookies: cookiesObject,
    headers: headersObject,
  };
}
