import SlugPageLayout from '../_components/SlugPageLayout';
import { serverProps, SlugServerProps } from './serverProps';
import React from 'react';
import PageLayout from '../_components/PageLayout';
import { MetaProps, metaV2 } from '@benzinga/seo';

export const generateMetadata = async ({ params }) => {
  const { props } = (await serverProps({ params })) as SlugServerProps;

  return await metaV2(props?.metaProps as MetaProps);
};
const SlugPage = async ({ params }) => {
  const data = await serverProps({ params });

  return (
    <PageLayout>
      <SlugPageLayout {...data} />
    </PageLayout>
  );
};
export default SlugPage;
