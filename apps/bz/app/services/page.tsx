import { PageType } from '@benzinga/seo';
import { Layout } from '@benzinga/core-ui';
import { BannerAds } from './components/BannerAds';
import { getServicesData } from './data';
import { ServiceCard } from './components/ServiceCard';
import { BzEdgeBlock } from './components/BzEdgeBlock';
import { Container } from './components/Container';
import PageLayout from '../_components/PageLayout';

export function generateMetadata() {
  const dateUpdated = new Date().toISOString();

  return {
    alternates: {
      canonical: 'https://www.benzinga.com/services',
    },
    dateUpdated,
    description: '',
    openGraph: {
      description: '',
      title: 'Benzinga Products',
      updatedTime: dateUpdated,
      url: 'https://www.benzinga.com/services',
    },
    pageType: PageType.Tool,
    title: 'Benzinga Products',
  };
}

export default async function ServicesPage() {
  const { products, webinarBlock } = await getServicesData();

  return (
    <PageLayout>
      <Container className="services-page">
        <Layout
          layoutHeader={
            <section className="services-hero-section">
              <div className="services-hero-content">
                <div>premium services</div>
                <h1>Take your trading to the next level with our suite of premium research services and tools.</h1>
                <p>
                  Whatever your goals are, we can alleviate the stress with expert strategies from our veteran analysts
                  and our trading terminal.
                </p>
              </div>
              <BzEdgeBlock />
              <div className="service-cards-container">
                {products.map((product, i) => (
                  <ServiceCard key={i} product={product} />
                ))}
              </div>
            </section>
          }
          layoutHeaderOptions={{ full_width: true }}
          layoutMain={
            <section className="ads-container">
              <BannerAds webinarBlock={webinarBlock} />
            </section>
          }
        />
      </Container>
    </PageLayout>
  );
}
