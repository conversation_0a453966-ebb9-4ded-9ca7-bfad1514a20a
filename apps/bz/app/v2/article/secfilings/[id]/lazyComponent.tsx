'use client';
import { LayoutBox, LayoutRow } from '@benzinga/core-ui';
import { PostElapsed } from '@benzinga/news';
import { DateTime } from 'luxon';
import React, { ReactElement } from 'react';
import LazyLoad from 'react-lazyload';
import styled from '@benzinga/themetron';
import { ArticleData } from '@benzinga/article-manager';

export const AdMgid = React.lazy(() => import('../../../../../src/components/Ads/MGID/AdMgid'));

export const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.RaptiveAdPlaceholder,
  })),
);

// eslint-disable-next-line @next/next/no-html-link-for-pages
const secLink = <a href="/secfilings">View All Secfilings</a>;

// This can be expanded on.... and should be moved to UI

export const GetLayoutSidebar: React.FC<{ secRecent: any; secRelated: any }> = ({ secRecent, secRelated }) => {
  return (
    <>
      <LayoutBox>
        <React.Suspense fallback={<div className="h-[250px] w-[300px] mb-2" />}>
          <RaptiveAdPlaceholder className="w-[300px] mb-2 overflow-hidden" type="static-sidebar" />
        </React.Suspense>
      </LayoutBox>
      {secRelated && articleSidebarBlock({ blockTitle: 'Related SEC Filings', link: secLink, stories: secRelated })}
      {secRecent && articleSidebarBlock({ blockTitle: 'Recent SEC Filings', link: secLink, stories: secRecent })}
    </>
  );
};

export const articleSidebarBlock: React.FC<{ stories: any; link: React.ReactElement; blockTitle: string }> = ({
  blockTitle,
  link,
  stories,
}) => {
  if (!stories || !stories.length) {
    return <></>;
  }

  return (
    <>
      <LayoutBox className="">
        <h3 className="mb-2">{blockTitle}</h3>
        <div>
          {stories.map((story, index) => (
            <div className="mb-2" key={index}>
              <a data-action="SEC Filing Sidebar Article Click" href={story.url}>
                {story.title}
              </a>
              <br />
              <ArticleDateTime title={DateTime.fromRFC2822(story.created).toString()}>
                <PostElapsed created={story.created} />
              </ArticleDateTime>
            </div>
          ))}
        </div>
        <div>{link}</div>
      </LayoutBox>
    </>
  );
};

export const LayoutBelow: React.FC = () => {
  return (
    <>
      <LayoutBox className="flex items-center justify-center">
        <LazyLoad offset={200} once>
          <AdMgid />
        </LazyLoad>
      </LayoutBox>
    </>
  );
};

export const GetLayoutMain: React.FC<{ article: ArticleData; secFormType: string | null }> = ({
  article,
  secFormType,
}): ReactElement => {
  return (
    <>
      <LayoutBox className="xl:w-4/6 mx-auto">
        <LayoutRow className="flex-wrap">
          <div className="flex-grow">
            <Label>Accepted:</Label> <PostElapsed created={article.createdAt} dateType="full" />
          </div>
          {secFormType && (
            <div>
              <Label>Form Type:</Label> {secFormType}
            </div>
          )}
        </LayoutRow>
        <LayoutRow>
          {article?.meta?.SEC?.AccessionNumber && (
            <div>
              <Label>Accession Number:</Label> {article.meta.SEC.AccessionNumber}
            </div>
          )}
        </LayoutRow>
      </LayoutBox>
      <IFrameDivWrapper className="flex flex-grow flex-1 items-stretch xl:w-4/6 w-full mx-auto">
        <IFrameSEC className="article-body w-full flex-1" id="article-body" srcDoc={article.body} />
      </IFrameDivWrapper>
      <LayoutBelow />
    </>
  );
};

const ArticleDateTime = styled.span`
  font-size: 12px;
  font-weight: 400;
`;

const IFrameDivWrapper = styled.div`
  min-height: 90vh;
`;

const IFrameSEC = styled.iframe``;
const Label = styled.span`
  font-weight: 700;
`;
