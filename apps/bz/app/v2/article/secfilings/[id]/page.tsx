import React from 'react';
import { Layout } from '@benzinga/core-ui';
import { Metadata, NextPage } from 'next';
import { GetLayoutMain, GetLayoutSidebar } from './lazyComponent';

import { getSecfilingsArticles, SECFilingArticlePageType } from '../../../../_libs/getArticleData';
import { MetaProps } from '@benzinga/seo';
import { setMetadata } from '../../../../_libs/setMetadata';

type Props = {
  params: { id: string };
};

const NotFound = () => (
  <div>
    <h1>404 - Not Found</h1>
    <p>The requested article was not found.</p>
  </div>
);

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { props } = await getSecfilingsArticles(params.id);
  const meta = props?.metaProps || ({} as MetaProps);

  return await setMetadata(meta);
}

const SECFilingArticlePage: NextPage<{ params: { id: string } }> = async ({ params: { id } }) => {
  if (!id) {
    return <NotFound />;
  }

  const { props } = await getSecfilingsArticles(id);
  const { article, secFormType, secRecent, secRelated } = props as unknown as SECFilingArticlePageType;
  if (!article) {
    return <NotFound />;
  }
  return (
    <>
      <Layout
        layoutMain={<GetLayoutMain article={article} secFormType={secFormType} />}
        layoutSidebar={<GetLayoutSidebar secRecent={secRecent} secRelated={secRelated} />}
        title={article.title}
        width="full"
      />
    </>
  );
};

export default SECFilingArticlePage;
