import ArticlePage from '../../[id]/page';
import { getArticles } from '../../../../_libs/getArticleData';

import { Metadata } from 'next';
import { MetaProps } from '@benzinga/seo';
import { setMetadata } from '../../../../_libs/setMetadata';
type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { props } = await getArticles(params.id);

  const meta = props?.article?.metaProps || ({} as MetaProps);
  return await setMetadata(meta);
}

const PressReleasesPage = props => {
  return <ArticlePage {...props} isTaggedPressRelease={true} />;
};

export default PressReleasesPage;
