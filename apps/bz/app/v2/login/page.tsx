import { MetaProps } from '@benzinga/seo';
import styles from './styles.module.scss';
import LoginHandler from './LoginHandler';
import { getLanguageCodeByHost, translate } from '@benzinga/translate';

import { headers } from 'next/headers';
import PageLayout from '../../_components/PageLayout';

export const metadata: MetaProps = {
  author: 'Benzinga',
  canonical: 'https://www.benzinga.com/login',
  description: translate('Auth.Meta.login-to-access-features', { ns: 'auth' }),
  title: translate('Auth.Meta.login-to-benzinga', { ns: 'auth' }),
};

async function getPageProps() {
  const headersList = headers();
  const host = headersList.get('host') ?? '';
  const language = getLanguageCodeByHost(host);
  const referer = headersList.get('referer');

  const props = {
    disablePushPrompt: true,
    headerProps: {
      hideBanner: true,
      hideFooter: true,
      hideNavigationBar: true,
      hideQuoteBar: true,
    },
    referer: referer ?? language !== 'en' ? `https://${host}/` : null,
  };
  return { props };
}

const LoginPage = async () => {
  const { props } = await getPageProps();

  return (
    <PageLayout pageProps={props}>
      <div className={styles.pageWrapper}>
        <LoginHandler referer={props.referer} />
      </div>
    </PageLayout>
  );
};

export default LoginPage;
