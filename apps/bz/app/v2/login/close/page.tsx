'use client';

import React from 'react';
import { useEffect } from 'react';
import { translate } from '@benzinga/translate';
import PageLayout from '../../../_components/PageLayout';

const PostLoginPage: React.FC = () => {
  const close = () => {
    window.opener = null;
    window.open('about:blank', '_self');
    window.close();
  };

  useEffect(() => {
    close();
  }, []);

  return (
    <PageLayout>
      <div className="p-8">
        {translate('Auth.Login.logged-in-success', { ns: 'auth' })}{' '}
        <button className="font-bold text-underline" onClick={close}>
          {translate('Buttons.close-window')}
        </button>
      </div>
    </PageLayout>
  );
};

export default PostLoginPage;
