'use client';
import styled from '@benzinga/themetron';
import React from 'react';
import { Button } from '@benzinga/core-ui';
import { SmartLink } from '@benzinga/analytics';
import Hooks from '@benzinga/hooks';
import {
  FusionSection,
  FusionSectionCaption,
  FusionSectionContent,
  FusionSectionText,
  FusionSectionTitle,
} from '@benzinga/ui';
import Image from 'next/image';

import styles from '../styles.module.scss';

const OurFounder = () => {
  const { width } = Hooks.useWindowSize();
  const isMobile = width <= 568;
  return (
    <FusionSection className={styles.ourFounderSection}>
      <FusionSectionContent className={styles.ourFounderSectionContent} paddingHorizontal={false}>
        <Image
          alt="Jason Raznick"
          className={styles.ourFounderImage}
          height={580}
          src="/next-assets/images/about/Jason-bg.png"
          width={817}
        />
        <div className="grow">
          <FusionSectionCaption className={styles.ourFounderSectionCaption}>OUR FOUNDER</FusionSectionCaption>
          <FusionSectionTitle className={styles.ourFounderSectionTitle}>
            Jason Raznick is the founder of Benzinga, a media and data technology startup empowering a new generation of
            investors
          </FusionSectionTitle>
          <FusionSectionText className={styles.ourFounderSectionText}>
            Raznick launched Benzinga.com in 2010, and it has since grown to become a hub for actionable information on
            the capital markets with approximately 25 million readers a month. Benzinga.com is supported by a high-speed
            newswire, Benzinga Pro, which is home to exclusive market-moving news.
          </FusionSectionText>

          {isMobile && (
            <div>
              <p className={styles.ourFounderName}>Jason Raznick</p>
              <p className={styles.ourFounderPosition}>BENZINGA FOUNDER</p>
            </div>
          )}
          <StyledSmartLink href="/inthenews" target="_self">
            <Button size="lg" variant="flat-blue">
              BENZINGA IN THE NEWS
            </Button>
          </StyledSmartLink>
        </div>
        {!isMobile && (
          <div>
            <p className={styles.ourFounderName}>Jason Raznick</p>
            <p className={styles.ourFounderPosition}>BENZINGA FOUNDER</p>
          </div>
        )}
      </FusionSectionContent>
    </FusionSection>
  );
};

export default OurFounder;

const StyledSmartLink = styled(SmartLink)`
  display: block;
  position: relative;
  z-index: 1;

  @media (max-width: 567px) {
    width: 100%;

    & button {
      width: 100%;
    }
  }
`;
