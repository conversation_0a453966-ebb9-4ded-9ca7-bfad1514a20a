import { getGlobalSession } from '../../../pages/api/session';
import { ContentManager, WordpressPost } from '@benzinga/content-manager';
import { presets as initialPresets, configurePreset, PresetType } from '@benzinga/scanner-widget';
import { ScannerManager, ScannerProtos } from '@benzinga/scanner-manager';
import { notFound } from 'next/navigation';

export interface AboutPageProps {
  post: WordpressPost | null;
  scannerData: ScannerProtos.IQueryResponse | null;
}

export const getPageData = async ({ preset: queryPreset }: { preset: PresetType }): Promise<AboutPageProps> => {
  try {
    const session = getGlobalSession();
    const contentManager = session.getManager(ContentManager);
    const postData = await contentManager.getPost(99053);

    if (queryPreset) {
      const availablePresets = ['largest-increase', 'largest-decrease', 'most-shorted'];

      if (!availablePresets.includes(queryPreset as string)) {
        return notFound();
      }
    }

    const scannerManager = session.getManager(ScannerManager);
    const presets = initialPresets.map(preset => configurePreset(preset, { limit: 100 }));
    const presetQuery = (presets.find(preset => preset.key === queryPreset) || presets[0])?.query;
    let scannerData: ScannerProtos.IQueryResponse | null = null;
    if (presetQuery) {
      const scannerResponse = await scannerManager.getInstrumentsWithIQuery(presetQuery);
      scannerData = scannerResponse?.ok ? JSON.parse(JSON.stringify(scannerResponse.ok)) : null;
    }

    return {
      post: postData?.ok ? postData.ok : null,
      scannerData,
    };
  } catch {
    return {
      post: null,
      scannerData: null,
    };
  }
};
