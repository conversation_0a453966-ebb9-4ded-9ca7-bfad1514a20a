import React from 'react';
import { Layout } from '@benzinga/core-ui';
import { PresetType } from '@benzinga/scanner-widget';

import { LayoutAbove } from './components/LayoutAbove';
import { LayoutMain } from './components/LayoutMain';
import { LayoutSidebar } from './components/LayoutSidebar';

import { getPageData } from './data';
import { PageMeta } from './meta';

export default async function Page({ params }: { params: { preset: PresetType } }) {
  const { post, scannerData } = await getPageData({ preset: params.preset });

  return (
    <>
      <PageMeta preset={params.preset} />
      <Layout
        layoutAbove={<LayoutAbove preset={params.preset} scannerData={scannerData} />}
        layoutMain={<LayoutMain post={post} />}
        layoutSidebar={<LayoutSidebar sidebar={post?.sidebar} />}
      />
    </>
  );
}
