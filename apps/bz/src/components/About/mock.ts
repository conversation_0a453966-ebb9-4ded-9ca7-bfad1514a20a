import { ReactElement } from 'react';
import { faBalanceScale } from '@fortawesome/pro-solid-svg-icons/faBalanceScale';
import { faCartPlus } from '@fortawesome/pro-light-svg-icons/faCartPlus';
import { faSearch } from '@fortawesome/pro-light-svg-icons/faSearch';
import { SocialButtonProps } from '@benzinga/ui';
import {
  AlertIcon,
  CalendarIcon,
  ChatIcon,
  MoversIcon,
  NewsfeedIcon,
  OptionsIcon,
  ScannerIcon,
  ScreenerIcon,
  SignalsIcon,
  SquawkIcon,
  Twitter,
} from '@benzinga/icons';

export const MoneyTabData = {
  B2B: {
    bottom_data: [],
    top_data: [],
  },
  Crypto: {
    bottom_data: [],
    top_data: [],
  },
  Education: {
    bottom_data: [],
    top_data: [],
  },
  Insurance: {
    bottom_data: [],
    top_data: [],
  },
  Investing: {
    bottom_data: [
      {
        image: '/next-assets/images/about/stock-brokers.svg',
        items: [
          {
            text: 'Brokerage for Stock Trading',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'Stock Brokers for Beginners',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'Forex Brokers',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'Options Broker Trading Platform',
            url: 'https://www.benzinga.com',
          },
        ],
        link: '/money/best-stock-brokers',
        title: 'Online Stock Brokers',
      },
      {
        image: '/next-assets/images/about/trade-forex.svg',
        items: [
          {
            text: 'How to Trade Cryptocurrency',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'How to Swing Trade Options',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'How to Buy Bonds',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'How to Buy ETFs',
            url: 'https://www.benzinga.com',
          },
        ],
        link: '/money/how-to-trade-forex',
        title: 'How to Trade Forex',
      },
      {
        image: '/next-assets/images/about/penny-stocks.svg',
        items: [
          {
            text: 'Best Swing Trade Stocks',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'Best Blue Chip Stocks',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'Most Volatile Stocks',
            url: 'https://www.benzinga.com',
          },
          {
            text: 'Best 5G Penny Stocks Right Now',
            url: 'https://www.benzinga.com',
          },
        ],
        link: '/best-penny-stocks',
        title: 'Penny Stocks Under $1',
      },
    ],
    top_data: [
      {
        icon: faBalanceScale,
        subtitle: "Let's start from the beginning",
        title: 'Compare stock brokers',
        url: 'https://www.benzinga.com',
      },
      {
        icon: faCartPlus,
        subtitle: 'All you need to know',
        title: 'How to buy stocks',
        url: 'https://www.benzinga.com',
      },
      {
        icon: faSearch,
        subtitle: 'Best stocks by industry',
        title: 'Find your next stock',
        url: 'https://www.benzinga.com',
      },
    ],
  },
  Mortgages: {
    bottom_data: [],
    top_data: [],
  },
  'Personal Finance': {
    bottom_data: [],
    top_data: [],
  },
};

// Make these link to the money pages

export const tabsData: { component: ReactElement | null; key: string; link: string; name: string }[] = [
  {
    component: null,
    key: '1',
    link: '/money/investing',
    name: 'Investing',
  },
  {
    component: null,
    key: '2',
    link: '/money/investing',
    name: 'Insurance',
  },
  {
    component: null,
    key: '3',
    link: '/money/mortgages',
    name: 'Mortgages',
  },
  {
    component: null,
    key: '4',
    link: '/money/education',
    name: 'Education',
  },
  {
    component: null,
    key: '5',
    link: '/business',
    name: 'B2B',
  },
  {
    component: null,
    key: '6',
    link: '/money/personal-finance',
    name: 'Personal Finance',
  },
  {
    component: null,
    key: '7',
    link: '/crypto',
    name: 'Crypto',
  },
];

export const proLinks = [
  {
    icon: AlertIcon,
    link: 'https://pro.benzinga.com/feature/alerts/',
    title: 'Alerts',
  },
  {
    icon: CalendarIcon,
    link: 'https://pro.benzinga.com/feature/calendar/',
    title: 'Calendar',
  },
  {
    icon: ChatIcon,
    link: 'https://pro.benzinga.com/feature/chat/',
    title: 'Chart',
  },
  {
    icon: MoversIcon,
    link: 'https://pro.benzinga.com/feature/movers/',
    title: 'Movers',
  },
  {
    icon: NewsfeedIcon,
    link: 'https://pro.benzinga.com/feature/newsfeed/',
    title: 'Newsfeed',
  },
  {
    icon: OptionsIcon,
    link: 'https://pro.benzinga.com/feature/options/',
    title: 'Options',
  },
  {
    icon: ScannerIcon,
    link: 'https://pro.benzinga.com/feature/scanner/',
    title: 'Scanner',
  },
  {
    icon: ScreenerIcon,
    link: 'https://pro.benzinga.com/feature/screener/',
    title: 'Screener',
  },
  {
    icon: SignalsIcon,
    link: 'https://pro.benzinga.com/feature/signals/',
    title: 'Signals',
  },
  {
    icon: SquawkIcon,
    link: 'https://pro.benzinga.com/feature/squawk/',
    title: 'Squawk',
  },
];

export const stories = [
  {
    description: 'From the rubble of the downturn, a new kind of investor emerged.',
    title:
      'With the collapse of Lehman Brothers and the financial crisis that followed, entire classes of market participants were wiped out.',
  },
  {
    description:
      'No longer does one have to rely on one firm or one analyst and pay large broker fees. Benzinga gives access to all that is available. Instead of giving financial advice, Benzinga gives you the news to make informed decisions to take control of your own financial future.',
    title: 'Benzinga empowers the individual investor by keeping them one step ahead.',
  },
  {
    description:
      'Dissatisfied with the dinosaurs of financial media, they craved a different, more engaging source of information.',
    title: 'This new investor is nimble, intelligent and creative.',
  },
  {
    description:
      'They needed a place that rolled all of this information into one financial media outlet. Against the backdrop of this revolution in financial journalism, Benzinga was born.',
    title:
      'They needed a place that combined real-time news with actionable trading ideas, a place that brought the insight of the biggest names and the brightest minds from across the country directly to their computer screens.',
  },
];

export const aboutAPIPills = [
  'Newswire & Article APIs',
  'Mission Critical Datasets',
  'Logos for Stocks and Mutual Funds',
  'Alternative data',
];

export const socialButtons: Array<SocialButtonProps> = [
  { icon: 'Facebook', key: 'Facebook', url: 'https://www.facebook.com/Benzinga' },
  { icon: 'Twitter', key: 'Twitter', url: 'https://twitter.com/benzinga' },
  { icon: 'Instagram', key: 'Instagram', url: 'https://www.instagram.com/benzinga' },
  { icon: 'YouTube', key: 'YouTube', url: 'https://www.youtube.com/benzinga' },
  { icon: 'SoundCloud', key: 'SoundCloud', url: 'https://soundcloud.com/bztv' },
  { icon: 'LinkedIn', key: 'LinkedIn', url: 'https://www.linkedin.com/company/benzinga' },
  { icon: 'TikTok', key: 'TikTok', url: 'https://www.tiktok.com/@benzinga' },
];

export const contactsList = [
  {
    description: "For questions regarding subscriptions to any of Benzinga's premium services",
    email: '<EMAIL>',
    link: 'https://help.benzinga.com/en/',
    linkLabel: 'Help Center',
    title: 'Subscriptions',
  },
  {
    description: 'For any questions regarding contributing to Benzinga',
    email: '<EMAIL>',
    link: 'https://contributor.benzinga.com/contributor-onboarding',
    linkLabel: 'Portal',
    title: 'Contributors',
  },
  {
    description: "To learn more about all of Benzinga's licensing opportunities",
    email: '<EMAIL>',
    link: 'https://www.benzinga.com/apis/',
    linkLabel: 'Website',
    title: 'Licensing',
  },
  {
    description: 'For information on advertising opportunities with Benzinga',
    email: '<EMAIL>',
    link: 'https://www.benzinga.com/advertising/',
    linkLabel: 'Website',
    title: 'Advertising',
  },
  {
    description: 'To send inquiries about news content',
    email: '<EMAIL>',
    title: 'News desk',
  },
  {
    description: 'For any inquiries regarding content',
    email: '<EMAIL>',
    title: 'Inquiries',
  },
  {
    description: "We're here to help",
    email: '<EMAIL>',
    phone: '18774409464',
    phoneLabel: '1-877-440-ZING (9464)',
    title: 'Customer Support',
  },
];
