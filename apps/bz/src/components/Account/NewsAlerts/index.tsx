'use client';

import React, { useState, useCallback, useEffect, useContext } from 'react';
import { HiOutlineExternalLink } from 'react-icons/hi';
import { FaBellSlash, FaCircleCheck } from 'react-icons/fa6';
import { TiInfoOutline } from 'react-icons/ti';
import { message, Popconfirm } from 'antd';
import { Spinner, Tooltip } from '@benzinga/core-ui';
import axios from 'axios';

import BodyCard from '../BodyCard';
import { SessionContext } from '@benzinga/session-context';
import { NewsAlertsManager, CategoriesAlert, AuthorAlert } from '@benzinga/news-alerts-manager';
import { WatchlistManager } from '@benzinga/watchlist-manager';
import { ContentManager } from '@benzinga/content-manager';
import { NotificationManager } from '@benzinga/notification-manager';

interface TopicAlert extends CategoriesAlert {
  url?: string;
}

export const NewsAlerts: React.FC = () => {
  const session = useContext(SessionContext);
  const newsAlertsManager = session.getManager(NewsAlertsManager);
  const [loading, setLoading] = useState<boolean>(true);
  const [categoryAlerts, setCategoryAlerts] = useState<TopicAlert[]>([]);
  const [authorAlerts, setAuthorAlerts] = useState<AuthorAlert[]>([]);
  const [watchlistCount, setWatchlistCount] = useState<number>(0);
  const [isPushNotificationsEnabled, setIsPushNotificationsEnabled] = useState<boolean>(false);

  const getCategoryTerms = useCallback(
    async alerts => {
      const termReq = alerts.map(alert => session.getManager(ContentManager).getTermById(alert.tids[0]));
      const res = await Promise.all(termReq);
      const termAlerts = alerts.map((alert, index) => ({
        ...alert,
        url: res[index]?.ok?.[0]?.url_public ?? `topic/${alert.id}`,
      }));
      return termAlerts;
    },
    [session],
  );

  useEffect(() => {
    const fetchAlerts = async () => {
      const watchlistRes = await session.getManager(WatchlistManager).getWatchlists();
      const watchlistAlerts = watchlistRes.ok?.filter(watchlist => watchlist.emailSummary);
      setWatchlistCount(watchlistAlerts?.length ?? 0);

      const categoryAlerts = await newsAlertsManager.getCategoriesAlerts();
      const subscribedCategories = categoryAlerts.ok?.filter(category => category.realtimeEmails) ?? [];
      const updatedCategoryAlerts = await getCategoryTerms(subscribedCategories);
      setCategoryAlerts(updatedCategoryAlerts);

      const authorAlerts = await newsAlertsManager.getAuthorSubscriptions();
      let updatedAuthorAlerts = authorAlerts.ok?.filter(author => author.send_realtime) ?? [];
      updatedAuthorAlerts = updatedAuthorAlerts.map(author => ({
        ...author,
        name: author.alert_name || 'Author',
        url: author.alert_name ? 'author/' + author.alert_name.replaceAll(' ', '-').toLowerCase() : null,
      }));
      setAuthorAlerts(updatedAuthorAlerts);

      setLoading(false);
    };

    if (loading) {
      fetchAlerts();
    }
  }, [session, newsAlertsManager, getCategoryTerms, loading]);

  useEffect(() => {
    const isPushEnabled = session.getManager(NotificationManager).isPushRegistered();
    if (isPushEnabled) {
      setIsPushNotificationsEnabled(true);
    }
  }, [session]);

  const handleDeleteAuthorAlert = async author => {
    const res = await newsAlertsManager.removeAuthorSubscription(author.alert_id);
    if (res.ok) {
      message.success(`${author.name} alert deleted`);
      setAuthorAlerts(authorAlerts.filter(alert => alert.alert_id !== author.alert_id));
    } else {
      message.error(`Unable to delete ${author.name} alert. Please try again later.`);
    }
  };

  const handleDeleteCategoryAlert = async category => {
    const res = await newsAlertsManager.setCategoriesAlert(category.id, { realtimeEmail: false });
    if (res.ok) {
      message.success(`${category.name} alert deleted`);
      setCategoryAlerts(categoryAlerts.filter(alert => alert.id !== category.id));
    } else {
      message.error(`Unable to delete ${category.name} alert. Please try again later.`);
    }
  };

  const handleUnsubscribeAll = async () => {
    try {
      const response = await newsAlertsManager.getUnsubscribeAllKey();
      if (response.err) {
        throw new Error('Unable to get unsubscribe key');
      }

      const key = response.ok?.data?.unsub_all_key;
      const unsubscribe = await axios.get(
        `https://www.benzinga.com/profile/unsubscribe?key=${key}&type=realtime&scope=all`,
      );
      if (unsubscribe.status !== 200) {
        throw new Error('Unable to unsubscribe all alerts');
      }

      message.success('All alerts unsubscribed successfully');
      setAuthorAlerts([]);
      setCategoryAlerts([]);
      setWatchlistCount(0);
    } catch (err) {
      console.log(err);
      message.error('Unable to unsubscribe all alerts. Please try again later.');
    }
  };

  return (
    <BodyCard title="News Alerts">
      <h1 className="section-heading">News Alerts</h1>
      <div className="section-body-wrapper flex-col">
        <div className="flex flex-col gap-6">
          {loading ? (
            <div className="bg-slate-300 rounded-lg animate-pulse w-full h-12"></div>
          ) : (
            <AlertsSummary
              authorCount={authorAlerts.length}
              categoryCount={categoryAlerts.length}
              watchlistCount={watchlistCount}
            />
          )}
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <a
              className="px-4 py-2 border border-slate-600 rounded-sm flex items-center gap-1 hover:bg-white text-[#2d2d2d]"
              href="https://www.benzinga.com/profile/portfolio/?action=notifications"
              target="_blank"
            >
              Manage Watchlist Alerts <HiOutlineExternalLink />
            </a>
            <Popconfirm
              cancelText="No"
              okText="Yes"
              onCancel={e => {
                e?.stopPropagation();
              }}
              onConfirm={handleUnsubscribeAll}
              title="Are you sure? This action cannot be reversed."
            >
              <button className="px-4 py-2 border border-slate-600 rounded-sm flex items-center gap-1 hover:bg-white">
                <FaBellSlash />
                Unsubscribe to all Alerts
              </button>
            </Popconfirm>
          </div>
          {isPushNotificationsEnabled && <NotificationIndicator />}
        </div>
      </div>
      <div className="mb-8">
        <h3 className="section-heading">Category Alerts</h3>
        {loading && <Spinner />}
        {!loading &&
          categoryAlerts.map((category, index) => (
            <AlertRow alert={category} key={`category-${index}`} onDelete={handleDeleteCategoryAlert} />
          ))}
        {!loading && categoryAlerts.length === 0 && <p className="w-full text-center">No category alerts</p>}
      </div>
      <div>
        <h3 className="section-heading">Author Alerts</h3>
        {loading && <Spinner />}
        {!loading &&
          authorAlerts.map((author, index) => (
            <AlertRow alert={author} key={`author-${index}`} onDelete={handleDeleteAuthorAlert} />
          ))}
        {!loading && authorAlerts.length === 0 && <p className="w-full text-center">No author alerts</p>}
      </div>
    </BodyCard>
  );
};

const AlertsSummary = ({ authorCount, categoryCount, watchlistCount }) => {
  const summary = {
    author: authorCount,
    category: categoryCount,
    watchlist: watchlistCount,
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 animate-fade-down">
      {Object.keys(summary)
        .reverse()
        .map(type => {
          return (
            <div className="flex flex-row gap-2" key={type}>
              <div className="font-bold text-5xl">{summary[type]}</div>
              <div className="capitalize">
                {type}
                <br />
                alerts
              </div>
            </div>
          );
        })}
    </div>
  );
};

const NotificationIndicator = () => {
  return (
    <div className="flex flex-row items-center gap-1 animate-fade-down">
      <FaCircleCheck className="text-green-600" />
      Push Notifications are enabled
      <Tooltip content="Notifications can be disabled through your browser settings." size="lg">
        <TiInfoOutline />
      </Tooltip>
    </div>
  );
};

const AlertRow = ({ alert, onDelete }) => {
  return (
    <div className="flex flex-row justify-between pb-1 border-b border-slate-300 mb-2 gap-2 items-center animate-fade-down">
      <div>
        {alert.url ? (
          <a
            className="text-black font-bold group flex flex-row items-center gap-1 w-fit"
            href={window.location.origin + `/${alert.url}`}
            target="_blank"
          >
            {alert.name}
            <HiOutlineExternalLink className="hidden group-hover:block" />
          </a>
        ) : (
          <div className="text-black font-bold">{alert.name}</div>
        )}
        {alert.description && <p className="text-xs">{alert.description}</p>}
      </div>
      <button
        className="px-2 py-1 bg-bzblue-1100 text-white text-sm rounded-sm h-fit hover:bg-bzblue-900/80"
        onClick={() => onDelete(alert)}
      >
        Delete
      </button>
    </div>
  );
};

export default NewsAlerts;
