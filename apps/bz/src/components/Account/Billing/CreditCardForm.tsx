'use client';
import React, { DOMAttributes, ReactElement } from 'react';
import { FieldError, useForm, Controller } from 'react-hook-form';
import { Form, message } from 'antd';

import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { StripeCardElement, StripeCardElementChangeEvent } from '@stripe/stripe-js';
import styled, { css, TC } from '@benzinga/themetron';
import { Button } from '@benzinga/core-ui';
import { SessionContext } from '@benzinga/session-context';
import { useUser } from '@benzinga/user-context';
import { ShopManager } from '@benzinga/shop-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { Close, MasterCard, Discover, Visa, AmericanExpress } from '@benzinga/themed-icons';

const { Item: FormItem } = Form;

interface StripeFieldProps {
  onChange: (event: StripeCardElementChangeEvent) => void;
  onBlur: () => void;
  invalid: boolean;
  isTouched: boolean;
  isDirty: boolean;
  error?: FieldError;
  value: StripeCardElementChangeEvent;
}

type ValidationStatus = 'error' | 'success' | 'warning' | 'validating';

const StripeField: React.FC<StripeFieldProps> = props => {
  const stripeElement = React.useRef<StripeCardElement | null>(null);
  const [showClear, setShowClear] = React.useState(() => false);

  const getValidationStatus = (): ValidationStatus | undefined => {
    const { invalid, isTouched } = props;

    if (isTouched) {
      if (invalid) {
        return 'error';
      } else {
        return 'success';
      }
    }

    return undefined;
  };

  const getValidationMessage = (): string => {
    const { value } = props;

    const postalCodeLength = value?.value?.postalCode.length;

    if (postalCodeLength > 10) {
      return 'Postal code value must be less than 10';
    }

    if (!value || value.empty || !value.complete) {
      return 'Card is required';
    }

    if (value.error) {
      return value.error.message;
    }

    return '';
  };

  const handleChange = React.useCallback(
    (stripeCard: StripeCardElementChangeEvent): any => {
      const onChange = props.onChange;
      onChange(stripeCard);
    },
    [props.onChange],
  );

  return (
    <FormItemCard
      className="form-item--hide-feedback-icon"
      help={getValidationMessage()}
      validateStatus={getValidationStatus()}
    >
      <StyledDiv>
        <CardElementStyled
          onBlur={props.onBlur}
          onChange={handleChange}
          onReady={c => {
            stripeElement.current = c;
            c.on('change', e => {
              setShowClear(!e?.empty);
            });
          }}
        />
        <IconDiv $showClear={showClear} onClick={() => stripeElement.current?.clear()}>
          <Close />
        </IconDiv>
      </StyledDiv>
    </FormItemCard>
  );
};

const StyledDiv = styled.div`
  align-items: center;
  display: flex;
  flex-direction: row;
  font-size: 1.5em;
  justify-content: center;
  width: 100%;
`;

const FormItemCard = styled(FormItem)`
  width: 100%;
  margin: 0px !important;
`;

const CardElementStyled = styled(CardElement)`
  width: 100%;
  ${TC.fontFamilyStyles}
`;

const IconDiv: React.FC<{ $showClear: boolean; children: any } & DOMAttributes<ReactElement>> = styled.div(
  (props: { $showClear: boolean }) => `
  border-left: 1px solid ${props => props.theme.colors.background};
  width: 28px;
  height: 28px;
  text-align: center;
  background-color: ${props.$showClear ? 'white' : 'transparent'};

  svg {
    fill:  ${props.$showClear ? '#777' : '#c8c8c8'};
  }
`,
);

const PaymentImageContainer = styled.div`
  align-items: center;
  display: flex;
  margin-bottom: 5px;
`;

interface Props {
  onSubmit: any;
}

export const CreditCardsIcons = () => {
  return (
    <PaymentImageContainer>
      <StyledMastercardIcon />
      <StyledDiscoverIcon />
      <StyledVisaIcon />
      <StyledAmericanExpressIcon />
    </PaymentImageContainer>
  );
};

export const StripeFormSection: React.FunctionComponent<Props> = props => {
  const { control, handleSubmit } = useForm({});
  return (
    <form onSubmit={handleSubmit(props.onSubmit)}>
      <div>
        <CreditCardsIcons />
        <Controller
          control={control}
          name="card"
          render={({ field, fieldState }) => <StripeField {...field} {...fieldState} />}
          rules={{ required: true }}
        />
      </div>
    </form>
  );
};

const IconCss = css`
  display: inline-block;
  margin-right: 5px;
`;

const StyledMastercardIcon = styled(MasterCard)`
  ${IconCss}
`;
const StyledDiscoverIcon = styled(Discover)`
  ${IconCss}
`;
const StyledVisaIcon = styled(Visa)`
  ${IconCss}
`;
const StyledAmericanExpressIcon = styled(AmericanExpress)`
  ${IconCss}
`;

export const StripeCreditCardForm = () => {
  const session = React.useContext(SessionContext);
  const user = useUser();
  const stripe = useStripe();
  const elements = useElements();
  const [cardErr, setCardErr] = React.useState<string>('');
  const [buttonDisabled, setButtonDisabled] = React.useState(false);

  const handleCardSubmit = async () => {
    setCardErr('');
    if (!stripe) {
      session.getManager(TrackingManager).trackErrorEvent('emit', {
        error_message: 'Stripe not present',
      });
      return;
    }
    session.getManager(TrackingManager).trackFormEvent('submit', 'stripe-credit-card-form', {
      form_type: 'Credit Card',
    });
    try {
      if (elements) {
        setButtonDisabled(true);
        const card = elements.getElement(CardElement);
        if (card === null) {
          throw 'could not get card';
        }
        const cardToken = await stripe.createToken(card);
        if (cardToken.token === undefined) {
          throw 'could not get create token';
        }

        const result = await session.getManager(ShopManager).addCreditCard(cardToken.token.id);
        if (result.err) {
          const errorMessage = await (result?.err?.data as Response).json();
          setCardErr(errorMessage?.[0] ?? 'There was a problem adding your card. Please try again.');
          throw errorMessage ?? result;
        } else {
          message.success('Successfully added card!');
          card.clear();
        }
        setButtonDisabled(false);
      }
    } catch (err) {
      setButtonDisabled(false);
      console.log('Error adding Card', err);
    }
  };

  return (
    <div className="flex flex-col">
      <StripeFormSection onSubmit={handleCardSubmit} />
      <Button
        className={`w-full ${buttonDisabled ? 'cursor-wait' : ''}`}
        disabled={buttonDisabled}
        onClick={handleCardSubmit}
      >
        {buttonDisabled ? 'Adding Card...' : 'Add Card'}
      </Button>
      {cardErr && <div className="text-red-500">{cardErr}</div>}
    </div>
  );
};
