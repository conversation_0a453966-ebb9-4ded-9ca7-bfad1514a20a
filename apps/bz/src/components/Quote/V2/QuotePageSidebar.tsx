import React from 'react';
import { useBenzingaEdge } from '@benzinga/edge';
import { NoFirstRender, useFirstInteractionMade } from '@benzinga/hooks';
import AboutQuote from './AboutQuote';
import { MetricsSidebar } from './MetricsSidebar';
import { Profile } from '@benzinga/content-manager';
import { QuoteProfile } from '../../../entities/quoteEntity';
import ETFQuoteSidebar from './ETFQuoteSidebar';

const ConnatixLivePlayer = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.ConnatixLivePlayer,
  })),
);

const WNSTNWidgetQuote = React.lazy(() =>
  import('@benzinga/ads').then(module => ({
    default: module.WNSTNWidgetQuote,
  })),
);

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const ConnatixInitialHeadScript = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.ConnatixInitialHeadScript };
  }),
);

export interface QuoteSidebarProps {
  advertiserProfile?: Profile | null;
  isAdvertiser: boolean;
  isCrypto?: boolean;
  isETF: boolean;
  profile: QuoteProfile;
  symbol: string;
}

export const QuotePageSidebar: React.FC<QuoteSidebarProps> = ({
  advertiserProfile,
  isAdvertiser,
  isCrypto,
  isETF,
  profile,
}) => {
  const wasFirstInteractionMade = useFirstInteractionMade();
  const hasAdLight = useBenzingaEdge().adLightEnabled;
  return (
    <>
      {!hasAdLight && <ConnatixInitialHeadScript cid="89cc050a-c0fc-4f72-a746-d63fbd3dee72" />}
      <div className="w-full lg:w-4/12 flex flex-col items-center gap-8 md:gap-4">
        <div className="metrics-desktop w-full">
          <MetricsSidebar
            advertiserProfile={advertiserProfile}
            isAdvertiser={isAdvertiser}
            isCrypto={isCrypto}
            isETF={isETF}
            profile={profile}
          />
        </div>
        {!isAdvertiser && !isCrypto && (
          <AboutQuote advertiserProfile={advertiserProfile} isAdvertiser={isAdvertiser} profile={profile} />
        )}
        <div className="flex flex-col gap-8 md:gap-4 px-4 md:mt-4">
          {wasFirstInteractionMade && !hasAdLight && (
            <div className="min-h-[220px] w-full">
              <NoFirstRender>
                <ConnatixLivePlayer
                  cid="89cc050a-c0fc-4f72-a746-d63fbd3dee72"
                  id="c1a731da3b7e4365831fbd05ef0e83b2"
                  pid="775ce337-8729-4abf-9ac1-35656aa1d770"
                />
              </NoFirstRender>
            </div>
          )}
          {/* <WNSTNWidgetQuote isCrypto={isCrypto} symbol={symbol} /> */}
          <RaptiveAdPlaceholder
            className="w-[300px] overflow-hidden"
            disableMargins={true}
            onlyDesktop={true}
            type="static-sidebar"
          />
          <RaptiveAdPlaceholder
            className="w-[300px] overflow-hidden"
            disableMargins={true}
            onlyMobile={true}
            type="content-small"
          />
          {isETF && <ETFQuoteSidebar profile={profile} />}
          {!advertiserProfile && (
            <>
              <RaptiveAdPlaceholder className="w-[300px] overflow-hidden" onlyDesktop={true} type="static-sidebar" />
              <RaptiveAdPlaceholder className="w-[300px] overflow-hidden" onlyDesktop={true} type="static-sidebar" />
            </>
          )}
        </div>
      </div>
    </>
  );
};
