import { StatBoxesContainer } from '@benzinga/core-ui';
import i18n from '@benzinga/translate';
import { formatPercentage, numberShorthand } from '@benzinga/utils';
import type { ShortInterestSummary } from '../../../../pages/api/quote/[ticker]/short-interest';
import { useTranslation } from 'react-i18next';
import StatBox from './StatBox';

export const ShortInterestSummaryBox: React.FC<{ shortInterestSummary: ShortInterestSummary }> = ({
  shortInterestSummary,
}) => {
  const { t } = useTranslation('quote', { i18n });

  const shortInterest = shortInterestSummary?.last ?? null;

  return (
    <>
      <StatBoxesContainer>
        {shortInterest && (
          <StatBox
            title={t('Quote.ShortInterest.short-interest')}
            value={
              shortInterest.totalShortInterest ? numberShorthand(Number(shortInterest.totalShortInterest), 2) : '–'
            }
          />
        )}
        {shortInterest && (
          <StatBox
            title={t('Quote.ShortInterest.short-interest') + ' %'}
            value={shortInterest.shortPercentOfFloat ? `${formatPercentage(shortInterest.shortPercentOfFloat)}%` : '–'}
          />
        )}
        {shortInterest && (
          <StatBox title={t('Quote.ShortInterest.days-to-cover')} value={`${shortInterest.daysToCover}` ?? '–'} />
        )}
      </StatBoxesContainer>
    </>
  );
};

export default ShortInterestSummaryBox;
