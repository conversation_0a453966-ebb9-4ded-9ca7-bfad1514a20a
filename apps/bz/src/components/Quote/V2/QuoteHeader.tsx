'use client';
import React, { useMemo } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import { faChevronUp } from '@fortawesome/pro-regular-svg-icons';

import i18n from '@benzinga/translate';
import styled from '@benzinga/themetron';
import { AddToWatchlist } from '@benzinga/watchlist-ui';
import { GetReportButton } from '@benzinga/ticker-ui';
import { Button, ErrorBoundary, Icon } from '@benzinga/core-ui';
import { isMobile } from '@benzinga/device-utils';
import Hooks from '@benzinga/hooks';

import { Wiim } from '../../Wiim';
import { QuoteActions } from '../QuoteActions';
import { CompanyLogo } from '../../CompanyLogo';
import { StockInfoMessages } from '../StockInfoMesages';
import { QuoteProfile } from '../../../entities/quoteEntity';
import type { ActiveTab } from '../../../quoteV2Utils';
import { getMainPriceBoxValues, getSecondaryPriceBoxValues, QuoteHeaderPriceBox } from './QuoteHeaderPriceBox';
import SymbolExchangeInformation from './SymbolExchangeInformation';
import { Quote } from '@benzinga/quotes-v3-manager';
import { SessionContext } from '@benzinga/session-context';
import { useQuoteSubscription } from '@benzinga/quotes-v3-manager-hooks';
import { useBenzingaEdge } from '@benzinga/edge';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const CommentsDrawerButton = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsDrawerButton })),
);

const CommentsEmbedScript = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsEmbedScript })),
);

const CommentsDrawer = React.lazy(() =>
  import('@benzinga/comments-ui').then(module => ({ default: module.CommentsDrawer })),
);

type TabTitle =
  | 'Overview'
  | 'Analyst Ratings'
  | 'News'
  | 'Guidance'
  | 'Dividends'
  | 'Earnings'
  | 'Option'
  | 'Insider Trades'
  | 'Ideas'
  | 'Short Interest'
  | 'Government Trades'
  | `${string} Forecast`;

interface TabProp {
  href: string;
  title: TabTitle;
  key: ActiveTab;
}

export interface QuoteHeaderProps {
  advertiserProfile?: any;
  defaultPath?: ActiveTab;
  isAdvertiser?: boolean;
  isCrypto?: boolean;
  profile: QuoteProfile;
  showGovLink?: boolean;
  symbol: string;
}

const headerTagKeyForSEO = [
  'profile',
  'news',
  'dividends',
  'short-interest',
  'earnings',
  'guidance',
  'analyst-ratings',
  'insider-trades',
];

export const QuoteHeader = ({
  advertiserProfile,
  defaultPath,
  isAdvertiser,
  isCrypto,
  profile,
  showGovLink,
  symbol,
}: QuoteHeaderProps) => {
  const session = React.useContext(SessionContext);
  const { t } = useTranslation('quote', { i18n });
  const [isCommentsDrawerOpen, setIsCommentsDrawerOpen] = React.useState(false);
  const [areQuoteActionsHidden, setAreQuoteActionsHidden] = React.useState(false);
  const quoteHeaderRef = React.useRef<HTMLDivElement>(null);
  const mainQuoteTabsWrapperElementRef = React.useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const templateVariant = i18n.language !== 'en' ? 'simple' : 'default';
  const fields = useMemo<Set<keyof Quote>>(
    () => new Set(['symbol', 'price', 'changePercent', 'change', 'lastTradeTime', 'previousClose', 'close']),
    [],
  );
  const quotes = useQuoteSubscription(symbol, fields);
  const hasAdLight = useBenzingaEdge().adLightEnabled;

  React.useEffect(() => {
    const isCommentsDrawerOpenByDefault = searchParams?.get('comments_open') === 'true';
    if (isCommentsDrawerOpenByDefault) {
      setIsCommentsDrawerOpen(true);
    }
  }, [searchParams]);

  const { isBreakingNewsBannerVisible, isStickyNavbar, position } = Hooks.useDetectStickyHeader();
  const { height: navigationHeaderHeight, scrollY, top: navigationHeaderTopOffset } = position;

  const scrollToElement = Hooks.useHorizontalScrollIntoView('active-tab-item');

  React.useEffect(() => {
    isMobile() && scrollToElement();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const quoteHeaderHeight = quoteHeaderRef.current?.clientHeight || 0;
  const shouldStickyHeaderStylesBeApplied = scrollY === 0 ? false : scrollY >= quoteHeaderHeight;
  const topOffset = isBreakingNewsBannerVisible
    ? navigationHeaderTopOffset + (isStickyNavbar ? 0 : 49)
    : navigationHeaderTopOffset;
  const name =
    (isCrypto ? profile.cryptoData?.name || profile.richQuoteData?.description : profile.richQuoteData?.name) || '';

  const tabs: TabProp[] = React.useMemo(() => {
    if (profile?.richQuoteData?.type === 'CRYPTO') return [];
    if (profile?.richQuoteData?.type === 'ETF') {
      // tabs = [profileTab, newsTab, dividendsTab, tradeIdeasTab, keyStatsTab];
      return [
        {
          href: `/quote/${symbol}`,
          key: 'profile',
          title: t('Quote.Nav.overview'),
        },
        {
          href: `/quote/${symbol}/news`,
          key: 'news',
          title: t('Quote.Nav.news'),
        },
        {
          href: `/quote/${symbol}/holdings`,
          key: 'holdings',
          title: t('Quote.Nav.holdings'),
        },
        {
          href: `/quote/${symbol}/dividends`,
          key: 'dividends',
          title: t('Quote.Nav.dividends'),
        },
        {
          href: `/quote/${symbol}/short-interest`,
          key: 'short-interest',
          title: t('Quote.Nav.short-interest'),
        },
      ];
    }
    const stockTabs: TabProp[] = [
      {
        href: `/quote/${symbol}`,
        key: 'profile',
        title: t('Quote.Nav.forecast', { company: name, symbol: symbol }),
      },
      {
        href: `/quote/${symbol}/news`,
        key: 'news',
        title: t('Quote.Nav.news'),
      },
      {
        href: `/quote/${symbol}/earnings`,
        key: 'earnings',
        title: t('Quote.Nav.earnings'),
      },
      {
        href: `/quote/${symbol}/option-chain`,
        key: 'option-chain',
        title: t('Quote.Nav.option', { company: name, symbol: symbol }),
      },
      {
        href: `/quote/${symbol}/guidance`,
        key: 'guidance',
        title: t('Quote.Nav.guidance'),
      },
      {
        href: `/quote/${symbol}/dividends`,
        key: 'dividends',
        title: t('Quote.Nav.dividends'),
      },
      {
        href: `/quote/${symbol}/analyst-ratings`,
        key: 'analyst-ratings',
        title: t('Quote.Nav.analyst-ratings'),
      },
      {
        href: `/quote/${symbol}/insider-trades`,
        key: 'insider-trades',
        title: t('Quote.Nav.insider-trades'),
      },
      {
        href: `/quote/${symbol}/short-interest`,
        key: 'short-interest',
        title: t('Quote.Nav.short-interest'),
      },
      // {
      //   href: `/quote/${symbol}/ideas`,
      //   key: 'ideas',
      //   title: 'Ideas',
      // },
    ];
    if (showGovLink) {
      stockTabs.splice(stockTabs.length - 1, 0, {
        href: `/gov-trades/securities/${symbol}`,
        title: t('Quote.Nav.government-trades'),
      } as TabProp);
    }

    return stockTabs;
  }, [name, profile?.richQuoteData?.type, showGovLink, symbol, t]);

  const [activeTab, setActiveTab] = React.useState(tabs.find(tab => tab.key === defaultPath));

  React.useEffect(() => {
    const tab = tabs.find(tab => tab.href === pathname);
    if (tab) setActiveTab(tab);
  }, [pathname, tabs]);

  const handleShowMoreQuoteActions = () => {
    setAreQuoteActionsHidden(!areQuoteActionsHidden);
  };

  const handleArticleCommentOpenAnalyticsEvent = () => {
    import('@benzinga/comments-ui').then(module => {
      module.handleCommentsAnalyticsEvent('Quote', 'open-drawer', session);
    });
  };

  const handleOpenCommentsSidebar = () => {
    setIsCommentsDrawerOpen(true);
    handleArticleCommentOpenAnalyticsEvent();
  };

  const handleCloseCommentsSidebar = () => {
    setIsCommentsDrawerOpen(false);
  };

  const advertiserImage = advertiserProfile?.entity?.client_header_image;

  const QuoteActionsContainer = ({ showGetReportButton = true }) => (
    <QuoteActions
      className="bottom-buttons"
      compareBrokersButtonVariant={templateVariant === 'simple' ? 'light-gray' : 'flat-light-blue'}
      getReportButtonVariant={templateVariant === 'simple' ? 'dark-blue' : 'flat-blue'}
      isCrypto={isCrypto}
      perksButtonVariant={templateVariant === 'simple' ? 'light-gray' : 'flat-light-blue'}
      quoteBuyButtonVariant={templateVariant === 'simple' ? 'light-gray' : 'flat-light-blue'}
      showGetReportButton={showGetReportButton}
      symbol={symbol}
    />
  );
  // Determine if the quote is real-time. If the lastTradeTime is present on quotes, the quote is real-time.
  const isRealTime = Boolean(quotes?.lastTradeTime);
  const doesMainPriceExist = !!getMainPriceBoxValues(profile, quotes as Quote, isRealTime).price;
  const doesSecondaryPriceExist = !!getSecondaryPriceBoxValues(profile).price;
  const areBothPriceBoxesShown = doesMainPriceExist || doesSecondaryPriceExist;

  const headerTag =
    activeTab?.key && headerTagKeyForSEO.includes(activeTab.key)
      ? t(`Quote.HeaderTags.${activeTab.key}`, { stockName: name })
      : null;

  return (
    <QuoteHeaderWrapper
      $areBothPriceBoxesShown={areBothPriceBoxesShown}
      $areQuoteActionsHidden={areQuoteActionsHidden}
      $isAdvertiser={isAdvertiser && advertiserImage}
      $isCrypto={!!isCrypto}
      $isLightHeader={advertiserProfile?.entity?.is_light_header && advertiserImage}
      $isStickyHeader={shouldStickyHeaderStylesBeApplied}
      $navigationHeaderHeight={navigationHeaderHeight}
      $topOffset={topOffset}
      $variant={i18n.language !== 'en' ? 'simple' : 'default'}
      className="quote-header"
      ref={quoteHeaderRef}
    >
      <ErrorBoundary name="quote-header-comments-embed-script">
        <CommentsEmbedScript
          id={symbol}
          isInjected={isCommentsDrawerOpen}
          type="Quote"
          url={`https://www.benzinga.com/quote/${symbol}`}
        />
      </ErrorBoundary>

      <ErrorBoundary name="quote-header-comments-drawer">
        <CommentsDrawer isOpen={isCommentsDrawerOpen} onClose={handleCloseCommentsSidebar} />
      </ErrorBoundary>

      {isAdvertiser && advertiserImage && (
        <HeaderAdvertiserImage
          $url={advertiserProfile?.entity?.client_header_image}
          className="header-advertiser-image"
        />
      )}
      <div className="header-top-half flex items-center justify-between w-full main-div">
        <div className="flex flex-col">
          <div className="lg:text-lg flex items-center">
            <CompanyLogo logo={profile.logoUrl ?? ''} name={name} wrapperClassName="ticker-logo" />
            <div className="flex gap-2 header-left-text items-center">
              <div className="flex items-center gap-2">
                <div className="flex flex-col items-start ml-2">
                  <h1 className="quote-name text-bzblue-1000 text-3xl">{headerTag || name}</h1>
                  <div className="flex items-center gap-3 mt-1">
                    <SymbolExchangeInformation
                      className="sm:text-sm text-grey-darker"
                      exchange={(profile.richQuoteData?.bzExchange ?? null) as string}
                      otcTier={(profile.richQuoteData?.otcTier ?? null) as string}
                      symbol={symbol}
                    />
                    <div className="hidden md:flex gap-3 mr-2">
                      <div className="md:ml-1 items-center w-full add-to-watchlist-wrapper">
                        <AddToWatchlist
                          buttonText={t('Buttons.watchlist')}
                          buttonVariant={templateVariant === 'simple' ? 'dark-blue' : 'flat-blue'}
                          showIcon={true}
                          symbol={profile.symbol}
                          variant="default"
                        />
                      </div>
                      <div className="leave-a-comment-wrapper whitespace-nowrap">
                        <CommentsDrawerButton
                          commentMessage={t('Comments.leave-a-comment')}
                          onClick={handleOpenCommentsSidebar}
                          variant="flat-blue"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <a
            className="benzinga-logo-copy text-sm text-blue-500 mt-2"
            href="https://www.benzinga.com/apis/cloud-product/company-logo-api/"
            rel="noreferrer noopener"
            target="_blank"
          >
            {t('Quote.About.logo-brought')}
          </a>
        </div>
        <span className="md:hidden">
          <AddToWatchlist buttonVariant="flat-blue" symbol={profile.symbol} variant="compact" />
        </span>
        <div className="quote-actions-desktop">
          <QuoteActionsContainer />
        </div>
      </div>
      <div className="quote-actions-tablet main-div">
        <QuoteActionsContainer />
      </div>
      <div className="main-div flex justify-between">
        <QuoteHeaderPriceBox isCrypto={isCrypto} isRealTime={isRealTime} profile={profile} quotes={quotes as Quote} />
        {!hasAdLight && (
          <RaptiveAdPlaceholder
            className="ml-auto mr-[unset] raptive-header-ad"
            onlyDesktop={true}
            type="header-small"
          />
        )}
      </div>
      {!isAdvertiser && (
        <div className="main-div">
          <div className="w-full my-4">
            <ErrorBoundary name="wiim">
              <Wiim wiim={profile.wiim} />
            </ErrorBoundary>
          </div>
        </div>
      )}
      <div className="main-div md:hidden w-full flex flex-col gap-2 mb-2">
        <div className="flex gap-2 items-center w-full">
          <GetReportButton ticker={profile?.symbol} variant={'flat-blue'} />
          <div className="flex items-center w-full">
            <div className="leave-a-comment-wrapper whitespace-nowrap w-full">
              <CommentsDrawerButton commentMessage="Comment" onClick={handleOpenCommentsSidebar} variant="flat-blue" />
            </div>
          </div>
          <Button
            aria-label="Toggle more quote actions"
            className={classNames('more-quote-actions-button', { active: areQuoteActionsHidden })}
            onClick={handleShowMoreQuoteActions}
            variant="flat-light-blue"
          >
            {<Icon icon={areQuoteActionsHidden ? faChevronUp : faChevronDown} />}
          </Button>
        </div>
        {areQuoteActionsHidden && <QuoteActionsContainer showGetReportButton={false} />}
      </div>
      <div className="main-div">
        <ErrorBoundary name="stock-info-messages">
          <StockInfoMessages profile={profile} />
        </ErrorBoundary>
      </div>
      <div className="mx-2">
        <div className="main-quote-tabs-wrapper" ref={mainQuoteTabsWrapperElementRef}>
          <div className="main-quote-tabs">
            {tabs.map((tab, index) => {
              if (activeTab === tab) {
                return (
                  <h2 className="tab active" id="active-tab-item" key={index}>
                    {tab.title}
                  </h2>
                );
              }
              return (
                <a className="tab" href={tab.href} key={index}>
                  {tab.title}
                </a>
              );
            })}
          </div>
        </div>
      </div>
    </QuoteHeaderWrapper>
  );
};

const QuoteHeaderWrapper = styled.div<{
  $isAdvertiser: boolean;
  $isCrypto: boolean;
  $isLightHeader: boolean;
  $areBothPriceBoxesShown: boolean;
  $areQuoteActionsHidden: boolean;
  $clientHeaderImage?: string;
  $isStickyHeader?: boolean;
  $topOffset?: number;
  $navigationHeaderHeight?: number;
  $variant?: string;
}>`
  color: #000000;
  min-height: 125px;
  position: relative;

  &.quote-header {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    background: ${({ $variant }) => ($variant === 'simple' ? `none` : `linear-gradient(#f3f9ff, #e1ebfa)`)};
    border-bottom: ${({ $variant }) => ($variant === 'simple' ? `1px solid #ddd` : `none`)};

    .header-top-half {
      padding-top: 0.85rem;
      padding-bottom: 0.85rem;
    }
  }

  .raptive-header-ad {
    display: none;
    margin-top: unset;
    margin-bottom: unset;
    @media (min-width: ${({ $areBothPriceBoxesShown }) => ($areBothPriceBoxesShown ? `1100px` : `800px`)}) {
      display: flex;
    }
  }

  .raptive-ben-header-sml {
    #AdThrive_Header_1_desktop {
      min-width: 320px !important;
      max-width: 320px !important;
    }
  }

  .quote-actions-desktop {
    display: none;

    @media (min-width: ${({ $isCrypto }) => ($isCrypto ? `768px` : `940px`)}) {
      display: block;
    }
  }

  .quote-actions-tablet {
    display: none;
    max-width: 400px;
    margin-left: unset;
    margin-right: unset;
    ${({ $isCrypto }) =>
      !$isCrypto &&
      `
        @media (min-width: 768px) {
          display: block;
        }
        @media (min-width: 940px) {
          display: none;
        }
    `}
  }

  .add-to-watchlist-wrapper {
    width: 100%;

    button {
      padding: 0.35rem 0.75rem;
    }
  }

  .comments-count-button {
    button {
      width: 100%;
    }
  }

  .get-report-button {
    white-space: nowrap;
    width: 100%;
    button {
      padding: 0.5rem 0.75rem;
    }
  }

  .more-quote-actions-button {
    background: #3f83f81a;
    box-shadow: unset;
  }

  .more-quote-actions-button {
    height: 34px;
    width: 40px;
  }

  button.add-to-watchlist-button {
    box-shadow: unset;
  }

  button.add-to-watchlist-button.compact {
    padding: 1rem 0.75rem;
  }

  button.more-quote-actions-button {
    width: 40px;
    background-color: #ffffff;

    &:hover {
      background-color: #ffffff;
    }
  }

  a.perks-button,
  a.buy-button,
  a.compare-brokers-button {
    width: 100%;
    background: ${({ $variant }) => ($variant === 'simple' ? `#f0f3fa` : `#3f83f81a`)};
    border: ${({ $variant }) => ($variant === 'simple' ? `none` : ``)};
    box-shadow: unset;
  }

  .quote-actions-container {
    &.bottom-buttons {
      justify-content: center;
      display: flex;
      @media (max-width: 600px) {
        flex-direction: column;
      }
    }
  }

  .profile-call-to-action-container {
    .call-to-action-container {
      margin-top: 16px;
    }
  }

  .section-title {
    font-size: 24px;
    font-weight: 700;
    text-transform: uppercase;
  }

  .main-quote-tabs-wrapper {
    display: flex;
    ${({ $isStickyHeader, $navigationHeaderHeight, $topOffset }) =>
      $isStickyHeader &&
      `
        position: fixed;
        background: linear-gradient(#f3f9ff, #e1ebfa);
        top: ${$topOffset ? `${($navigationHeaderHeight || 242) + $topOffset}px` : `${$navigationHeaderHeight || 242}px`};
        left: 0;
        z-index: 99;
        width: 100%;
        @media (min-width: 800px) {
          top: ${
            $topOffset ? `${($navigationHeaderHeight || 202) + $topOffset}px` : `${$navigationHeaderHeight || 202}px`
          };
        }
    `}
    @media (min-width: 800px) {
      padding: 0 2rem;
    }
  }

  .main-quote-tabs {
    width: 100%;
    margin: 0 auto;
    max-width: 1280px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    a {
      color: unset;
    }

    .tab {
      font-size: 14px;
      font-weight: 700;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
      cursor: pointer;
      white-space: nowrap;
      color: ${({ $variant }) => ($variant === 'simple' ? `#000` : `#5b7292`)};
      padding: 0.8rem 1.3rem;

      @media (min-width: 800px) {
        padding: 0.5rem 1.2rem;
      }

      &.mobile-tab {
        padding: 12px 16px;
        height: 48px;
      }

      &:hover {
        color: ${({ $variant }) => ($variant === 'simple' ? `#093451` : `#3f83f8`)};
      }

      &.active {
        color: ${({ $variant }) => ($variant === 'simple' ? `#093451` : `#3f83f8`)};
        border-bottom: ${({ $variant }) => ($variant === 'simple' ? `2px solid #093451` : `2px solid #3f83f8`)};
      }
    }
  }

  .company-logo {
    height: 64px;
    width: 64px;
    min-width: 64px;
  }

  @media (min-width: 768px) {
    .header-left-text {
      font-size: 20px;
      line-height: 22px;
    }
    .leave-a-comment-wrapper {
      button {
        padding: 0.35rem 0.75rem;
      }
    }
  }
`;

const HeaderAdvertiserImage = styled.div<{ $url }>`
  background-image: ${({ $url }) => ($url ? `url(${$url})` : 'none')};
  height: 200px;
  background-position: top;
  background-size: cover;
  background-repeat: no-repeat;
`;
