import React from 'react';
import classNames from 'classnames';
import styled from '@benzinga/themetron';
import { useBenzingaEdge } from '@benzinga/edge';
import { ErrorBoundary, LoadingScreen } from '@benzinga/core-ui';
import { QuoteHeader, QuoteHeaderProps } from './QuoteHeader';
import { NoFirstRender } from '@benzinga/hooks';

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

const WNSTNWidgetQuote = React.lazy(() =>
  import('@benzinga/ads').then(module => ({ default: module.WNSTNWidgetQuote })),
);

interface QuoteLayoutProps extends QuoteHeaderProps {
  symbol: string;
  showGovLink?: boolean;
}

export const QuoteLayout: React.FC<React.PropsWithChildren<QuoteLayoutProps>> = ({
  advertiserProfile,
  children,
  defaultPath,
  isAdvertiser,
  isCrypto,
  profile,
  showGovLink,
  symbol,
}) => {
  const hasAdLight = useBenzingaEdge().adLightEnabled;
  return (
    <QuoteLayoutWrapper className={`quote-page-container ${defaultPath ?? 'profile'}`}>
      <QuoteHeader
        advertiserProfile={advertiserProfile}
        defaultPath={defaultPath}
        isAdvertiser={isAdvertiser}
        isCrypto={isCrypto}
        profile={profile}
        showGovLink={showGovLink}
        symbol={symbol}
      />
      {!hasAdLight ? (
        <div className="quote-page-above-layout-ads">
          <React.Suspense fallback={<div />}>
            <RaptiveAdPlaceholder onlyMobile={true} type="content-small" />
          </React.Suspense>
        </div>
      ) : null}
      <QuoteLayoutContent className={classNames('flex flex-col gap-y-8 w-full ml-auto py-4')}>
        <React.Suspense fallback={<LoadingScreen />}>{children}</React.Suspense>
      </QuoteLayoutContent>
      <ErrorBoundary name="floating-wnstn-widget">
        <NoFirstRender>
          <React.Suspense fallback={null}>
            <WNSTNWidgetQuote isCrypto={isCrypto} symbol={symbol} />
          </React.Suspense>
        </NoFirstRender>
      </ErrorBoundary>
    </QuoteLayoutWrapper>
  );
};

export const QuoteLayoutContent = styled.div``;

export const QuoteLayoutWrapper = styled.div`
  .section-description {
    color: #5b7292;
  }

  .divider-bottom {
    border-bottom: 1px solid #e1ebfa;
  }

  .top-area-and-sidebar-container {
    display: flex;
    flex-direction: column;
    @media screen and (min-width: 1020px) {
      flex-direction: row;
    }
  }

  .metrics-mobile {
    display: block;
  }

  .metrics-desktop {
    display: none;
  }

  @media screen and (min-width: 1020px) {
    .metrics-mobile {
      display: none;
    }
    .metrics-desktop {
      display: block;
    }
  }

  .quote-actions-container {
    &.bottom-buttons {
      justify-content: center;
    }
  }

  .video-card {
    cursor: pointer;
    img {
      width: 100%;
      aspect-ratio: 16/9;
    }
  }
  .profile-call-to-action-container {
    .call-to-action-container {
      margin-top: 16px;
    }
  }

  .inline-image-banner {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .top-tabs-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #f2f8ff;
    border-radius: 4px;
    padding: 4px;

    .top-tabs__tab {
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      color: #3f83f8;
      border: none !important;
      background: #f2f8ff;

      &:hover {
        color: #ffffff;
        background: #61a9ff;
      }
    }

    .top-tabs__tab--active {
      background: #3f83f8;
      color: white;
    }
  }

  .card {
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #ceddf2;

    &.technical-card {
      height: 164px;
    }
  }

  .simple-tab-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #f2f8ff;
    border-radius: 4px;
    padding: 4px;

    .simple-tab {
      cursor: pointer;
      padding: 2px 8px;
      border-radius: 4px;
      color: #3f83f8;
      &.active {
        background: #3f83f8;
        color: white;
      }

      .simple-tab-text {
        font-weight: 600;
      }
    }
  }

  .news-tab-wrapper {
    flex-direction: row;
    align-items: center;
    gap: 14px;
    background: #f2f8ff;
    border-radius: 4px;
    padding: 4px;

    /* This flex row is taking up 100% of the width for some reason, we dont want that */
    display: inline-flex;

    .simple-tab {
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 4px;
      color: #3f83f8;

      &.active {
        background: #3f83f8;
        color: white;
      }

      .simple-tab-text {
        font-weight: 600;
      }
    }
  }

  .faq-container .faq-block {
    border: none;

    .qa-container {
      padding: 0rem;
      margin-bottom: 0.5rem;
      border-radius: 0.5rem;
      border: 1px solid #ceddf2;

      .qa-header {
        color: #283d59;
        padding: 0.5rem;
        border-top-right-radius: 0.5rem;
        border-top-left-radius: 0.5rem;
      }

      .qa-body {
        color: #395173;
        padding: 0.5rem;
      }
    }
  }

  .bottom-buttons {
    margin-bottom: 12px;
  }

  .main-div {
    margin-left: auto;
    margin-right: auto;
    max-width: 1280px;
    width: 100%;
    padding: 0 1rem;
  }

  .solid-text {
    font-size: 14px;
    line-height: 17px;
    font-weight: 700;
  }

  .under-calendar-text {
    padding: 4px;
  }

  .quote-calendar {
    max-width: 1280px;
  }

  .server-side-calendar-container {
    display: flex;
    width: 100%;
    max-width: 1280px;
    overflow-x: auto;
  }

  /* AG Grid Styling */
  .ag-root-wrapper {
    .ag-header-cell {
      border-left: none !important;
      border-right: none !important;
    }

    .ag-header-cell {
      padding: 4px 6px !important;
      background: ${({ theme }) => theme.colorPalette.blue500} !important;

      .ag-header-cell-text {
        color: #ffffff;
        font-weight: 700;
      }
    }

    .ag-header-container {
      background: #e1ebfa !important;
    }

    .ag-header-viewport {
      background: ${({ theme }) => theme.colorPalette.blue500} !important;
    }

    .ag-icon-asc,
    .ag-icon-desc {
      color: #ffffff;
    }
  }

  .benzinga-core-table-container {
    .benzinga-core-table-thead tr {
      background: ${({ theme }) => theme.colorPalette.blue500};
      border-bottom-color: ${({ theme }) => theme.colorPalette.blue500};

      th {
        border-left: none;
      }

      th:first-of-type {
        border-left-color: ${({ theme }) => theme.colorPalette.blue500};
      }

      th:last-of-type {
        border-right-color: ${({ theme }) => theme.colorPalette.blue500};
      }
    }
    .benzinga-core-table-wrapper {
      border-color: rgb(224, 224, 224);
    }
  }
`;
