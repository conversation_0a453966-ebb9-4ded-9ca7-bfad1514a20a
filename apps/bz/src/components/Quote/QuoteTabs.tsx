'use client';
import React, { useCallback } from 'react';
import { QuoteProfile as QuoteProfileProps } from '../../entities/quoteEntity';
import { formatTickerToURLFriendly } from '@benzinga/utils';
import { Tabs, TabsInterface } from '@benzinga/core-ui';
import styled from '@benzinga/themetron';

export const QuotePageTabSlugs = [
  'profile',
  'analyst-ratings',
  'guidance',
  'earnings',
  'dividend',
  'news',
  'short-interest',
  'insider-trades',
  'key-statistics',
  'ideas',
];

interface QuoteTabsProps {
  activeTab: string;
  profile: QuoteProfileProps;
  symbol: string;
  title: string;
  description: string;
  tabContents?: {
    [key: string]: JSX.Element;
  };
}

const pad = (n, width, z) => {
  z = z || '0';
  n = n + '';
  return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
};

export const QuoteTabs: React.FC<React.PropsWithChildren<QuoteTabsProps>> = ({
  activeTab,
  children,
  description,
  profile,
  symbol,
  title,
}) => {
  const [currentTab, setCurrentTab] = React.useState(activeTab);

  const onChange = (key: string) => {
    const targetTabSettings = isTargetTabExternalLink(key);
    if (!targetTabSettings) {
      setCurrentTab(key);
    }
  };

  const getTabUrl = (tabSlug: string) => {
    const ticker = symbol;
    return `/quote/${formatTickerToURLFriendly(ticker)}/${tabSlug}`;
  };

  const cik = pad(profile.fundamentals?.company?.cik, 10, '0');

  const profileTab = {
    component: children,
    key: 'profile',
    link: `/quote/${formatTickerToURLFriendly(symbol)}`,
    name: 'Profile',
  };

  const analystRatingsTab = {
    component: children,
    key: 'analyst-ratings',
    link: getTabUrl('analyst-ratings'),
    name: 'Analyst Ratings',
  };

  const guidanceTab = {
    component: children,
    key: 'guidance',
    link: getTabUrl('guidance'),
    name: 'Guidance',
  };

  const dividendsTab = {
    component: children,
    key: 'dividend',
    link: getTabUrl('dividend'),
    name: 'Dividends',
  };

  const earningsTab = {
    component: children,
    key: 'earnings',
    link: getTabUrl('earnings'),
    name: 'Earnings',
  };

  const newsTab = {
    component: children,
    key: 'news',
    link: getTabUrl('news'),
    name: 'News',
  };

  const insiderTradesTab = {
    component: children,
    key: 'insider-trades',
    link: getTabUrl('insider-trades'),
    name: 'Insider Trades',
  };

  const tradeIdeasTab = {
    component: children,
    key: 'ideas',
    link: getTabUrl('ideas'),
    name: 'Ideas',
  };

  const keyStatsTab = {
    component: children,
    key: 'key-statistics',
    link: getTabUrl('key-statistics'),
    name: 'Key Statistics',
  };

  const shortInterestTab = {
    component: children,
    key: 'short-interest',
    link: getTabUrl('short-interest'),
    name: 'Short Interest',
  };

  const getTabs = useCallback((): TabsInterface[] => {
    let tabs: TabsInterface[] = [];

    if (profile?.richQuoteData?.type === 'ETF') {
      // tabs = [profileTab, newsTab, dividendsTab, tradeIdeasTab, keyStatsTab];
      tabs = [profileTab, newsTab, dividendsTab, tradeIdeasTab];
    } else {
      tabs = [
        profileTab,
        newsTab,
        analystRatingsTab,
        guidanceTab,
        dividendsTab,
        earningsTab,
        insiderTradesTab,
        tradeIdeasTab,
        // keyStatsTab,
      ];
    }
    if (profile.shortInterest && profile.shortInterest.length) {
      tabs.push(shortInterestTab);
    }
    return tabs;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [symbol, profile.shortInterest, title, description]);

  const tabs = getTabs();

  const renderTabs = (): TabsInterface[] => {
    if (Array.isArray(!tabs)) {
      return [];
    }

    return tabs
      .map(item => {
        if (item.component) {
          const link = item.link ? (item.key === 'profile' ? `/quote/${symbol}` : getTabUrl(item.key)) : null;
          return {
            ...item,
            link: item.externalLink ? null : link,
          };
        } else if (item?.externalLink) {
          return {
            ...item,
            link: item.externalLink,
          };
        }
        return null;
      })
      .filter(item => !!item) as TabsInterface[];
  };

  return (
    <Container className="quote-tabs">
      <Tabs
        activeTab={currentTab}
        enableLazyLoad={true}
        isShowMoreButtonEnabled={true}
        onChange={onChange}
        tabListWrapperClassname="wrapper"
        tabs={renderTabs()}
      />
    </Container>
  );
};

const isTargetTabExternalLink = (key: string) => {
  return document.querySelector(`[data-tab-link-external=${key}]`) as HTMLElement;
};

const Container = styled.div`
  &.quote-tabs {
    > .tabs-container {
      margin-bottom: 0.5rem;
      margin-top: 1rem;

      > .tab-list-wrapper {
        background-color: #f8fafc;

        .tab-list {
          display: flex;

          button.tab {
            padding: 6px 10px;
            margin-right: 12px;

            &:last-of-type {
              margin-right: 0;
            }
          }

          button.tab {
            color: ${({ theme }) => theme.colorPalette.blue500};
            font-weight: ${({ theme }) => theme.fontWeight.bold};
            font-size: ${({ theme }) => theme.fontSize.base};
            text-transform: uppercase;
            border-bottom-width: 2px;
            border-bottom-color: transparent;

            &.active {
              color: ${({ theme }) => theme.colorPalette.gray600};
              border-bottom: 1px solid ${({ theme }) => theme.colorPalette.blue500};
              border-bottom-width: 2px;
            }
          }
        }
      }
    }
  }
`;
