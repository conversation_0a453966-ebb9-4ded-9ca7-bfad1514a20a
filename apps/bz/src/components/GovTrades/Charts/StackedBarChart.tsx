import React from 'react';

const ReactECharts = React.lazy(() => import('echarts-for-react'));

export const StackedBarChart = ({ data }) => {
  const months = data?.data?.map(item => {
    return item.month.slice(0, 3) + ' ' + item.year;
  });

  const buys = data?.data?.map(item => {
    return item.bought.amount;
  });

  const sells = data?.data?.map(item => {
    return item.sold.amount;
  });

  const options = React.useMemo(() => {
    return {
      grid: {
        bottom: '3%',
        containLabel: true,
        left: '0%',
        right: '8%',
      },
      legend: {
        right: 50,
      },
      series: [
        {
          color: '#56d288',
          data: buys,
          emphasis: {
            focus: 'series',
          },
          name: 'Buy',
          roundCap: true,
          stack: 'Ad',
          type: 'bar',
        },
        {
          color: '#f85656',
          data: sells,
          emphasis: {
            focus: 'series',
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
          },
          name: 'Sell',
          stack: 'Ad',
          type: 'bar',
        },
      ],
      tooltip: {
        axisPointer: {
          type: 'shadow',
        },
        trigger: 'axis',
      },
      xAxis: [
        {
          axisLabel: {
            formatter: val => val.slice(0, 3),
          },
          data: months,
          type: 'category',
        },
      ],
      yAxis: [
        {
          max: 'dataMax',
          min: 'dataMin',
        },
      ],
    };
  }, [buys, months, sells]);

  return <ReactECharts notMerge={true} option={options} />;
};
