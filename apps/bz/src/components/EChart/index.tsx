import { memo, LegacyRef } from 'react';
import ReactEcharts from 'echarts-for-react';
import type { EChartsOption } from 'echarts';

const EChart = ({ chartRef, customOptions }: { chartRef?: LegacyRef<ReactEcharts>; customOptions?: EChartsOption }) => {
  return (
    <div className="h-full w-full">
      <ReactEcharts
        option={customOptions}
        ref={chartRef}
        style={{
          height: '100%',
          width: '100%',
        }}
      />
    </div>
  );
};

export default memo(EChart);
