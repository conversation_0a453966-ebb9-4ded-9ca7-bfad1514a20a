'use client';
import React from 'react';
import VizSensor from 'react-visibility-sensor';
import { isGlobalImpressionStored, storeGlobalImpression } from '@benzinga/content-manager';

import styled from '@benzinga/themetron';
import { Pagination, Collapse, Divider, Checkbox, CheckboxGroup } from '@benzinga/core-ui';

import { Hr } from './utils/styletron';
import ContentHeadline from './ContentHeadline';
import { NoResults } from '@benzinga/core-ui';
import { NodeQueryParams } from '@benzinga/basic-news-manager';
import { getQuoteNews, GroupedNewsI, splitQuoteNewsByDate } from '../../pages/api/quote';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';

const channels = [
  { label: 'General', value: 18467 },
  { label: 'Contracts', value: 87 },
  { label: 'Dividends', value: 41 },
  // { label: 'Dividends', value: 61 },
  { label: 'Events', value: 29618 },
  { label: 'FDA', value: 59 },
  { label: 'M&A', value: 64 },
  { label: 'Offerings', value: 65 },
  { label: 'Stock Split', value: 66 },
  { label: 'Media', value: 5 },
  { label: 'Buybacks', value: 63 },
  { label: 'Insider Trades', value: 62 },
  { label: 'Earnings', value: 16888 },
  { label: 'Guidance', value: 16889 },
  { label: 'Analyst Ratings', value: 67 },
  { label: 'Trading Ideas', value: 22 },
];

const News: React.FC<{ initialNews: GroupedNewsI[]; symbol: string }> = ({ initialNews, symbol }) => {
  const [news, setNews] = React.useState<GroupedNewsI[]>(initialNews ?? []);
  const [page, setPage] = React.useState(0);
  const [checkAll, setCheckAll] = React.useState(true);
  const channelNames: string[] = channels.map(channel => channel.label);
  const [selectedChannels, setSelectedChannels] = React.useState(channelNames);

  const session = React.useContext(SessionContext);

  const handleFetchQuoteNews = React.useCallback((query: NodeQueryParams) => {
    getQuoteNews(session.getSession(), query).then(res => {
      setNews(splitQuoteNewsByDate(res));
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOnChangeOption = React.useCallback(
    (list: string[]) => {
      if (checkAll) {
        channels?.filter(channel => {
          if (!list.includes(channel.label)) {
            setSelectedChannels([channel.label]);
            const newQuery = { channels: [channel.value.toString()], page, symbols: [symbol] };
            handleFetchQuoteNews(newQuery);
            return channel;
          }
          return false;
        });
        setCheckAll(false);
        return;
      } else if (!checkAll && list.length === 0) {
        setSelectedChannels(channelNames);
        setCheckAll(true);
      } else {
        setSelectedChannels(list);
        setCheckAll(list.length === channelNames.length);
      }

      const channelIds = channels
        .filter(channel => list.includes(channel.label))
        .map(channel => channel.value.toString());

      const allChannelsSelected = channelIds.length === channels.length;
      if (allChannelsSelected) {
        const newQuery = { page, symbols: [symbol] };
        handleFetchQuoteNews(newQuery);
      } else {
        const newQuery = { channels: channelIds, page, symbols: [symbol] };
        handleFetchQuoteNews(newQuery);
      }
    },
    [channelNames, checkAll, handleFetchQuoteNews, page, symbol],
  );

  const handleOnChangeCheckAll = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSelectedChannels(e.target.checked ? channelNames : []);
      setCheckAll(e.target.checked);
      const newQuery = { page, symbols: [symbol] };
      handleFetchQuoteNews(newQuery);
    },
    [channelNames, handleFetchQuoteNews, page, symbol],
  );

  const handleOnChangePagination = React.useCallback(
    data => {
      const queryPage = data.nextPage - 1;
      setPage(data.nextPage);

      const channelIds = channels
        .filter(channel => selectedChannels.includes(channel.label))
        .map(channel => channel.value.toString());

      const allChannelsSelected = channelIds.length === channels.length;
      if (allChannelsSelected) {
        const newQuery = { page: queryPage, symbols: [symbol] };
        handleFetchQuoteNews(newQuery);
      } else {
        const newQuery = { channels: channelIds, page: queryPage, symbols: [symbol] };
        handleFetchQuoteNews(newQuery);
      }
    },
    [handleFetchQuoteNews, selectedChannels, symbol],
  );

  const collapseHeader = checkAll
    ? 'Categories: All'
    : `Categories: ${selectedChannels && selectedChannels.length > 0 && selectedChannels.join(', ')}`;

  const CheckboxContainer = () => (
    <div className="channels-checkbox-container">
      <Checkbox
        checked={checkAll}
        disabled={checkAll}
        label="All News"
        onChange={handleOnChangeCheckAll}
        style={{ fontWeight: 600 }}
      />
      <Divider thickness="thin" />
      <CheckboxGroup onChange={handleOnChangeOption} options={channelNames} selected={selectedChannels} />
    </div>
  );

  const registerImpression = ({ article, isVisible }) => {
    const sponsored = article?.meta?.Flags?.ShowAdvertiserDisclosure;
    if (sponsored && isVisible && !isGlobalImpressionStored(`sponsored-item-${article?.nodeId}`)) {
      storeGlobalImpression(`sponsored-item-${article?.nodeId}`);
      session.getManager(TrackingManager).trackCampaignEvent('view', {
        partner_id: (article?.meta?.Reach?.Disclosure?.tid || 0).toString(),
        unit_type: `sponsored-item-${article?.nodeId}`,
      });
    }
  };

  return (
    <Container className="news-container">
      <div>
        <div className="collapse-wrapper">
          <Collapse defaultCollapsed={true} header={collapseHeader}>
            <CheckboxContainer />
          </Collapse>
        </div>
        <div className="desktop">
          <CheckboxContainer />
        </div>
        <div className="news-content">
          {news.map(group => (
            <div key={group.date}>
              <div className="date-heading flex justify-between items-center">
                <h2 className="underline text-xl">{group.date}</h2>
                <Hr className="date-heading-hr flex-grow border-b relative" />
              </div>
              <ul>
                {group.articles.map(article => (
                  <li key={article.id}>
                    <VizSensor onChange={isVisible => registerImpression({ article, isVisible })}>
                      <ContentHeadline content={article} key={article.id} />
                    </VizSensor>
                  </li>
                ))}
              </ul>
            </div>
          ))}
          {news.length === 0 && <NoResults title={`There are no results`} />}
          {news.length > 0 && (
            <Pagination onPageChanged={handleOnChangePagination} pageNeighbors={2} totalItems={500} />
          )}
        </div>
      </div>
    </Container>
  );
};

export default News;

const Container = styled.div`
  &.news-container {
    margin-left: auto;
    margin-right: auto;
    padding-top: 10px;
    padding: 10px;
    width: 100%;

    > div {
      display: flex;
      flex-direction: column;

      .collapse-wrapper {
        margin-bottom: 10px;
        border: 1px solid ${({ theme }) => theme.colors.border};

        .collapse-panel-header {
          display: flex;
          align-items: center;
          font-weight: 700;
          padding: 10px 20px;
        }
      }
    }

    .desktop {
      display: none;
    }

    .channels-checkbox-container {
      flex-direction: column;
      margin-right: 20px;
      width: 100%;
      height: fit-content;
      background-color: ${({ theme }) => theme.colors.backgroundActive};
      padding: 10px;

      .checkbox-group-container {
        white-space: nowrap;
      }
    }

    .news-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      > div {
        margin-bottom: 20px;

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }

    .pagination-container {
      margin-top: auto;
    }

    @media screen and (min-width: 650px) {
      > div {
        display: flex;
        flex-direction: row;
      }

      .collapse-wrapper {
        display: none;
      }

      .desktop {
        display: block;
        .channels-checkbox-container {
          display: flex;
          width: 150px;
          min-width: 150px;
          margin-bottom: 15px;
          border: 1px solid ${({ theme }) => theme.colors.border};
        }
      }
    }
  }
`;
