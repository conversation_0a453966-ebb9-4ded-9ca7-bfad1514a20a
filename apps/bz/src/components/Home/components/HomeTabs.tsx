import React, { ReactNode } from 'react';
import styled from '@benzinga/themetron';
import { Tabs } from '@benzinga/core-ui';
import { HomePageTabKey, homePageTabs, PageTabsT } from '../interface';
import { isMobile } from '@benzinga/device-utils';
import Hooks from '@benzinga/hooks';

type TabIdentification = string;

interface Tab {
  title: string;
  slug: TabIdentification;
  url: string;
}

interface Props {
  tabs?: Tab[];
  activeTab: HomePageTabKey;
  tabContent: React.ReactNode;
}

export const HOME_PAGE_TABS: homePageTabs = {
  BRIEFS: 'briefs',
  EXCLUSIVES: 'exclusives',
  OFFER: 'offer',
  RECENT: 'recent',
  STOCKIDEAS: 'stockideas',
  TOOLSIDEA: 'toolsidea',
  TRENDING: 'trending',
  WATCH: 'watch',
};

export const pageTabs: PageTabsT = {
  [HOME_PAGE_TABS.TRENDING]: {
    clientSide: false,
    loaded: false,
    title: 'Trending',
    url: '/',
  },
  [HOME_PAGE_TABS.RECENT]: {
    clientSide: true,
    loaded: false,
    title: 'Latest News',
    url: '/recent',
  },
  [HOME_PAGE_TABS.STOCKIDEAS]: {
    clientSide: true,
    loaded: false,
    title: 'Stock Ideas',
    url: '/stock-ideas',
  },
  [HOME_PAGE_TABS.BRIEFS]: {
    clientSide: true,
    loaded: false,
    title: 'Briefs',
    url: '/briefs',
  },
  [HOME_PAGE_TABS.EXCLUSIVES]: {
    clientSide: true,
    loaded: false,
    title: 'Exclusives',
    url: '/exclusives',
  },
  [HOME_PAGE_TABS.WATCH]: {
    clientSide: true,
    loaded: false,
    title: 'Benzinga TV',
    url: '/watch',
  },
  // [HOME_PAGE_TABS.TOOLSIDEA]: {
  //   title: 'Premium Tools & Ideas',
  //   shouldReload: true,
  //   clientSide: true,
  //   loaded: false,
  //   url: 'https://www.benzinga.com/premium-products?utm_source=benzigna-homepage&utm_medium=benzinga-homepage'
  // },
};

export const HomeTabs: React.FC<Props> = ({ activeTab, tabContent }) => {
  const isMobileHydrated = Hooks.useHydrate(isMobile(), false);

  const handleTabRouting = React.useCallback((tabKey: string) => {
    const tab = pageTabs[tabKey];
    if (tab) {
      if (tab.openInNewTab) {
        window.open(tab.url, '_blank');
      } else {
        window.location.href = tab.url;
      }
    }
  }, []);

  const renderTabs = React.useCallback(() => {
    const tabs: { component: ReactNode; key: HomePageTabKey; name: string }[] = [];

    for (const tabKey in pageTabs) {
      tabs.push({
        component: activeTab === tabKey ? tabContent : null,
        key: tabKey as HomePageTabKey,
        name: pageTabs[tabKey].title,
      });
    }
    return tabs;
  }, [activeTab, tabContent]);

  return (
    <HomeTabsWrapper>
      <Tabs
        activeTab={activeTab}
        isShowMoreButtonEnabled={!isMobileHydrated}
        onChange={handleTabRouting}
        tabs={renderTabs()}
        variant="lifted"
      />
    </HomeTabsWrapper>
  );
};

export const HomeTabsWrapper = styled.div`
  @media screen and (max-width: 800px) {
    margin-top: 0rem;
  }

  .tabs-container.lifted-variant {
    .tab-list-wrapper {
      border-bottom: 1px solid ${({ theme }) => theme.colorPalette.gray600};
      margin-bottom: 1rem;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .tab-list-wrapper {
      .tab-list {
        margin-bottom: -1px;

        button.tab {
          color: ${({ theme }) => theme.colorPalette.blue500};
          font-size: ${({ theme }) => theme.fontSize.xl};
          font-weight: ${({ theme }) => theme.fontWeight.bold};
          padding: 4px 20px;
          text-align: center;

          &.active {
            background-color: transparent;
            border: 1px solid ${({ theme }) => theme.colorPalette.gray600};
            border-bottom-color: ${({ theme }) => theme.colorPalette.white};
            color: ${({ theme }) => theme.colors.foregroundActive};

            &:hover {
              border-bottom-color: ${({ theme }) => theme.colorPalette.white};
            }
          }

          @media screen and (max-width: 800px) {
            padding: 4px 12px;
            border: none !important;
            min-width: auto;
            &.active {
              border-bottom: solid 3px #3f82f8;
            }
          }
        }

        button.tab:first-child {
          min-width: 120px;
        }
        button.tab:nth-child(2) {
          min-width: 147px;
        }
        button.tab:nth-child(3) {
          min-width: 141px;
        }
        button.tab:nth-child(4) {
          min-width: 91px;
        }
        button.tab:nth-child(5) {
          min-width: 132px;
        }
        button.tab:nth-child(6) {
          min-width: 147px;
        }
      }
    }
  }
`;
