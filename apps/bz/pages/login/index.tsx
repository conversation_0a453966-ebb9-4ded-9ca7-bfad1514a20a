import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import styled from '@benzinga/themetron';
import { AuthContainer } from '@benzinga/auth-ui';
import { Button } from '@benzinga/core-ui';
import { SessionContext } from '@benzinga/session-context';
import { SubscriptionsManager } from '@benzinga/subscription-manager';
import { useIsUserLoggedIn, usePermission } from '@benzinga/user-context';
import { AuthMode } from '@benzinga/session';
import { getLanguageCodeByHost, setLanguageByHost, translate } from '@benzinga/translate';

interface LoginPageProps {
  referrer: string | null;
  url: string;
}
export type LoginRegisterEventsTypes =
  | 'close'
  | 'already_logged_in'
  | 'login_succeed'
  | 'login_failed'
  | 'register_succeed'
  | 'start_trial_succeed'
  | 'register_failed';

const LoginPage: React.FC<LoginPageProps> = ({ referrer }) => {
  const session = React.useContext(SessionContext);
  const isLoggedIn = useIsUserLoggedIn();

  const [subscriptions, setSubscriptions] = React.useState<{ link: string; name: string }[] | null>(null);
  const isEditor = usePermission('editorial/edit');
  const isBenzingaContributor = usePermission('cms/role');
  const isAdmin = usePermission('#', '#');
  const router = useRouter();
  const authMode: AuthMode | string | string[] = router.query?.action ?? 'login';

  const redirectNextParam = router?.query?.next || router?.query?.redirect;
  const redirectNext = Array.isArray(redirectNextParam) ? redirectNextParam[0] : redirectNextParam;

  if (isEditor && typeof window !== 'undefined' && window.localStorage) {
    window.localStorage.setItem('editorial/edit', '1');
  }

  if (isBenzingaContributor && typeof window !== 'undefined' && window.localStorage) {
    window.localStorage.setItem('cms/role_contributor', '1');
  }

  // benzinga-newsletter, benzinga-premium
  const loadSubscriptions = React.useCallback(() => {
    const subs: { link: string; name: string }[] = [];
    // This does not load subscriptions because for some reason they not loaded on login
    const subscriptions = session.getManager(SubscriptionsManager).getSubscriptionsStored();
    if (subscriptions?.length) {
      if (subscriptions.filter(sub => sub.product === 'benzinga-pro')) {
        subs.push({ link: 'https://pro.benzinga.com/dashboard/', name: 'Benzinga Pro' });
      } else {
        subs.push({ link: 'https://www.benzinga.com/pro/register?utm=login_list', name: 'Benzinga Pro Trial' });
      }
      subs.push({ link: 'https://www.benzinga.com/research', name: 'Benzinga Research' });
    }
    if (isEditor) {
      subs.push({ link: 'https://www.benzinga.com/admin/bz/editor/queue', name: 'Benzinga Admin/Editorial' });
      subs.push({ link: 'https://www.benzinga.com/node/add/story', name: 'Create Story' });
    }
    if (isAdmin) {
      subs.push({ link: 'https://www.benzinga.com/admin/bz/api', name: 'API Admin' });
    }
    setSubscriptions(subs);
    return subs;
  }, [isEditor, isAdmin, session]);

  const handleGoToPortfolio = React.useCallback(() => {
    window.location.href = '/profile/portfolio/';
  }, []);

  const handleGoToSubscription = React.useCallback(subscription => {
    if (subscription.link) {
      window.location.href = subscription.link;
    }
  }, []);

  useEffect(() => {
    if (isLoggedIn) {
      (async () => {
        await loadSubscriptions();
      })();
    }
  }, [isLoggedIn, loadSubscriptions]);

  return (
    <>
      <PageWrapper>
        {subscriptions?.length ? (
          <SubscriptionBox>
            <h2>Access Your Subscription</h2>
            <div className="p-2">
              <Button as="a" className="w-full" href="https://www.benzinga.com">
                Benzinga.com
              </Button>
            </div>
            {subscriptions.map(subscription => {
              return (
                <div className="p-2" key={subscription?.name}>
                  <Button className="w-full" onClick={() => handleGoToSubscription(subscription)} variant="primary">
                    {subscription?.name}
                  </Button>
                </div>
              );
            })}
            <div className="p-2">
              <Button className="w-full" onClick={handleGoToPortfolio} variant="default">
                Go To My Portfolio
              </Button>
            </div>
          </SubscriptionBox>
        ) : (
          <AuthBox>
            <AuthContainer authMode={authMode} nextUrl={referrer ?? redirectNext ?? undefined} placement="login" />
          </AuthBox>
        )}
      </PageWrapper>
    </>
  );
};

export const PageWrapper = styled.div`
  min-height: 100vh;
  background-color: ${({ theme }) => theme.colors.backgroundInactive};
  padding: 3vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media (max-width: 768px) {
    padding: 0;
    background-color: ${({ theme }) => theme.colorPalette.white};

    & .benzinga-logo {
      max-width: 150px;
    }
  }
`;

export const CardBox = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.white};
  border: solid 1px ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  position: relative;
  padding: 0.75rem;
  box-shadow: ${({ theme }) => theme.shadow.md};
  margin: auto;
  @media (max-width: 768px) {
    box-shadow: none;
    border: none;
  }
`;

export const AuthBox = styled('div')`
  width: 80%;
  margin: 0px auto;
  display: flex;
  flex-direction: column;
  position: relative;
  justify-content: center;
  align-items: center;
  min-height: 90vh !important;
  @media (max-width: 768px) {
    width: 100%;
  }
`;

export const SubscriptionBox = styled(CardBox)`
  max-width: 312px;
  h2 {
    text-align: center;
  }
`;

export async function getServerSideProps({ isServer, req }) {
  const host = req.headers.host;
  const translations = await setLanguageByHost(host, ['common', 'auth']); // set language for server side translations
  const language = getLanguageCodeByHost(host);

  const url = isServer ? `https://${host}/login` : '';
  return {
    props: {
      disablePushPrompt: true,
      headerProps: {
        hideBanner: true,
        hideFooter: true,
        hideNavigationBar: true,
        hideQuoteBar: true,
      },
      metaProps: {
        canonical: `https://${host}/login`,
        description: translate('Auth.Meta.login-to-access-features', { ns: 'auth' }),
        host,
        language,
        title: translate('Auth.Meta.login-to-benzinga', { ns: 'auth' }),
        translations,
      },
      referrer: req.headers.referer ?? language !== 'en' ? `https://${host}/` : null,
      url: url,
    },
  };
}

export default LoginPage;
