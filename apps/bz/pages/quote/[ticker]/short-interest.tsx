import React from 'react';
import { DFPManager } from 'react-dfp';

import { QuotePageContent, metaInfo, getPageTitle } from './index';

import { getQuoteData } from '../../api/quote';

import QuoteTab from '../../../src/components/Quote/QuoteTab';
import QuoteCalendar from '../../../src/components/Quote/QuoteCalendar';
import { ShortInterestSummary, getShortInterestSummary } from '../../api/quote/[ticker]/short-interest';

import { StatBox, StatBoxesContainer } from '@benzinga/core-ui';
import { formatLarge, formatPercentage } from '@benzinga/utils';
import Link from 'next/link';

const ShortInterestSummaryBox: React.FC<{ shortInterestSummary: ShortInterestSummary }> = ({
  shortInterestSummary,
}) => {
  const shortInterest = shortInterestSummary?.last ?? null;
  return (
    <>
      <StatBoxesContainer>
        {shortInterest && (
          <StatBox
            title="Short Interest"
            value={shortInterest.totalShortInterest ? formatLarge(Number(shortInterest.totalShortInterest)) : '–'}
          />
        )}
        {shortInterest && (
          <StatBox
            title="Short Interest %"
            value={shortInterest.shortPercentOfFloat ? `${formatPercentage(shortInterest.shortPercentOfFloat)}%` : '–'}
          />
        )}
        {shortInterest && <StatBox title="Days to Cover" value={`${shortInterest.daysToCover}` ?? '–'} />}
      </StatBoxesContainer>
    </>
  );
};

const QuoteShortInterestPage = ({ activeTab, profile, shortInterestSummary, sidebar, symbol }) => {
  DFPManager.setTargetingArguments({ BZ_CHANNEL: 'Short Interest' });

  const meta = metaInfo(profile, activeTab);

  return (
    <QuotePageContent activeTab={activeTab} profile={profile} sidebar={sidebar} symbol={symbol} tabKey={activeTab}>
      <QuoteTab description={meta.description} title={getPageTitle(profile, activeTab)}>
        <div className="px-2">
          <ShortInterestSummaryBox shortInterestSummary={shortInterestSummary} />
          <QuoteCalendar
            calendar="short-interest"
            initialData={shortInterestSummary?.shortInterest ?? []}
            symbol={symbol}
          />
          <p className="text-sm">
            {/* eslint-disable-next-line @next/next/no-html-link-for-pages */}
            Looking for the <a href="/short-interest/most-shorted">most shorted stocks</a>?
          </p>
        </div>
      </QuoteTab>
    </QuotePageContent>
  );
};

export default QuoteShortInterestPage;

export const getServerSideProps = async ({ query: { ticker } }) => {
  try {
    const symbol = ticker?.toUpperCase();
    const activeTab = 'short-interest';

    const profile = await getQuoteData(symbol);
    const shortInterestSummary = await getShortInterestSummary(symbol);

    if (profile.notFound === true || shortInterestSummary === null) {
      return { notFound: true };
    }

    const headerProps = {};
    const sidebar = null;

    const metaProps = metaInfo(profile, activeTab);

    if (!shortInterestSummary?.shortInterest || shortInterestSummary?.shortInterest?.length < 10) {
      metaProps.robots = 'noindex, nofollow';
    }

    return {
      props: {
        activeTab: activeTab,
        headerProps: headerProps,
        metaProps,
        profile: profile,
        shortInterestSummary,
        sidebar: sidebar,
        symbol: symbol,
      },
    };
  } catch (e) {
    return { notFound: true };
  }
};
